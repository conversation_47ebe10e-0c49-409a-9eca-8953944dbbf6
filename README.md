# PyADB - Pure Python ADB Implementation

A pure Python implementation of the Android Debug Bridge (ADB) protocol for communicating with Android devices over USB and TCP/IP connections.

## Features

- **Pure Python**: No external ADB binary required
- **Multiple Transports**: Support for both USB and TCP/IP connections
- **Shell Commands**: Execute shell commands with full protocol support
- **Modern Protocol**: Support for shell_v2, cmd, and other modern ADB features
- **Streaming**: Real-time command output streaming
- **Device Management**: Device discovery and information retrieval

## Installation

### Basic Installation
```bash
# Clone or copy the pyadb directory to your project
# No external dependencies required for TCP/IP connections
```

### USB Support (Optional)
For USB device support, install PyUSB:
```bash
pip install pyusb
```

## Quick Start

### TCP/IP Connection
```python
from pyadb import AdbTcpConnection, AdbShellService

# Connect to device via TCP/IP (wireless debugging)
with AdbTcpConnection('*************', 5555) as conn:
    shell = AdbShellService(conn)
    
    # Execute a command
    result = shell.execute_command('ls -la')
    print(result.stdout.decode())
    print(f"Exit code: {result.exit_code}")
    
    # Get device information
    info = shell.get_device_info()
    print(f"Device: {info['model']} running Android {info['android_version']}")
```

### USB Connection
```python
from pyadb import find_usb_devices, AdbUsbConnection, AdbShellService

# Find connected USB devices
devices = find_usb_devices()
if devices:
    print(f"Found {len(devices)} devices:")
    for device in devices:
        print(f"  - {device}")

    # Connect to first device
    with AdbUsbConnection(devices[0]) as conn:
        shell = AdbShellService(conn)

        # List installed packages
        packages = shell.list_packages()
        print(f"Installed packages: {len(packages)}")
```

### File Transfer Operations
```python
from pyadb import AdbTcpConnection, AdbPullService, AdbPushService

with AdbTcpConnection('*************', 5555) as conn:
    # Pull a file from device
    pull_service = AdbPullService(conn)
    pull_service.pull_file('/system/build.prop', 'local_build.prop')

    # Push a file to device
    push_service = AdbPushService(conn)
    push_service.push_file('local_file.txt', '/data/local/tmp/remote_file.txt')
```

### Root Access Management
```python
from pyadb import AdbTcpConnection, AdbRootService

with AdbTcpConnection('localhost', 5555) as conn:  # Emulator
    root_service = AdbRootService(conn)

    # Check current root status
    is_root = root_service.check_root_status()
    print(f"Currently root: {is_root}")

    # Enable root (works on debuggable builds)
    if root_service.enable_root():
        print("Root access enabled")
```

### System Remount
```python
from pyadb import AdbTcpConnection, AdbRemountService

with AdbTcpConnection('localhost', 5555) as conn:  # Emulator
    remount_service = AdbRemountService(conn)

    # Check if system is writable
    is_writable = remount_service.is_system_writable()
    print(f"System writable: {is_writable}")

    # Remount system as read-write (requires root)
    if remount_service.remount_system():
        print("System remounted as read-write")
```

### Streaming Command Output
```python
def on_stdout(data):
    print(data.decode(), end='')

def on_stderr(data):
    print(data.decode(), file=sys.stderr, end='')

# Execute with real-time output
exit_code = shell.execute_command_streaming(
    'logcat -d',
    stdout_callback=on_stdout,
    stderr_callback=on_stderr
)
```

## API Reference

### Core Classes

#### `AdbTcpConnection(host, port, timeout)`
TCP/IP connection to ADB daemon.
- `host`: Target IP address or hostname
- `port`: Target port (default: 5555)
- `timeout`: Connection timeout in seconds

#### `AdbUsbConnection(device, timeout)`
USB connection to ADB-enabled device.
- `device`: UsbDevice object (None for auto-detect)
- `timeout`: USB transfer timeout in milliseconds

#### `AdbShellService(connection, features)`
Shell command execution service.
- `connection`: ADB connection object
- `features`: List of supported protocol features

### Key Methods

#### Shell Service
- `execute_command(command, timeout)`: Execute command and return result
- `execute_command_streaming(command, stdout_callback, stderr_callback)`: Execute with streaming
- `get_device_info()`: Get device information dictionary
- `get_properties()`: Get Android system properties
- `list_packages(system_apps)`: List installed packages
- `is_root_available()`: Check if root access is available

#### Connection Management
- `connect()`: Establish connection
- `disconnect()`: Close connection
- `is_connected()`: Check connection status
- `send_packet(packet)`: Send ADB protocol packet
- `receive_packet()`: Receive ADB protocol packet

### Utility Functions
- `find_usb_devices()`: Discover ADB-enabled USB devices
- `create_tcp_connection(host, port)`: Create TCP connection
- `create_usb_connection(device)`: Create USB connection

## Protocol Support

PyADB implements the full ADB protocol including:

- **Core Protocol**: SYNC, CNXN, OPEN, OKAY, CLSE, WRTE, AUTH commands
- **Shell Protocol**: Both legacy and shell_v2 with separate stdout/stderr
- **Feature Negotiation**: Automatic detection of device capabilities
- **Authentication**: Support for ADB authentication (when implemented)
- **Error Handling**: Robust error detection and recovery

## Architecture

```
pyadb/
├── __init__.py      # Main package interface
├── const.py         # Protocol constants and enums
├── core.py          # Core protocol implementation
├── conn_wifi.py     # TCP/IP connection transport
├── conn_usb.py      # USB connection transport
└── shell.py         # Shell service implementation
```

## Requirements

- Python 3.7+
- PyUSB (optional, for USB support)

## Testing

Run the test suite:
```bash
python test_pyadb.py
```

## License

Apache License 2.0 - Based on the Android Open Source Project ADB implementation.

## Contributing

This implementation is based on the Android Open Source Project ADB source code and follows the same protocol specifications. Contributions are welcome!

## Limitations

- Authentication is not yet fully implemented
- Some advanced ADB features may not be supported
- USB support requires PyUSB and appropriate drivers
- Tested primarily on Android devices with USB debugging enabled

## Troubleshooting

### USB Issues
- Ensure USB debugging is enabled on the Android device
- Install appropriate USB drivers for your device
- Try running as administrator/root if permission issues occur

### TCP/IP Issues  
- Ensure wireless debugging is enabled on the device
- Check that the device and host are on the same network
- Verify the correct IP address and port (usually 5555)

### Connection Issues
- Check that no other ADB instances are running
- Verify device authorization if required
- Try restarting the ADB daemon on the device
