# PyADB WiFi Discovery and Pairing

This document describes the WiFi discovery and pairing functionality in PyADB, which allows you to discover, pair with, and connect to Android devices over WiFi networks.

## Features

- **Device Discovery**: Automatically discover ADB devices on your local network using mDNS
- **Secure Pairing**: Pair with devices using password-based authentication
- **Key Management**: Automatic RSA key generation and management for device authentication
- **Device Registry**: Local storage of paired devices with connection history
- **TLS Support**: Secure connections using TLS encryption
- **High-level API**: Easy-to-use functions for common operations

## Prerequisites

1. **Python Dependencies**:
   ```bash
   pip install cryptography
   ```

2. **Android Device Setup**:
   - Enable Developer Options
   - Enable "Wireless debugging" or "WiFi debugging"
   - Ensure device is on the same network as your computer

## Quick Start

### 1. Discover Devices

```python
import pyadb

# Discover ADB devices on the network
devices = pyadb.discover_wifi_devices(timeout=10.0)

for device in devices:
    print(f"Found device: {device.name} at {device.ip_address}:{device.port}")
    print(f"  Paired: {device.is_paired}")
    print(f"  Can pair: {device.can_pair}")
```

### 2. Pair with a Device

```python
# Pair with a device using IP address and pairing code
result = pyadb.pair_wifi_device("*************", "123456")

if result.success:
    print(f"Successfully paired! Device GUID: {result.device_guid}")
else:
    print(f"Pairing failed: {result.error_message}")
```

### 3. Connect to a Paired Device

```python
# Connect to a paired device by GUID
connection = pyadb.connect_wifi_device("device-guid-here")

if connection:
    # Use the connection for ADB operations
    shell = pyadb.AdbShellService(connection)
    result = shell.execute_command("ls -la")
    print(result.stdout.decode())
    
    connection.disconnect()
```

### 4. Direct Connection by IP

```python
# Connect directly to a device by IP address
connection = pyadb.connect_wifi_address("*************", 5555)

if connection:
    # Use the connection
    shell = pyadb.AdbShellService(connection)
    result = shell.execute_command("getprop ro.build.version.release")
    print(f"Android version: {result.stdout.decode().strip()}")
    
    connection.disconnect()
```

## Advanced Usage

### Using the WiFi Manager

```python
from pyadb import get_wifi_manager

manager = get_wifi_manager()

# Start continuous discovery
manager.start_discovery()

# Find devices available for pairing
pairing_devices = manager.find_pairing_devices(timeout=5.0)

# Pair with a specific device
result = manager.pair_with_device("*************", "123456", device_name="My Phone")

# List all paired devices
paired_devices = manager.list_paired_devices()

# Connect with TLS encryption
connection = manager.connect_to_device("device-guid", use_tls=True)

# Stop discovery
manager.stop_discovery()
```

### Device Registry Operations

```python
from pyadb import get_device_registry

registry = get_device_registry()

# Get device information
device_info = registry.get_device("device-guid")
if device_info:
    print(f"Device: {device_info.name}")
    print(f"Last seen: {device_info.last_seen}")
    print(f"Connection count: {device_info.connection_count}")

# Remove a device
registry.remove_device("device-guid")

# Clean up old devices (not seen for 30 days)
removed_count = registry.cleanup_old_devices(max_age_days=30)
```

### Key Management

```python
from pyadb import get_key_manager

key_manager = get_key_manager()

# Get public key for sharing
public_key = key_manager.get_public_key_string()
print(f"Public key: {public_key}")

# Get key fingerprint
fingerprint = key_manager.get_key_fingerprint()
print(f"Key fingerprint: {fingerprint}")
```

## Command Line Interface

Use the included test script for command-line operations:

```bash
# Discover devices
python test_wifi_discovery.py discover

# Pair with a device
python test_wifi_discovery.py pair ************* 123456

# Connect to a paired device
python test_wifi_discovery.py connect device-guid-here

# List paired devices
python test_wifi_discovery.py list

# Run full demo
python test_wifi_discovery.py demo
```

## Android Device Setup

### Enable WiFi Debugging

1. **Enable Developer Options**:
   - Go to Settings > About phone
   - Tap "Build number" 7 times
   - Developer options will appear in Settings

2. **Enable Wireless Debugging**:
   - Go to Settings > Developer Options
   - Enable "Wireless debugging" or "WiFi debugging"
   - Note the port number (usually 5555)

3. **For Pairing**:
   - Tap "Pair device with pairing code"
   - Note the IP address and 6-digit pairing code
   - Use these with PyADB's pairing function

### Network Requirements

- Both computer and Android device must be on the same WiFi network
- Network should allow multicast traffic (for mDNS discovery)
- Firewall should allow connections to ADB ports (typically 5555, 37000-37001)

## Security Considerations

- **RSA Keys**: PyADB generates 2048-bit RSA keys for authentication
- **Key Storage**: Private keys are stored in `~/.android/adbkey` with restricted permissions
- **Device Registry**: Paired devices are stored in `~/.android/adb_known_hosts.json`
- **TLS Support**: Secure connections use TLS encryption when supported
- **Password-based Pairing**: Uses secure pairing protocol similar to official ADB

## Troubleshooting

### Discovery Issues

- **No devices found**: Check that WiFi debugging is enabled and devices are on the same network
- **mDNS not working**: Some networks block multicast traffic; try direct IP connection
- **Firewall blocking**: Ensure firewall allows mDNS (port 5353) and ADB ports

### Pairing Issues

- **Wrong password**: Ensure the 6-digit pairing code is correct and not expired
- **Connection refused**: Device may not be in pairing mode or pairing may have timed out
- **Network unreachable**: Check IP address and network connectivity

### Connection Issues

- **Authentication failed**: Device may not be paired or keys may be invalid
- **TLS handshake failed**: Try connecting without TLS first
- **Connection timeout**: Device may be sleeping or WiFi debugging disabled

### Common Solutions

1. **Restart WiFi debugging** on the Android device
2. **Clear paired devices** in Android's Developer Options
3. **Remove device** from PyADB registry and re-pair
4. **Check network connectivity** with ping
5. **Try direct IP connection** instead of discovery

## API Reference

### Main Functions

- `discover_wifi_devices(timeout=10.0)` - Discover devices on network
- `pair_wifi_device(ip_address, password)` - Pair with a device
- `connect_wifi_device(device_guid)` - Connect to paired device
- `connect_wifi_address(ip_address, port=5555)` - Connect by IP address

### Classes

- `AdbWifiManager` - High-level WiFi operations manager
- `DiscoveredDevice` - Information about discovered devices
- `PairingResult` - Result of pairing operations
- `DeviceInfo` - Information about paired devices
- `AdbTcpConnection` - TCP/IP connection with TLS support

### Configuration

- **Key directory**: `~/.android/` (configurable)
- **Registry file**: `~/.android/adb_known_hosts.json`
- **Default ports**: 5555 (ADB), 37000-37001 (pairing)
- **Discovery timeout**: 10 seconds (configurable)
- **Connection timeout**: 10 seconds (configurable)

## Examples

See `test_wifi_discovery.py` for comprehensive examples of all functionality.

## License

This WiFi discovery and pairing implementation is part of PyADB and follows the same license terms.
