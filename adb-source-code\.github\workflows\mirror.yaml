name: Run clone.sh and update mirror branch

on:
  workflow_dispatch:   # Allow manual trigger
  schedule:
    - cron: "0 2 * * *" # Optional: runs daily at 2 AM UTC

jobs:
  update-mirror:
    runs-on: ubuntu-latest

    steps:
      # 1. Checkout the repo
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0    # Needed so we can push to another branch
          ref: mirror       # Optional: start from existing mirror branch if exists

      # 2. Run your clone script
      - name: Run clone.sh
        run: |
          chmod +x clone.sh
          ./clone.sh

      # 3. Configure Git identity for actions
      - name: Set up Git config
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      # 4. Commit changes (if any)
      - name: Commit changes
        run: |
          git add -A
          if git diff --cached --quiet; then
            echo "No changes to commit."
            exit 0
          fi
          git commit -m "Update mirror branch from clone.sh run"

      # 5. Push to `mirror` branch
      - name: Push to mirror branch
        run: |
          git push origin HEAD:mirror
