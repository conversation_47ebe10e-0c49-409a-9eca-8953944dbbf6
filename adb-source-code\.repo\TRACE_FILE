PID: 1950 START: 1754922138338005248 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250811T142216Z-P0000079e/repo-20250811T142218Z-P0000079e]

PID: 1950 START: 1754922138369378816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'init'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: git init 1>| 2>|

PID: 1950 END: 1754922138373006336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'init'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: git init 1>| 2>|

PID: 1950 START: 1754922138373834240 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1950 END: 1754922138375310080 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1950 START: 1754922138375563008 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1950 END: 1754922138376982272 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1950 START: 1754922138377214464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 1950 END: 1754922138378836224 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 1950 START: 1754922138378984192 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 1950 END: 1754922138380649984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 1950 START: 1754922138380819200 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --unset-all core.bare 1>| 2>|

PID: 1950 END: 1754922138382395136 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --unset-all core.bare 1>| 2>|

PID: 1950 START: 1754922138382544640 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.depth', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.depth 1 1>| 2>|

PID: 1950 END: 1754922138384084736 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.depth', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.depth 1 1>| 2>|

PID: 1950 START: 1754922138384259072 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'remote.origin.url', 'https://android.googlesource.com/platform/manifest'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all remote.origin.url https://android.googlesource.com/platform/manifest 1>| 2>|

PID: 1950 END: 1754922138385851648 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'remote.origin.url', 'https://android.googlesource.com/platform/manifest'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all remote.origin.url https://android.googlesource.com/platform/manifest 1>| 2>|

PID: 1950 START: 1754922138386012928 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 1950 END: 1754922138387658240 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 1950 START: 1754922138387870976 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'manifest.platform', 'auto'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all manifest.platform auto 1>| 2>|

PID: 1950 END: 1754922138389443584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'manifest.platform', 'auto'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all manifest.platform auto 1>| 2>|

PID: 1950 START: 1754922138389597184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.partialclone', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.partialclone true 1>| 2>|

PID: 1950 END: 1754922138391144448 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.partialclone', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.partialclone true 1>| 2>|

PID: 1950 START: 1754922138391314944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.clonefilter', 'blob:none'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.clonefilter blob:none 1>| 2>|

PID: 1950 END: 1754922138392871424 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.clonefilter', 'blob:none'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.clonefilter blob:none 1>| 2>|

PID: 1950 START: 1754922138393017600 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.superproject', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.superproject false 1>| 2>|

PID: 1950 END: 1754922138394681344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.superproject', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.superproject false 1>| 2>|

PID: 1950 START: 1754922138394857728 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 1950 END: 1754922138396430336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 1950 START: 1754922138396593408 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'extensions.preciousObjects', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all extensions.preciousObjects true 1>| 2>|

PID: 1950 END: 1754922138398156800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'extensions.preciousObjects', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all extensions.preciousObjects true 1>| 2>|

PID: 1950 START: 1754922138398381312 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'extensions.partialclone', 'origin'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all extensions.partialclone origin 1>| 2>|

PID: 1950 END: 1754922138399946496 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'extensions.partialclone', 'origin'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all extensions.partialclone origin 1>| 2>|

PID: 1950 START: 1754922138400191488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1950 END: 1754922138718222592 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1950 START: 1754922138718490368 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 1950 END: 1754922138720591360 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 1950 START: 1754922138720955136 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1950 END: 1754922138722780672 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1950 START: 1754922138722941696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1950 END: 1754922138724825600 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1950 START: 1754922138724985344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1950 END: 1754922140034745856 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1950 START: 1754922140034897408 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1950 END: 1754922140035197440 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1950 START: 1754922140035475200 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1950 END: 1754922140037803264 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1950 START: 1754922140038018048 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1950 END: 1754922140039903232 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1950 START: 1754922140040116992 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1950 END: 1754922140042841088 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1950 START: 1754922140043005184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1950 END: 1754922140044666624 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1950 START: 1754922140044843008 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1950 END: 1754922140044886272 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1950 START: 1754922140045017600 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1950 END: 1754922140046553344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1950 START: 1754922140046704128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1950 END: 1754922140048213504 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1950 START: 1754922140048485120 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1950 END: 1754922140049997568 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1950 START: 1754922140050141696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1950 END: 1754922140051682048 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1950 START: 1754922140051731456 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1950 END: 1754922140051773696 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1950 START: 1754922140051916544 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1950 END: 1754922140053793280 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1950 START: 1754922140053940480 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1950 END: 1754922140055672576 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1950 START: 1754922140055892736 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1950 END: 1754922140057440000 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1950 START: 1754922140120767488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1950 END: 1754922140122590976 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1950 END: 1754922140122665984 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250811T142216Z-P0000079e/repo-20250811T142218Z-P0000079e]

PID: 2031 START: 1754922140329797120 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250811T142220Z-P000007ef/repo-20250811T142220Z-P000007ef]

PID: 2031 START: 1754922140331598336 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 2031 END: 1754922140333154560 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 2031 START: 1754922140333383424 :: parsing /home/<USER>/.gitconfig

PID: 2031 END: 1754922140333454080 :: parsing /home/<USER>/.gitconfig

PID: 2031 START: 1754922140338860032 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 END: 1754922140340487168 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 START: 1754922140340663552 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 2031 END: 1754922140342125568 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 2031 START: 1754922140342427648 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 END: 1754922140343958784 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 START: 1754922140344129792 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 END: 1754922140345743104 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 START: 1754922140345917184 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 END: 1754922140347486720 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 START: 1754922140419072512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2031 END: 1754922140751215616 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2031 START: 1754922140751368448 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2031 END: 1754922140751614464 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2031 START: 1754922140751795456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', '-m', 'manifest set to refs/heads/main', 'refs/remotes/m/main', 'refs/remotes/origin/main'] with debug: : git symbolic-ref -m manifest set to refs/heads/main refs/remotes/m/main refs/remotes/origin/main 1>| 2>|

PID: 2031 END: 1754922140753689344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', '-m', 'manifest set to refs/heads/main', 'refs/remotes/m/main', 'refs/remotes/origin/main'] with debug: : git symbolic-ref -m manifest set to refs/heads/main refs/remotes/m/main refs/remotes/origin/main 1>| 2>|

PID: 2031 START: 1754922140753776128 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2031 END: 1754922140753827072 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2031 START: 1754922140753860096 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2031 END: 1754922140753898496 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2031 START: 1754922140754028544 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 END: 1754922140755665152 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 START: 1754922140756510464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.existingprojectcount', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.existingprojectcount 0 1>| 2>|

PID: 2031 END: 1754922140758135296 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.existingprojectcount', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.existingprojectcount 0 1>| 2>|

PID: 2031 START: 1754922140758286848 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.newprojectcount', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.newprojectcount 1 1>| 2>|

PID: 2031 END: 1754922140759922688 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.newprojectcount', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.newprojectcount 1 1>| 2>|

PID: 2031 START: 1754922140799465984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git
: git init 1>| 2>|

PID: 2031 END: 1754922140803013888 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git
: git init 1>| 2>|

PID: 2031 START: 1754922140805984256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2031 END: 1754922140807513856 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2031 START: 1754922140807791872 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 2031 END: 1754922140809399040 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 2031 START: 1754922140809559808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 2031 END: 1754922140811143168 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 2031 START: 1754922140811325696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --unset-all core.bare 1>| 2>|

PID: 2031 END: 1754922140812975872 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --unset-all core.bare 1>| 2>|

PID: 2031 START: 1754922140813198592 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'remote.aosp.url', 'https://android.googlesource.com/platform/packages/modules/adb'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all remote.aosp.url https://android.googlesource.com/platform/packages/modules/adb 1>| 2>|

PID: 2031 END: 1754922140814811392 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'remote.aosp.url', 'https://android.googlesource.com/platform/packages/modules/adb'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all remote.aosp.url https://android.googlesource.com/platform/packages/modules/adb 1>| 2>|

PID: 2031 START: 1754922140814990080 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'remote.aosp.review', 'https://android-review.googlesource.com/'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all remote.aosp.review https://android-review.googlesource.com/ 1>| 2>|

PID: 2031 END: 1754922140816585984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'remote.aosp.review', 'https://android-review.googlesource.com/'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all remote.aosp.review https://android-review.googlesource.com/ 1>| 2>|

PID: 2031 START: 1754922140816761600 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'remote.aosp.projectname', 'platform/packages/modules/adb'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all remote.aosp.projectname platform/packages/modules/adb 1>| 2>|

PID: 2031 END: 1754922140818342144 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'remote.aosp.projectname', 'platform/packages/modules/adb'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all remote.aosp.projectname platform/packages/modules/adb 1>| 2>|

PID: 2031 START: 1754922140818533376 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'remote.aosp.fetch', '+refs/heads/*:refs/remotes/aosp/*'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all remote.aosp.fetch +refs/heads/*:refs/remotes/aosp/* 1>| 2>|

PID: 2031 END: 1754922140820098560 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'remote.aosp.fetch', '+refs/heads/*:refs/remotes/aosp/*'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all remote.aosp.fetch +refs/heads/*:refs/remotes/aosp/* 1>| 2>|

PID: 2031 START: 1754922140820418304 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 2031 END: 1754922140821991680 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 2031 START: 1754922140822143488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'extensions.partialclone', 'aosp'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all extensions.partialclone aosp 1>| 2>|

PID: 2031 END: 1754922140823746304 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--replace-all', 'extensions.partialclone', 'aosp'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --replace-all extensions.partialclone aosp 1>| 2>|

PID: 2031 START: 1754922140824068096 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 2031 END: 1754922141435699456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 2031 START: 1754922141435933952 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 2031 END: 1754922141437942016 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 2031 START: 1754922141438016256 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 END: 1754922141438201856 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 START: 1754922141438381568 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'symbolic-ref', '-m', 'manifest set to main', 'refs/remotes/m/main', 'refs/remotes/aosp/main'] with debug: : git symbolic-ref -m manifest set to main refs/remotes/m/main refs/remotes/aosp/main 1>| 2>|

PID: 2031 END: 1754922141440125184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'symbolic-ref', '-m', 'manifest set to main', 'refs/remotes/m/main', 'refs/remotes/aosp/main'] with debug: : git symbolic-ref -m manifest set to main refs/remotes/m/main refs/remotes/aosp/main 1>| 2>|

PID: 2031 START: 1754922141440600832 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 2031 END: 1754922141442269440 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 2031 START: 1754922141442449920 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 2031 END: 1754922141444360192 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 2031 START: 1754922141444524032 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2031 END: 1754922142381682688 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2031 START: 1754922142381825024 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 END: 1754922142381881856 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 START: 1754922142381904640 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 END: 1754922142382132224 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 START: 1754922142382350080 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 END: 1754922142384159744 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 START: 1754922142384336896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2031 END: 1754922142385836800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2031 START: 1754922142386250752 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 END: 1754922142386345984 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 START: 1754922142386405120 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 END: 1754922142386454016 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2031 START: 1754922142389058304 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 END: 1754922142390730752 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2031 START: 1754922142481412352 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2031 END: 1754922142483153152 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2031 START: 1754922142483494144 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-11T14:22:22.483380+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-11T14:22:22.483380+00:00 1>| 2>|

PID: 2031 END: 1754922142485101824 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-11T14:22:22.483380+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-11T14:22:22.483380+00:00 1>| 2>|

PID: 2031 START: 1754922142485249536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.version', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.version 1 1>| 2>|

PID: 2031 END: 1754922142486859008 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.version', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.version 1 1>| 2>|

PID: 2031 START: 1754922142487014144 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.sys.argv', "['/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/main.py', '--repo-dir=/home/<USER>/work/adb-source-code/adb-source-code/.repo', '--wrapper-version=2.54', '--wrapper-path=/home/<USER>/work/adb-source-code/adb-source-code/repo', '--', 'sync', '-c', '-j8', 'packages/modules/adb']"] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.sys.argv ['/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/main.py', '--repo-dir=/home/<USER>/work/adb-source-code/adb-source-code/.repo', '--wrapper-version=2.54', '--wrapper-path=/home/<USER>/work/adb-source-code/adb-source-code/repo', '--', 'sync', '-c', '-j8', 'packages/modules/adb'] 1>| 2>|

PID: 2031 END: 1754922142488609536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.sys.argv', "['/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/main.py', '--repo-dir=/home/<USER>/work/adb-source-code/adb-source-code/.repo', '--wrapper-version=2.54', '--wrapper-path=/home/<USER>/work/adb-source-code/adb-source-code/repo', '--', 'sync', '-c', '-j8', 'packages/modules/adb']"] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.sys.argv ['/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/main.py', '--repo-dir=/home/<USER>/work/adb-source-code/adb-source-code/.repo', '--wrapper-version=2.54', '--wrapper-path=/home/<USER>/work/adb-source-code/adb-source-code/repo', '--', 'sync', '-c', '-j8', 'packages/modules/adb'] 1>| 2>|

PID: 2031 START: 1754922142488792320 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobs', '8'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobs 8 1>| 2>|

PID: 2031 END: 1754922142490401536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobs', '8'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobs 8 1>| 2>|

PID: 2031 START: 1754922142490550016 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.outermanifest', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.outermanifest true 1>| 2>|

PID: 2031 END: 1754922142492121088 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.outermanifest', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.outermanifest true 1>| 2>|

PID: 2031 START: 1754922142492268288 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobsnetwork', '8'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobsnetwork 8 1>| 2>|

PID: 2031 END: 1754922142493878272 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobsnetwork', '8'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobsnetwork 8 1>| 2>|

PID: 2031 START: 1754922142494055936 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobscheckout', '8'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobscheckout 8 1>| 2>|

PID: 2031 END: 1754922142495658752 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobscheckout', '8'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobscheckout 8 1>| 2>|

PID: 2031 START: 1754922142495806720 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.mpupdate', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.mpupdate true 1>| 2>|

PID: 2031 END: 1754922142497385728 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.mpupdate', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.mpupdate true 1>| 2>|

PID: 2031 START: 1754922142497532928 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.interleaved', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.interleaved true 1>| 2>|

PID: 2031 END: 1754922142499099648 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.interleaved', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.interleaved true 1>| 2>|

PID: 2031 START: 1754922142499246336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.currentbranchonly', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.currentbranchonly true 1>| 2>|

PID: 2031 END: 1754922142500873728 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.currentbranchonly', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.currentbranchonly true 1>| 2>|

PID: 2031 START: 1754922142501021184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.clonebundle', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.clonebundle false 1>| 2>|

PID: 2031 END: 1754922142502615552 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.clonebundle', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.clonebundle false 1>| 2>|

PID: 2031 START: 1754922142502765056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.retryfetches', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.retryfetches 0 1>| 2>|

PID: 2031 END: 1754922142504361984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.retryfetches', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.retryfetches 0 1>| 2>|

PID: 2031 START: 1754922142504509696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.prune', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.prune true 1>| 2>|

PID: 2031 END: 1754922142506060800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.prune', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.prune true 1>| 2>|

PID: 2031 START: 1754922142506240512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.repoverify', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.repoverify true 1>| 2>|

PID: 2031 END: 1754922142507902208 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.repoverify', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.repoverify true 1>| 2>|

PID: 2031 START: 1754922142508049408 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.quiet', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.quiet false 1>| 2>|

PID: 2031 END: 1754922142509643520 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.quiet', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.quiet false 1>| 2>|

PID: 2031 START: 1754922142509801984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.verbose', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.verbose false 1>| 2>|

PID: 2031 END: 1754922142511406336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.verbose', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.verbose false 1>| 2>|

PID: 2031 START: 1754922142511596544 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.depth', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.depth 1 1>| 2>|

PID: 2031 END: 1754922142513144832 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.depth', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.depth 1 1>| 2>|

PID: 2031 START: 1754922142513339136 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.partialclone', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.partialclone true 1>| 2>|

PID: 2031 END: 1754922142514912256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.partialclone', 'true'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.partialclone true 1>| 2>|

PID: 2031 START: 1754922142515055872 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.clonefilter', 'blob:none'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.clonefilter blob:none 1>| 2>|

PID: 2031 END: 1754922142516712704 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.clonefilter', 'blob:none'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.clonefilter blob:none 1>| 2>|

PID: 2031 START: 1754922142516880896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.superproject', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.superproject false 1>| 2>|

PID: 2031 END: 1754922142518475264 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.superproject', 'false'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.superproject false 1>| 2>|

PID: 2031 START: 1754922142518628096 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.url', 'https://android.googlesource.com/platform/manifest'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.url https://android.googlesource.com/platform/manifest 1>| 2>|

PID: 2031 END: 1754922142520201472 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.url', 'https://android.googlesource.com/platform/manifest'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.url https://android.googlesource.com/platform/manifest 1>| 2>|

PID: 2031 START: 1754922142520380160 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 2031 END: 1754922142521955328 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 2031 START: 1754922142522102784 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.partialclonefilter', 'blob:none'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.partialclonefilter blob:none 1>| 2>|

PID: 2031 END: 1754922142523710208 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.partialclonefilter', 'blob:none'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.partialclonefilter blob:none 1>| 2>|

PID: 2031 START: 1754922142523858944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.branch.default.remote', 'origin'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.branch.default.remote origin 1>| 2>|

PID: 2031 END: 1754922142525448704 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.branch.default.remote', 'origin'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.branch.default.remote origin 1>| 2>|

PID: 2031 START: 1754922142525597184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.branch.default.merge', 'refs/heads/main'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.branch.default.merge refs/heads/main 1>| 2>|

PID: 2031 END: 1754922142527184896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.branch.default.merge', 'refs/heads/main'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.branch.default.merge refs/heads/main 1>| 2>|

PID: 2031 START: 1754922142527378432 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.existingprojectcount', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.existingprojectcount 0 1>| 2>|

PID: 2031 END: 1754922142529026048 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.existingprojectcount', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.existingprojectcount 0 1>| 2>|

PID: 2031 START: 1754922142529174016 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.newprojectcount', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.newprojectcount 1 1>| 2>|

PID: 2031 END: 1754922142530825728 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.newprojectcount', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.newprojectcount 1 1>| 2>|

PID: 2031 START: 1754922142531344384 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2031 END: 1754922142532794880 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2031 END: 1754922142532866560 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250811T142220Z-P000007ef/repo-20250811T142220Z-P000007ef]

PID: 2015 START: 1754968460434813440 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250812T031418Z-P000007df/repo-20250812T031420Z-P000007df]

PID: 2015 START: 1754968460436729856 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 2015 END: 1754968460438300928 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 2015 START: 1754968460438721536 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 2015 END: 1754968460440260608 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 2015 START: 1754968460449437184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2015 END: 1754968460776535040 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2015 START: 1754968460777008128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 2015 END: 1754968460779073280 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 2015 START: 1754968460779259904 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 2015 END: 1754968460781448704 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 2015 START: 1754968460781644800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2015 END: 1754968460784399104 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2015 START: 1754968460784479232 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2015 END: 1754968460784775680 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2015 START: 1754968460784934144 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 END: 1754968460786658816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 START: 1754968460786818816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2015 END: 1754968460788455424 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2015 START: 1754968460788719104 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 2015 END: 1754968460791614464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 2015 START: 1754968460791830272 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 2015 END: 1754968460793646336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 2015 START: 1754968460793843456 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2015 END: 1754968460793892608 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2015 START: 1754968460793917184 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2015 END: 1754968460794116096 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2015 START: 1754968460794267648 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 END: 1754968460796050176 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 START: 1754968460796208128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2015 END: 1754968460797853696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2015 START: 1754968460798077696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 END: 1754968460800017920 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 START: 1754968460800183296 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2015 END: 1754968460801881088 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2015 START: 1754968460802066688 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 END: 1754968460803794176 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 START: 1754968460803952896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2015 END: 1754968460805573632 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2015 START: 1754968460805659904 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2015 END: 1754968460805731840 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2015 START: 1754968460805890560 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 2015 END: 1754968460807921408 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 2015 START: 1754968460808075008 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 2015 END: 1754968460809956096 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 2015 START: 1754968460810199296 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 END: 1754968460811872256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2015 START: 1754968460877324800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2015 END: 1754968460879250688 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2015 END: 1754968460879336704 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250812T031418Z-P000007df/repo-20250812T031420Z-P000007df]

PID: 2076 START: 1754968461101847552 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250812T031420Z-P0000081c/repo-20250812T031421Z-P0000081c]

PID: 2076 START: 1754968461102069760 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 2076 END: 1754968461102163456 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 2076 START: 1754968461102206976 :: parsing /home/<USER>/.gitconfig

PID: 2076 END: 1754968461102257408 :: parsing /home/<USER>/.gitconfig

PID: 2076 START: 1754968461109555456 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 END: 1754968461111264768 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 START: 1754968461111465728 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 2076 END: 1754968461113045248 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 2076 START: 1754968461113371136 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 END: 1754968461114977536 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 START: 1754968461115164160 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 END: 1754968461116809472 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 START: 1754968461116999168 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 END: 1754968461118679040 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 START: 1754968461190980096 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2076 END: 1754968461500044032 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2076 START: 1754968461500170240 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2076 END: 1754968461500453376 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2076 START: 1754968461500537088 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2076 END: 1754968461500592128 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2076 START: 1754968461500652544 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2076 END: 1754968461500715520 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2076 START: 1754968461500880128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 END: 1754968461502779904 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 START: 1754968461503723008 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.existingprojectcount', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.existingprojectcount 1 1>| 2>|

PID: 2076 END: 1754968461505667584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.existingprojectcount', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.existingprojectcount 1 1>| 2>|

PID: 2076 START: 1754968461505836288 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.newprojectcount', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.newprojectcount 0 1>| 2>|

PID: 2076 END: 1754968461507768832 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.newprojectcount', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.newprojectcount 0 1>| 2>|

PID: 2076 START: 1754968461542011904 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2076 END: 1754968461544104704 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2076 START: 1754968461544869632 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 2076 END: 1754968461830679552 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 2076 START: 1754968461830819328 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2076 END: 1754968461831057152 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2076 START: 1754968461831590656 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 2076 END: 1754968461833740800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 2076 START: 1754968461833919744 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 2076 END: 1754968461835751168 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 2076 START: 1754968461835917312 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2076 END: 1754968461877920512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2076 START: 1754968461878078464 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2076 END: 1754968461878150144 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2076 START: 1754968461878354944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 END: 1754968461880356352 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 START: 1754968461880535808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2076 END: 1754968461882299392 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2076 START: 1754968461882813952 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2076 END: 1754968461882885888 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2076 START: 1754968461882932736 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2076 END: 1754968461882978816 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2076 START: 1754968461885687808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 END: 1754968461887670528 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2076 START: 1754968461991759360 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 2076 END: 1754968461991861248 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 2076 START: 1754968461992233984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-12T03:14:21.992092+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-12T03:14:21.992092+00:00 1>| 2>|

PID: 2076 END: 1754968461994312704 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-12T03:14:21.992092+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-12T03:14:21.992092+00:00 1>| 2>|

PID: 2076 START: 1754968461994570496 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.existingprojectcount', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.existingprojectcount 1 1>| 2>|

PID: 2076 END: 1754968461996259840 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.existingprojectcount', '1'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.existingprojectcount 1 1>| 2>|

PID: 2076 START: 1754968461996413696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.newprojectcount', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.newprojectcount 0 1>| 2>|

PID: 2076 END: 1754968461998058752 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.newprojectcount', '0'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.newprojectcount 0 1>| 2>|

PID: 2076 START: 1754968461998570240 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2076 END: 1754968462000079360 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2076 END: 1754968462000152320 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250812T031420Z-P0000081c/repo-20250812T031421Z-P0000081c]

PID: 1922 START: 1755055031481517824 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250813T031709Z-P00000782/repo-20250813T031711Z-P00000782]

PID: 1922 START: 1755055031483323648 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1922 END: 1755055031484887552 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1922 START: 1755055031485281024 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1922 END: 1755055031486774784 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1922 START: 1755055031507943936 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1922 END: 1755055031695911936 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1922 START: 1755055031696366336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1922 END: 1755055031698269184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1922 START: 1755055031698423808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1922 END: 1755055031700506368 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1922 START: 1755055031700651264 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1922 END: 1755055031703144192 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1922 START: 1755055031703214592 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 END: 1755055031703465728 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 START: 1755055031703602432 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755055031705262336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755055031705411840 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 END: 1755055031706928896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 START: 1755055031707176960 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1922 END: 1755055031709862656 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1922 START: 1755055031710052352 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1922 END: 1755055031711772160 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1922 START: 1755055031711942912 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 END: 1755055031711989248 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 START: 1755055031712036096 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 END: 1755055031712239872 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 START: 1755055031712372736 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755055031713932544 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755055031714107904 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 END: 1755055031715647232 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 START: 1755055031715844096 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755055031717428992 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755055031717575680 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 END: 1755055031719109888 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 START: 1755055031719258112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755055031720795904 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755055031720940288 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 END: 1755055031722482688 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 START: 1755055031722534144 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 END: 1755055031722585344 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 START: 1755055031722726144 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1922 END: 1755055031724636928 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1922 START: 1755055031724789248 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1922 END: 1755055031726490368 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1922 START: 1755055031726714112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755055031728301312 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755055031793037056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1922 END: 1755055031794819072 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1922 END: 1755055031794891520 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250813T031709Z-P00000782/repo-20250813T031711Z-P00000782]

PID: 1982 START: 1755055032002172672 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250813T031711Z-P000007be/repo-20250813T031712Z-P000007be]

PID: 1982 START: 1755055032002388480 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1982 END: 1755055032002480896 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1982 START: 1755055032002523904 :: parsing /home/<USER>/.gitconfig

PID: 1982 END: 1755055032002575616 :: parsing /home/<USER>/.gitconfig

PID: 1982 START: 1755055032009769472 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755055032011456768 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755055032011647488 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1982 END: 1755055032013212672 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1982 START: 1755055032013522432 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755055032015135744 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755055032015314688 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755055032016961792 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755055032017197568 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755055032018829312 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755055032091536384 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1982 END: 1755055032236909568 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1982 START: 1755055032237058560 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 END: 1755055032237338368 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 START: 1755055032237410816 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 END: 1755055032237463808 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 START: 1755055032237492992 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 END: 1755055032237570304 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 START: 1755055032237740800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755055032239536640 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755055032299748864 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1982 END: 1755055032301619456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1982 START: 1755055032302297856 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1982 END: 1755055032449640704 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1982 START: 1755055032449779712 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 END: 1755055032450000640 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 START: 1755055032450560512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1982 END: 1755055032452950784 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1982 START: 1755055032453149696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1982 END: 1755055032454831872 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1982 START: 1755055032454980864 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1982 END: 1755055032496205568 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1982 START: 1755055032496335104 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 END: 1755055032496397824 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 START: 1755055032496579328 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755055032498359296 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755055032498521600 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1982 END: 1755055032500066048 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1982 START: 1755055032500512768 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 END: 1755055032500576512 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 START: 1755055032500623360 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 END: 1755055032500670208 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 START: 1755055032503192576 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755055032504926208 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755055032599962112 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1982 END: 1755055032600070912 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1982 START: 1755055032600403200 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-13T03:17:12.600276+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-13T03:17:12.600276+00:00 1>| 2>|

PID: 1982 END: 1755055032602338816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-13T03:17:12.600276+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-13T03:17:12.600276+00:00 1>| 2>|

PID: 1982 START: 1755055032602893056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1982 END: 1755055032604348416 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1982 END: 1755055032604417024 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250813T031711Z-P000007be/repo-20250813T031712Z-P000007be]

PID: 1934 START: 1755141445204448512 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250814T031723Z-P0000078e/repo-20250814T031725Z-P0000078e]

PID: 1934 START: 1755141445206269696 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1934 END: 1755141445207879936 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1934 START: 1755141445208255488 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1934 END: 1755141445209765632 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1934 START: 1755141445242797056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1934 END: 1755141445431910912 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1934 START: 1755141445432331264 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1934 END: 1755141445434291456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1934 START: 1755141445434462464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1934 END: 1755141445436467456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1934 START: 1755141445436617472 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1934 END: 1755141445439135488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1934 START: 1755141445439212288 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1934 END: 1755141445439458816 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1934 START: 1755141445439599360 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 END: 1755141445441195520 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 START: 1755141445441355520 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1934 END: 1755141445442938880 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1934 START: 1755141445443161600 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1934 END: 1755141445446021632 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1934 START: 1755141445446188800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1934 END: 1755141445447879936 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1934 START: 1755141445448054016 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1934 END: 1755141445448100608 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1934 START: 1755141445448124672 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1934 END: 1755141445448312320 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1934 START: 1755141445448445440 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 END: 1755141445450021888 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 START: 1755141445450169088 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1934 END: 1755141445451693312 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1934 START: 1755141445451930624 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 END: 1755141445453486336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 START: 1755141445453632256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1934 END: 1755141445455180288 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1934 START: 1755141445455328256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 END: 1755141445456899072 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 START: 1755141445457043200 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1934 END: 1755141445458574080 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1934 START: 1755141445458624256 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1934 END: 1755141445458675200 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1934 START: 1755141445458818816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1934 END: 1755141445460674816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1934 START: 1755141445460843008 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1934 END: 1755141445462606848 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1934 START: 1755141445462851584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 END: 1755141445464452096 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1934 START: 1755141445527997184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1934 END: 1755141445529711104 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1934 END: 1755141445529782272 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250814T031723Z-P0000078e/repo-20250814T031725Z-P0000078e]

PID: 1994 START: 1755141445733754368 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250814T031725Z-P000007ca/repo-20250814T031725Z-P000007ca]

PID: 1994 START: 1755141445733957376 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1994 END: 1755141445734048256 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1994 START: 1755141445734087680 :: parsing /home/<USER>/.gitconfig

PID: 1994 END: 1755141445734156544 :: parsing /home/<USER>/.gitconfig

PID: 1994 START: 1755141445740508416 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 END: 1755141445742664192 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 START: 1755141445742969856 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1994 END: 1755141445745187840 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1994 START: 1755141445745612544 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 END: 1755141445747890176 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 START: 1755141445748137472 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 END: 1755141445750532096 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 START: 1755141445750803456 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 END: 1755141445752556288 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 START: 1755141445823797504 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1994 END: 1755141445992363264 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1994 START: 1755141445992498432 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1994 END: 1755141445992784384 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1994 START: 1755141445992891136 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1994 END: 1755141445992956672 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1994 START: 1755141445992994560 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1994 END: 1755141445993041152 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1994 START: 1755141445993198592 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 END: 1755141445994979072 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 START: 1755141446064323584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1994 END: 1755141446066236416 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1994 START: 1755141446066914560 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1994 END: 1755141446234507008 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1994 START: 1755141446234645248 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1994 END: 1755141446234913792 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1994 START: 1755141446235429888 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1994 END: 1755141446237625344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1994 START: 1755141446237807616 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1994 END: 1755141446239571200 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1994 START: 1755141446239727360 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1994 END: 1755141446281086208 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1994 START: 1755141446281207552 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1994 END: 1755141446281268992 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1994 START: 1755141446281445376 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 END: 1755141446283297536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 START: 1755141446283533056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1994 END: 1755141446285267456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1994 START: 1755141446285709312 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1994 END: 1755141446285772032 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1994 START: 1755141446285857792 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1994 END: 1755141446285915392 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1994 START: 1755141446288586240 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 END: 1755141446290426112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1994 START: 1755141446387965184 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1994 END: 1755141446388042752 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1994 START: 1755141446388376576 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-14T03:17:26.388250+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-14T03:17:26.388250+00:00 1>| 2>|

PID: 1994 END: 1755141446390297856 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-14T03:17:26.388250+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-14T03:17:26.388250+00:00 1>| 2>|

PID: 1994 START: 1755141446390871552 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1994 END: 1755141446392337408 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1994 END: 1755141446392406784 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250814T031725Z-P000007ca/repo-20250814T031725Z-P000007ca]

PID: 1922 START: 1755227910917698816 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250815T031829Z-P00000782/repo-20250815T031830Z-P00000782]

PID: 1922 START: 1755227910919497728 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1922 END: 1755227910921050624 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1922 START: 1755227910921415168 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1922 END: 1755227910922920960 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1922 START: 1755227910948225024 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1922 END: 1755227911273012736 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1922 START: 1755227911273440768 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1922 END: 1755227911275419392 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1922 START: 1755227911275590400 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1922 END: 1755227911277648128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1922 START: 1755227911277838336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1922 END: 1755227911280425728 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1922 START: 1755227911280500736 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 END: 1755227911280752896 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 START: 1755227911280931584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755227911282527488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755227911282676736 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 END: 1755227911284247040 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 START: 1755227911284455680 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1922 END: 1755227911287299072 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1922 START: 1755227911287474176 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1922 END: 1755227911289232128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1922 START: 1755227911289413376 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 END: 1755227911289460480 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 START: 1755227911289490944 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 END: 1755227911289680640 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 START: 1755227911289865472 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755227911291459840 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755227911291625216 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 END: 1755227911293450752 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 START: 1755227911293732608 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755227911295393280 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755227911295542272 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 END: 1755227911297105920 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 START: 1755227911297256448 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755227911298839040 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755227911298984192 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 END: 1755227911300509440 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1922 START: 1755227911300560384 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 END: 1755227911300612096 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1922 START: 1755227911300790016 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1922 END: 1755227911302653696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1922 START: 1755227911302852096 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1922 END: 1755227911304572416 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1922 START: 1755227911304833280 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 END: 1755227911306405888 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1922 START: 1755227911369070336 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1922 END: 1755227911370817024 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1922 END: 1755227911370887680 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250815T031829Z-P00000782/repo-20250815T031830Z-P00000782]

PID: 1982 START: 1755227911580744192 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250815T031831Z-P000007be/repo-20250815T031831Z-P000007be]

PID: 1982 START: 1755227911581000704 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1982 END: 1755227911581095936 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1982 START: 1755227911581140224 :: parsing /home/<USER>/.gitconfig

PID: 1982 END: 1755227911581191680 :: parsing /home/<USER>/.gitconfig

PID: 1982 START: 1755227911588371456 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755227911590030592 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755227911590220288 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1982 END: 1755227911591720448 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1982 START: 1755227911592075776 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755227911593611264 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755227911593819392 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755227911595415552 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755227911595596032 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755227911597234944 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755227911668254464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1982 END: 1755227911913245952 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1982 START: 1755227911913374720 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 END: 1755227911913652736 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 START: 1755227911913727744 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 END: 1755227911913816064 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 START: 1755227911913858816 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 END: 1755227911913906688 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1982 START: 1755227911914066432 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755227911915779584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755227911979458048 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1982 END: 1755227911981396480 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1982 START: 1755227911982080256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1982 END: 1755227912492122624 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1982 START: 1755227912492255744 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 END: 1755227912492497920 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 START: 1755227912493074944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1982 END: 1755227912495002112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1982 START: 1755227912495154176 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1982 END: 1755227912496797440 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1982 START: 1755227912496949760 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1982 END: 1755227912538624768 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1982 START: 1755227912538797056 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 END: 1755227912538870528 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 START: 1755227912539062784 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755227912540920832 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755227912541073408 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1982 END: 1755227912542594816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1982 START: 1755227912543057408 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 END: 1755227912543129856 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 START: 1755227912543181568 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 END: 1755227912543227904 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1982 START: 1755227912545698048 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 END: 1755227912547565568 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1982 START: 1755227912650123776 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1982 END: 1755227912650245888 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1982 START: 1755227912650598400 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-15T03:18:32.650463+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-15T03:18:32.650463+00:00 1>| 2>|

PID: 1982 END: 1755227912652563968 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-15T03:18:32.650463+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-15T03:18:32.650463+00:00 1>| 2>|

PID: 1982 START: 1755227912653139968 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1982 END: 1755227912654679040 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1982 END: 1755227912654754816 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250815T031831Z-P000007be/repo-20250815T031831Z-P000007be]

PID: 1920 START: 1755313905907807232 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250816T031144Z-P00000780/repo-20250816T031145Z-P00000780]

PID: 1920 START: 1755313905909624064 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1920 END: 1755313905911173120 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1920 START: 1755313905911536384 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1920 END: 1755313905913050112 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1920 START: 1755313905952511488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1920 END: 1755313906339529984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1920 START: 1755313906339967488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1920 END: 1755313906341791488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1920 START: 1755313906341948160 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1920 END: 1755313906343858432 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1920 START: 1755313906344002560 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1920 END: 1755313906346455808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1920 START: 1755313906346523904 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 END: 1755313906346798080 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 START: 1755313906346938624 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755313906348576512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755313906348757248 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 END: 1755313906350303744 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 START: 1755313906350512128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1920 END: 1755313906353241856 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1920 START: 1755313906353405440 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1920 END: 1755313906355129344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1920 START: 1755313906355296000 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 END: 1755313906355342080 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 START: 1755313906355365376 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 END: 1755313906355554816 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 START: 1755313906355711744 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755313906357264896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755313906357412096 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 END: 1755313906358984704 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 START: 1755313906359178752 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755313906360754688 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755313906360898560 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 END: 1755313906362420736 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 START: 1755313906362574080 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755313906364154880 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755313906364304128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 END: 1755313906365908992 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 START: 1755313906365959680 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 END: 1755313906366010112 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 START: 1755313906366154240 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1920 END: 1755313906367994112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1920 START: 1755313906368138240 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1920 END: 1755313906370014464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1920 START: 1755313906370227712 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755313906372137984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755313906434106880 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1920 END: 1755313906435639552 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1920 END: 1755313906435704832 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250816T031144Z-P00000780/repo-20250816T031145Z-P00000780]

PID: 1980 START: 1755313906639271936 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250816T031146Z-P000007bc/repo-20250816T031146Z-P000007bc]

PID: 1980 START: 1755313906639448576 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1980 END: 1755313906639534848 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1980 START: 1755313906639576064 :: parsing /home/<USER>/.gitconfig

PID: 1980 END: 1755313906639664640 :: parsing /home/<USER>/.gitconfig

PID: 1980 START: 1755313906645922048 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 END: 1755313906647514880 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 START: 1755313906647732480 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1980 END: 1755313906649184256 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1980 START: 1755313906649477376 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 END: 1755313906651061504 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 START: 1755313906651257856 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 END: 1755313906652862464 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 START: 1755313906653040896 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 END: 1755313906654629632 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 START: 1755313906723235584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1980 END: 1755313907016631808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1980 START: 1755313907016756480 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1980 END: 1755313907017032448 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1980 START: 1755313907017104384 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1980 END: 1755313907017157888 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1980 START: 1755313907017187072 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1980 END: 1755313907017230848 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1980 START: 1755313907017386496 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 END: 1755313907019086848 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 START: 1755313907095243520 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1980 END: 1755313907097072896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1980 START: 1755313907097771776 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1980 END: 1755313907429015040 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1980 START: 1755313907429144832 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1980 END: 1755313907429360384 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1980 START: 1755313907429912320 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1980 END: 1755313907431784448 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1980 START: 1755313907431937280 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1980 END: 1755313907433547264 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1980 START: 1755313907433727232 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1980 END: 1755313907480864512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1980 START: 1755313907481035008 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1980 END: 1755313907481121536 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1980 START: 1755313907481385984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 END: 1755313907483913728 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 START: 1755313907484123904 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1980 END: 1755313907486226944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1980 START: 1755313907488016640 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1980 END: 1755313907488101888 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1980 START: 1755313907488162048 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1980 END: 1755313907488230656 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1980 START: 1755313907491604224 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 END: 1755313907496740608 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1980 START: 1755313907617203712 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1980 END: 1755313907617271296 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1980 START: 1755313907617638400 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-16T03:11:47.617460+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-16T03:11:47.617460+00:00 1>| 2>|

PID: 1980 END: 1755313907619524608 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-16T03:11:47.617460+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-16T03:11:47.617460+00:00 1>| 2>|

PID: 1980 START: 1755313907620106752 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1980 END: 1755313907621530112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1980 END: 1755313907621621248 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250816T031146Z-P000007bc/repo-20250816T031146Z-P000007bc]

PID: 1976 START: 1755400969698062336 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250817T032248Z-P000007b8/repo-20250817T032249Z-P000007b8]

PID: 1976 START: 1755400969699693312 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1976 END: 1755400969701184768 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1976 START: 1755400969701541376 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1976 END: 1755400969702981376 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1976 START: 1755400969726375424 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1976 END: 1755400970012242688 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1976 START: 1755400970012660736 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1976 END: 1755400970014565632 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1976 START: 1755400970014742784 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1976 END: 1755400970016765184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1976 START: 1755400970016915712 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1976 END: 1755400970019495936 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1976 START: 1755400970019568896 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 END: 1755400970019853824 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 START: 1755400970020006912 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755400970021747968 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755400970021897728 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 END: 1755400970023489792 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 START: 1755400970023703808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1976 END: 1755400970026477056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1976 START: 1755400970026644992 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1976 END: 1755400970028388864 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1976 START: 1755400970028565760 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 END: 1755400970028612608 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 START: 1755400970028635648 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 END: 1755400970028822784 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 START: 1755400970028955648 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755400970030606080 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755400970030757120 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 END: 1755400970032329984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 START: 1755400970032529920 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755400970034106112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755400970034253568 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 END: 1755400970035837184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 START: 1755400970035995648 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755400970037657344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755400970037803520 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 END: 1755400970039383040 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 START: 1755400970039438336 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 END: 1755400970039491584 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 START: 1755400970039638784 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1976 END: 1755400970041528064 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1976 START: 1755400970041673472 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1976 END: 1755400970043419136 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1976 START: 1755400970043638272 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755400970045228800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755400970109584384 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1976 END: 1755400970111410944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1976 END: 1755400970111488256 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250817T032248Z-P000007b8/repo-20250817T032249Z-P000007b8]

PID: 2038 START: 1755400970325824256 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250817T032250Z-P000007f6/repo-20250817T032250Z-P000007f6]

PID: 2038 START: 1755400970326027520 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 2038 END: 1755400970326150400 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 2038 START: 1755400970326192384 :: parsing /home/<USER>/.gitconfig

PID: 2038 END: 1755400970326245376 :: parsing /home/<USER>/.gitconfig

PID: 2038 START: 1755400970333147392 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 END: 1755400970334754304 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 START: 1755400970334936576 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 2038 END: 1755400970336487936 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 2038 START: 1755400970336785408 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 END: 1755400970338391040 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 START: 1755400970338566656 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 END: 1755400970340204544 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 START: 1755400970340382464 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 END: 1755400970341980672 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 START: 1755400970435791360 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2038 END: 1755400970744316672 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2038 START: 1755400970744439040 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2038 END: 1755400970744716032 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2038 START: 1755400970744791040 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2038 END: 1755400970744845824 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2038 START: 1755400970744876032 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2038 END: 1755400970744921088 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2038 START: 1755400970745148672 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 END: 1755400970746892032 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 START: 1755400970814715904 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2038 END: 1755400970816644864 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2038 START: 1755400970817345024 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 2038 END: 1755400971254712320 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 2038 START: 1755400971254851328 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2038 END: 1755400971255116544 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2038 START: 1755400971255642624 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 2038 END: 1755400971257537536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 2038 START: 1755400971257690112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 2038 END: 1755400971259321344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 2038 START: 1755400971259464448 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2038 END: 1755400971300832512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2038 START: 1755400971300974080 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2038 END: 1755400971301067264 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2038 START: 1755400971301272576 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 END: 1755400971303162112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 START: 1755400971303313664 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2038 END: 1755400971304857088 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2038 START: 1755400971305322240 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2038 END: 1755400971305388288 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2038 START: 1755400971305430784 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2038 END: 1755400971305476352 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2038 START: 1755400971308199424 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 END: 1755400971309976064 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2038 START: 1755400971407860480 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 2038 END: 1755400971407933696 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 2038 START: 1755400971408315392 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-17T03:22:51.408183+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-17T03:22:51.408183+00:00 1>| 2>|

PID: 2038 END: 1755400971410269440 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-17T03:22:51.408183+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-17T03:22:51.408183+00:00 1>| 2>|

PID: 2038 START: 1755400971410819328 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2038 END: 1755400971412266752 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2038 END: 1755400971412335616 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250817T032250Z-P000007f6/repo-20250817T032250Z-P000007f6]

PID: 1920 START: 1755487607840711680 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250818T032646Z-P00000780/repo-20250818T032647Z-P00000780]

PID: 1920 START: 1755487607842471936 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1920 END: 1755487607844003072 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1920 START: 1755487607844367616 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1920 END: 1755487607845839104 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1920 START: 1755487607868542464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1920 END: 1755487608086168576 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1920 START: 1755487608086623488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1920 END: 1755487608088484096 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1920 START: 1755487608088671488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1920 END: 1755487608090889472 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1920 START: 1755487608091036672 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1920 END: 1755487608093523456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1920 START: 1755487608093612288 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 END: 1755487608093875712 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 START: 1755487608094015488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755487608095639552 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755487608095785984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 END: 1755487608097316352 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 START: 1755487608097530112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1920 END: 1755487608100300288 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1920 START: 1755487608100465152 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1920 END: 1755487608102189056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1920 START: 1755487608102365952 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 END: 1755487608102411776 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 START: 1755487608102435072 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 END: 1755487608102656768 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 START: 1755487608102796800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755487608104337152 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755487608104484864 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 END: 1755487608106050560 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 START: 1755487608106244608 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755487608107795456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755487608107939328 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 END: 1755487608109458944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 START: 1755487608109630464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755487608111179520 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755487608111324160 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 END: 1755487608112931840 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1920 START: 1755487608112988160 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 END: 1755487608113040128 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1920 START: 1755487608113184768 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1920 END: 1755487608115022848 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1920 START: 1755487608115166976 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1920 END: 1755487608116872960 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1920 START: 1755487608117089280 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 END: 1755487608118730240 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1920 START: 1755487608184221696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1920 END: 1755487608185996544 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1920 END: 1755487608186068480 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250818T032646Z-P00000780/repo-20250818T032647Z-P00000780]

PID: 1981 START: 1755487608390361344 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250818T032648Z-P000007bd/repo-20250818T032648Z-P000007bd]

PID: 1981 START: 1755487608390563840 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1981 END: 1755487608390682624 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1981 START: 1755487608390728448 :: parsing /home/<USER>/.gitconfig

PID: 1981 END: 1755487608390780416 :: parsing /home/<USER>/.gitconfig

PID: 1981 START: 1755487608397459456 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755487608399048192 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755487608399229952 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1981 END: 1755487608400703232 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1981 START: 1755487608401006848 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755487608402508288 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755487608402750720 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755487608404347392 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755487608404524288 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755487608406124288 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755487608475527424 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1981 END: 1755487608630364416 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1981 START: 1755487608630476032 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 END: 1755487608630788864 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 START: 1755487608630865664 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 END: 1755487608630920448 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 START: 1755487608630966016 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 END: 1755487608631019776 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 START: 1755487608631184640 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755487608632888576 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755487608665001472 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1981 END: 1755487608666769664 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1981 START: 1755487608667409920 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1981 END: 1755487608816746496 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1981 START: 1755487608816912896 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 END: 1755487608817134080 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 START: 1755487608817699328 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1981 END: 1755487608819571712 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1981 START: 1755487608819799552 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1981 END: 1755487608821389056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1981 START: 1755487608821538048 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1981 END: 1755487608866114816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1981 START: 1755487608866241536 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 END: 1755487608866304768 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 START: 1755487608866497536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755487608868333824 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755487608868483328 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1981 END: 1755487608870030848 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1981 START: 1755487608870460672 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 END: 1755487608870522624 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 START: 1755487608870564096 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 END: 1755487608870639360 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 START: 1755487608873302016 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755487608875057152 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755487608971086848 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1981 END: 1755487608971165952 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1981 START: 1755487608971516928 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-18T03:26:48.971381+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-18T03:26:48.971381+00:00 1>| 2>|

PID: 1981 END: 1755487608973473792 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-18T03:26:48.971381+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-18T03:26:48.971381+00:00 1>| 2>|

PID: 1981 START: 1755487608974061568 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1981 END: 1755487608975521792 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1981 END: 1755487608975612160 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250818T032648Z-P000007bd/repo-20250818T032648Z-P000007bd]

PID: 1921 START: 1755572858332366336 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250819T030736Z-P00000781/repo-20250819T030738Z-P00000781]

PID: 1921 START: 1755572858334333952 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1921 END: 1755572858335925760 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1921 START: 1755572858336363776 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1921 END: 1755572858337862400 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1921 START: 1755572858360180480 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1921 END: 1755572858714280448 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1921 START: 1755572858714716416 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1921 END: 1755572858716623104 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1921 START: 1755572858716783360 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1921 END: 1755572858718794752 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1921 START: 1755572858718948352 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1921 END: 1755572858721529344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1921 START: 1755572858721600768 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1921 END: 1755572858721851904 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1921 START: 1755572858721993984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 END: 1755572858723613184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 START: 1755572858723762688 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1921 END: 1755572858725313536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1921 START: 1755572858725527040 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1921 END: 1755572858728336640 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1921 START: 1755572858728509696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1921 END: 1755572858730228224 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1921 START: 1755572858730406400 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1921 END: 1755572858730453760 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1921 START: 1755572858730477056 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1921 END: 1755572858730663168 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1921 START: 1755572858730797824 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 END: 1755572858732375296 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 START: 1755572858732524544 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1921 END: 1755572858734052864 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1921 START: 1755572858734288896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 END: 1755572858735853312 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 START: 1755572858735999488 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1921 END: 1755572858737613824 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1921 START: 1755572858737764608 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 END: 1755572858739376896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 START: 1755572858739523584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1921 END: 1755572858741041664 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1921 START: 1755572858741116160 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1921 END: 1755572858741174784 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1921 START: 1755572858741322496 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1921 END: 1755572858743197696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1921 START: 1755572858743343360 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1921 END: 1755572858745041920 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1921 START: 1755572858745292032 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 END: 1755572858746887424 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1921 START: 1755572858811965952 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1921 END: 1755572858813829632 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1921 END: 1755572858813902848 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250819T030736Z-P00000781/repo-20250819T030738Z-P00000781]

PID: 1981 START: 1755572859024699392 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250819T030738Z-P000007bd/repo-20250819T030739Z-P000007bd]

PID: 1981 START: 1755572859024910336 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1981 END: 1755572859025001728 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1981 START: 1755572859025042688 :: parsing /home/<USER>/.gitconfig

PID: 1981 END: 1755572859025123584 :: parsing /home/<USER>/.gitconfig

PID: 1981 START: 1755572859032875520 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755572859034561280 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755572859034757632 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1981 END: 1755572859036278272 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1981 START: 1755572859036573696 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755572859038114304 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755572859038289920 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755572859039865856 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755572859040041984 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755572859041710080 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755572859112340992 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1981 END: 1755572859402355968 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1981 START: 1755572859402481152 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 END: 1755572859402747648 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 START: 1755572859402819840 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 END: 1755572859402873344 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 START: 1755572859402903040 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 END: 1755572859402948352 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1981 START: 1755572859403166464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755572859404882688 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755572859469270784 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1981 END: 1755572859471248384 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1981 START: 1755572859471911168 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1981 END: 1755572859761143552 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1981 START: 1755572859761275392 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 END: 1755572859761493760 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 START: 1755572859762023680 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1981 END: 1755572859763982848 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1981 START: 1755572859764175104 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1981 END: 1755572859765836800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1981 START: 1755572859765985792 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1981 END: 1755572859807488000 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1981 START: 1755572859807629824 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 END: 1755572859807700224 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 START: 1755572859807891456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755572859809812480 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755572859809969152 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1981 END: 1755572859811608064 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1981 START: 1755572859812048128 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 END: 1755572859812143872 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 START: 1755572859812191744 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 END: 1755572859812236032 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1981 START: 1755572859814369280 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 END: 1755572859816160000 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1981 START: 1755572859913725440 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1981 END: 1755572859913805568 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1981 START: 1755572859914177024 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-19T03:07:39.914021+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-19T03:07:39.914021+00:00 1>| 2>|

PID: 1981 END: 1755572859916135168 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-19T03:07:39.914021+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-19T03:07:39.914021+00:00 1>| 2>|

PID: 1981 START: 1755572859916695808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1981 END: 1755572859918160384 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1981 END: 1755572859918231296 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250819T030738Z-P000007bd/repo-20250819T030739Z-P000007bd]

PID: 1916 START: 1755659135741449984 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250820T030533Z-P0000077c/repo-20250820T030535Z-P0000077c]

PID: 1916 START: 1755659135743374336 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1916 END: 1755659135744934400 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1916 START: 1755659135745317120 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1916 END: 1755659135746814720 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1916 START: 1755659135774514944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1916 END: 1755659136502512128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1916 START: 1755659136502957312 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1916 END: 1755659136504879104 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1916 START: 1755659136505034496 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1916 END: 1755659136506998784 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1916 START: 1755659136507142400 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1916 END: 1755659136509625344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1916 START: 1755659136509695488 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1916 END: 1755659136509944832 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1916 START: 1755659136510080768 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 END: 1755659136511696896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 START: 1755659136511845888 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1916 END: 1755659136513396224 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1916 START: 1755659136513658624 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1916 END: 1755659136516455424 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1916 START: 1755659136516666368 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1916 END: 1755659136518365952 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1916 START: 1755659136518532864 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1916 END: 1755659136518622720 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1916 START: 1755659136518659072 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1916 END: 1755659136518845440 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1916 START: 1755659136518975744 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 END: 1755659136520574464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 START: 1755659136520725504 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1916 END: 1755659136522310400 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1916 START: 1755659136522507520 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 END: 1755659136524133632 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 START: 1755659136524280576 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1916 END: 1755659136525842944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1916 START: 1755659136525990656 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 END: 1755659136527568128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 START: 1755659136527717888 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1916 END: 1755659136529238272 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1916 START: 1755659136529288192 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1916 END: 1755659136529339392 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1916 START: 1755659136529480192 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1916 END: 1755659136531397632 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1916 START: 1755659136531542528 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1916 END: 1755659136533299968 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1916 START: 1755659136533512704 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 END: 1755659136535136000 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1916 START: 1755659136598045184 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1916 END: 1755659136599932672 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1916 END: 1755659136600025600 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250820T030533Z-P0000077c/repo-20250820T030535Z-P0000077c]

PID: 1976 START: 1755659136808267264 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250820T030536Z-P000007b8/repo-20250820T030536Z-P000007b8]

PID: 1976 START: 1755659136808484352 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1976 END: 1755659136808609536 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1976 START: 1755659136808673536 :: parsing /home/<USER>/.gitconfig

PID: 1976 END: 1755659136808744704 :: parsing /home/<USER>/.gitconfig

PID: 1976 START: 1755659136815793664 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755659136817394688 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755659136817605376 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1976 END: 1755659136819061760 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1976 START: 1755659136819357696 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755659136820941824 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755659136821120256 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755659136822772736 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755659136822949632 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755659136824513024 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755659136894985216 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1976 END: 1755659137382605824 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1976 START: 1755659137382720256 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 END: 1755659137382987776 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 START: 1755659137383061504 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 END: 1755659137383114496 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 START: 1755659137383143680 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 END: 1755659137383188224 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1976 START: 1755659137383341056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755659137385043712 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755659137449595904 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1976 END: 1755659137451502592 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1976 START: 1755659137452209920 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1976 END: 1755659138083748864 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1976 START: 1755659138083874048 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1976 END: 1755659138084091136 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1976 START: 1755659138084664064 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1976 END: 1755659138086626304 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1976 START: 1755659138086780160 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1976 END: 1755659138088406016 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1976 START: 1755659138088583424 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1976 END: 1755659138129964288 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1976 START: 1755659138130105856 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1976 END: 1755659138130174464 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1976 START: 1755659138130366464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755659138132272640 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755659138132422656 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 END: 1755659138134002944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1976 START: 1755659138134436096 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1976 END: 1755659138134497536 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1976 START: 1755659138134537728 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1976 END: 1755659138134632960 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1976 START: 1755659138137310976 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 END: 1755659138139105536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1976 START: 1755659138237780480 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1976 END: 1755659138237853184 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1976 START: 1755659138238209792 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-20T03:05:38.238074+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-20T03:05:38.238074+00:00 1>| 2>|

PID: 1976 END: 1755659138240212224 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-20T03:05:38.238074+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-20T03:05:38.238074+00:00 1>| 2>|

PID: 1976 START: 1755659138240793088 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1976 END: 1755659138242263808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1976 END: 1755659138242333952 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250820T030536Z-P000007b8/repo-20250820T030536Z-P000007b8]

PID: 1913 START: 1755745476429427968 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250821T030434Z-P00000779/repo-20250821T030436Z-P00000779]

PID: 1913 START: 1755745476431072512 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1913 END: 1755745476432568576 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1913 START: 1755745476432922624 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1913 END: 1755745476434403328 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1913 START: 1755745476456883200 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1913 END: 1755745476924304128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1913 START: 1755745476924711680 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1913 END: 1755745476926556416 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1913 START: 1755745476926710528 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1913 END: 1755745476928645632 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1913 START: 1755745476928789248 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1913 END: 1755745476931322368 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1913 START: 1755745476931392000 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1913 END: 1755745476931637504 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1913 START: 1755745476931773440 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 END: 1755745476933421568 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 START: 1755745476933568256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1913 END: 1755745476935119872 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1913 START: 1755745476935326208 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1913 END: 1755745476938203648 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1913 START: 1755745476938368512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1913 END: 1755745476940243456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1913 START: 1755745476940418560 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1913 END: 1755745476940464384 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1913 START: 1755745476940487168 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1913 END: 1755745476940672512 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1913 START: 1755745476940804864 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 END: 1755745476942403072 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 START: 1755745476942550528 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1913 END: 1755745476944091136 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1913 START: 1755745476944288000 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 END: 1755745476945848832 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 START: 1755745476946000640 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1913 END: 1755745476947563008 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1913 START: 1755745476947709440 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 END: 1755745476949399808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 START: 1755745476949600768 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1913 END: 1755745476951418112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1913 START: 1755745476951469824 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1913 END: 1755745476951520000 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1913 START: 1755745476951661056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1913 END: 1755745476953515520 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1913 START: 1755745476953661696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1913 END: 1755745476955407104 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1913 START: 1755745476955619328 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 END: 1755745476957272320 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1913 START: 1755745477020454400 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1913 END: 1755745477022167808 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1913 END: 1755745477022239744 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250821T030434Z-P00000779/repo-20250821T030436Z-P00000779]

PID: 1974 START: 1755745477225888768 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250821T030437Z-P000007b6/repo-20250821T030437Z-P000007b6]

PID: 1974 START: 1755745477226081280 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1974 END: 1755745477226180608 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 1974 START: 1755745477226220544 :: parsing /home/<USER>/.gitconfig

PID: 1974 END: 1755745477226268160 :: parsing /home/<USER>/.gitconfig

PID: 1974 START: 1755745477232422912 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 END: 1755745477233985536 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 START: 1755745477234187264 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1974 END: 1755745477235652864 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 1974 START: 1755745477235940352 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 END: 1755745477237520896 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 START: 1755745477237692928 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 END: 1755745477239328768 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 START: 1755745477239502848 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 END: 1755745477241098496 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 START: 1755745477309936896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1974 END: 1755745477540781056 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1974 START: 1755745477540888576 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1974 END: 1755745477541194752 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1974 START: 1755745477541278720 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1974 END: 1755745477541339392 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1974 START: 1755745477541370624 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1974 END: 1755745477541415168 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1974 START: 1755745477541570560 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 END: 1755745477543264256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 START: 1755745477606241280 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1974 END: 1755745477607984640 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 1974 START: 1755745477608642304 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1974 END: 1755745477866051584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 1974 START: 1755745477866178048 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1974 END: 1755745477866395392 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1974 START: 1755745477866916608 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1974 END: 1755745477868835840 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 1974 START: 1755745477868999680 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1974 END: 1755745477870659840 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 1974 START: 1755745477870811648 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1974 END: 1755745477911919360 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1974 START: 1755745477912057856 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1974 END: 1755745477912133888 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1974 START: 1755745477912306944 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 END: 1755745477913999872 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 START: 1755745477914176512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1974 END: 1755745477915702016 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1974 START: 1755745477916148992 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1974 END: 1755745477916215296 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1974 START: 1755745477916254976 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1974 END: 1755745477916300032 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 1974 START: 1755745477918931712 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 END: 1755745477920672256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1974 START: 1755745478016457984 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1974 END: 1755745478016528128 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 1974 START: 1755745478016883456 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-21T03:04:38.016753+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-21T03:04:38.016753+00:00 1>| 2>|

PID: 1974 END: 1755745478018819584 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-21T03:04:38.016753+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-21T03:04:38.016753+00:00 1>| 2>|

PID: 1974 START: 1755745478019393792 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1974 END: 1755745478020821504 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1974 END: 1755745478020888064 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250821T030437Z-P000007b6/repo-20250821T030437Z-P000007b6]

PID: 1953 START: 1755831929728837632 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250822T030527Z-P000007a1/repo-20250822T030529Z-P000007a1]

PID: 1953 START: 1755831929730777600 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1953 END: 1755831929732446976 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 1953 START: 1755831929732835328 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1953 END: 1755831929734432000 :git command None ['git', 'config', '--file', '/home/<USER>/.gitconfig', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/.gitconfig --includes --null --list 1>| 2>|

PID: 1953 START: 1755831929757089280 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1953 END: 1755831929983598848 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: : export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 1953 START: 1755831929984015104 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1953 END: 1755831929985941248 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/main^0'] with debug: : git rev-parse --verify refs/remotes/origin/main^0 1>| 2>|

PID: 1953 START: 1755831929986106368 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1953 END: 1755831929988123648 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git update-ref --no-deref HEAD 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1953 START: 1755831929988275968 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1953 END: 1755831929990835200 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 1953 START: 1755831929990906112 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1953 END: 1755831929991160320 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1953 START: 1755831929991303168 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 END: 1755831929992930816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 START: 1755831929993081088 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1953 END: 1755831929994671104 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1953 START: 1755831929994886400 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1953 END: 1755831929997844992 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'checkout', '-q', '1ed30a827f1156dc831083e6cf999d6bd9deae18', '--'] with debug: : git checkout -q 1ed30a827f1156dc831083e6cf999d6bd9deae18 -- 2>|

PID: 1953 START: 1755831929998032128 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1953 END: 1755831929999889664 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 1953 START: 1755831930000081920 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1953 END: 1755831930000129792 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1953 START: 1755831930000153600 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1953 END: 1755831930000375296 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1953 START: 1755831930000524800 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 END: 1755831930002169088 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 START: 1755831930002398976 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1953 END: 1755831930004005632 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1953 START: 1755831930004211968 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 END: 1755831930005872384 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 START: 1755831930006019840 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1953 END: 1755831930007620352 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1953 START: 1755831930007774464 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 END: 1755831930009409536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 START: 1755831930009565696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1953 END: 1755831930011124480 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 1953 START: 1755831930011177216 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1953 END: 1755831930011230208 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 1953 START: 1755831930011439360 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1953 END: 1755831930013466112 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '1ed30a827f1156dc831083e6cf999d6bd9deae18'] with debug: : git update-ref refs/heads/default 1ed30a827f1156dc831083e6cf999d6bd9deae18 1>| 2>|

PID: 1953 START: 1755831930013617152 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1953 END: 1755831930015486208 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 1953 START: 1755831930015714816 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 END: 1755831930017383936 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 1953 START: 1755831930084406528 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1953 END: 1755831930086256640 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 1953 END: 1755831930086355712 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, --partial-clone, --no-use-superproject, -u, https://android.googlesource.com/platform/manifest, -b, main, --depth=1 [sid=repo-20250822T030527Z-P000007a1/repo-20250822T030529Z-P000007a1]

PID: 2016 START: 1755831930306377728 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250822T030530Z-P000007e0/repo-20250822T030530Z-P000007e0]

PID: 2016 START: 1755831930306602752 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 2016 END: 1755831930306695936 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config

PID: 2016 START: 1755831930306737664 :: parsing /home/<USER>/.gitconfig

PID: 2016 END: 1755831930306787328 :: parsing /home/<USER>/.gitconfig

PID: 2016 START: 1755831930314099456 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 END: 1755831930315784960 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 START: 1755831930315976448 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 2016 END: 1755831930317499648 :git command None ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 2016 START: 1755831930317800448 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 END: 1755831930319393024 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 START: 1755831930319580160 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 END: 1755831930321234432 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 START: 1755831930321456640 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 END: 1755831930323098112 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 START: 1755831930395809536 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2016 END: 1755831930706576384 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'origin', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/origin/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/objects
: git fetch --filter=blob:none --depth=1 --quiet origin --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/origin/main 1>| 2>&1

PID: 2016 START: 1755831930706686720 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2016 END: 1755831930706963456 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2016 START: 1755831930707037696 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2016 END: 1755831930707091200 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2016 START: 1755831930707121152 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2016 END: 1755831930707166720 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git

PID: 2016 START: 1755831930707325696 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 END: 1755831930709139712 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 START: 1755831930775561984 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2016 END: 1755831930777579008 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config --includes --null --list 1>| 2>|

PID: 2016 START: 1755831930778288896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 2016 END: 1755831931027821312 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'fetch', '--filter=blob:none', '--depth=1', '--quiet', 'aosp', '--prune', '--recurse-submodules=no', '--no-tags', '+refs/heads/main:refs/remotes/aosp/main'] with debug: 
: export GIT_DIR=/home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/work/adb-source-code/adb-source-code/.repo/project-objects/platform/packages/modules/adb.git/objects
: git fetch --filter=blob:none --depth=1 --quiet aosp --prune --recurse-submodules=no --no-tags +refs/heads/main:refs/remotes/aosp/main 1>| 2>&1

PID: 2016 START: 1755831931027947776 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2016 END: 1755831931028179712 :: load refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2016 START: 1755831931028761344 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 2016 END: 1755831931030779392 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--verify', 'refs/remotes/aosp/main^0'] with debug: : git rev-parse --verify refs/remotes/aosp/main^0 1>| 2>|

PID: 2016 START: 1755831931030944256 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 2016 END: 1755831931032689664 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'update-ref', '--no-deref', 'HEAD', '1cf2f017d312f73b3dc53bda85ef2610e35a80e9'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/packages/modules/adb
: git update-ref --no-deref HEAD 1cf2f017d312f73b3dc53bda85ef2610e35a80e9 1>| 2>|

PID: 2016 START: 1755831931032841472 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2016 END: 1755831931073807872 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 2016 START: 1755831931073951488 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2016 END: 1755831931074024960 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2016 START: 1755831931074216704 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 END: 1755831931076146432 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 START: 1755831931076319232 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2016 END: 1755831931078048512 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 2016 START: 1755831931078545408 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2016 END: 1755831931078612480 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2016 START: 1755831931078662400 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2016 END: 1755831931078710016 :: scan refs /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git

PID: 2016 START: 1755831931081448192 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 END: 1755831931083312896 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 2016 START: 1755831931183184896 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 2016 END: 1755831931183298048 :: parsing /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git/config

PID: 2016 START: 1755831931183658240 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-22T03:05:31.183519+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-22T03:05:31.183519+00:00 1>| 2>|

PID: 2016 END: 1755831931185640960 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--file', '/home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-22T03:05:31.183519+00:00'] with debug: : git config --file /home/<USER>/work/adb-source-code/adb-source-code/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-22T03:05:31.183519+00:00 1>| 2>|

PID: 2016 START: 1755831931186214400 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2016 END: 1755831931187771136 :git command /home/<USER>/work/adb-source-code/adb-source-code/.repo/projects/packages/modules/adb.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 2016 END: 1755831931187842560 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c, -j8, packages/modules/adb [sid=repo-20250822T030530Z-P000007e0/repo-20250822T030530Z-P000007e0]

