{"core.repositoryformatversion": ["1"], "core.filemode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "repo.depth": ["1"], "repo.partialclone": ["true"], "repo.clonefilter": ["blob:none"], "repo.superproject": ["false"], "repo.existingprojectcount": ["1"], "repo.newprojectcount": ["0"], "remote.origin.url": ["https://android.googlesource.com/platform/manifest"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "remote.origin.partialclonefilter": ["blob:none"], "manifest.platform": ["auto"], "extensions.preciousobjects": ["true"], "extensions.partialclone": ["origin"], "branch.default.remote": ["origin"], "branch.default.merge": ["refs/heads/main"], "repo.syncstate.main.synctime": ["2025-08-21T03:04:38.016753+00:00"], "repo.syncstate.main.version": ["1"], "repo.syncstate.sys.argv": ["['/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/main.py', '--repo-dir=/home/<USER>/work/adb-source-code/adb-source-code/.repo', '--wrapper-version=2.54', '--wrapper-path=/home/<USER>/work/adb-source-code/adb-source-code/repo', '--', 'sync', '-c', '-j8', 'packages/modules/adb']"], "repo.syncstate.options.jobs": ["8"], "repo.syncstate.options.outermanifest": ["true"], "repo.syncstate.options.jobsnetwork": ["8"], "repo.syncstate.options.jobscheckout": ["8"], "repo.syncstate.options.mpupdate": ["true"], "repo.syncstate.options.interleaved": ["true"], "repo.syncstate.options.currentbranchonly": ["true"], "repo.syncstate.options.clonebundle": ["false"], "repo.syncstate.options.retryfetches": ["0"], "repo.syncstate.options.prune": ["true"], "repo.syncstate.options.repoverify": ["true"], "repo.syncstate.options.quiet": ["false"], "repo.syncstate.options.verbose": ["false"], "repo.syncstate.repo.depth": ["1"], "repo.syncstate.repo.partialclone": ["true"], "repo.syncstate.repo.clonefilter": ["blob:none"], "repo.syncstate.repo.superproject": ["false"], "repo.syncstate.repo.existingprojectcount": ["1"], "repo.syncstate.repo.newprojectcount": ["0"], "repo.syncstate.remote.origin.url": ["https://android.googlesource.com/platform/manifest"], "repo.syncstate.remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "repo.syncstate.remote.origin.partialclonefilter": ["blob:none"], "repo.syncstate.branch.default.remote": ["origin"], "repo.syncstate.branch.default.merge": ["refs/heads/main"]}