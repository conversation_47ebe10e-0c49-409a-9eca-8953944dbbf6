[core]
	repositoryFormatVersion = 1
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[repo]
	depth = 1
	partialclone = true
	clonefilter = blob:none
	superproject = false
	existingprojectcount = 1
	newprojectcount = 0
[remote "origin"]
	url = https://android.googlesource.com/platform/manifest
	fetch = +refs/heads/*:refs/remotes/origin/*
	partialclonefilter = blob:none
[manifest]
	platform = auto
[extensions]
	preciousObjects = true
	partialclone = origin
[branch "default"]
	remote = origin
	merge = refs/heads/main
[repo "syncstate.main"]
	synctime = 2025-08-22T03:05:31.183519+00:00
	version = 1
[repo "syncstate.sys"]
	argv = ['/home/<USER>/work/adb-source-code/adb-source-code/.repo/repo/main.py', '--repo-dir=/home/<USER>/work/adb-source-code/adb-source-code/.repo', '--wrapper-version=2.54', '--wrapper-path=/home/<USER>/work/adb-source-code/adb-source-code/repo', '--', 'sync', '-c', '-j8', 'packages/modules/adb']
[repo "syncstate.options"]
	jobs = 8
	outermanifest = true
	jobsnetwork = 8
	jobscheckout = 8
	mpupdate = true
	interleaved = true
	currentbranchonly = true
	clonebundle = false
	retryfetches = 0
	prune = true
	repoverify = true
	quiet = false
	verbose = false
[repo "syncstate.repo"]
	depth = 1
	partialclone = true
	clonefilter = blob:none
	superproject = false
	existingprojectcount = 1
	newprojectcount = 0
[repo "syncstate.remote.origin"]
	url = https://android.googlesource.com/platform/manifest
	fetch = +refs/heads/*:refs/remotes/origin/*
	partialclonefilter = blob:none
[repo "syncstate.branch.default"]
	remote = origin
	merge = refs/heads/main
