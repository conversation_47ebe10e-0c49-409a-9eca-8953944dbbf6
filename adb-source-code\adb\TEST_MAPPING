{"adbd-mainline-presubmit": [{"name": "adbd_test"}], "mainline-presubmit": [{"name": "adbd_test[com.google.android.adbd.apex]"}], "presubmit": [{"name": "adbd_test"}, {"name": "adb_crypto_test"}, {"name": "adb_pairing_auth_test"}, {"name": "adb_pairing_connection_test"}, {"name": "adb_tls_connection_test"}, {"name": "FastDeployTests"}, {"name": "FastDeployHostTests"}, {"name": "MicrodroidHostTestCases"}], "hwasan-presubmit": [{"name": "adbd_test"}, {"name": "adb_crypto_test"}, {"name": "adb_pairing_auth_test"}, {"name": "adb_pairing_connection_test"}, {"name": "adb_tls_connection_test"}, {"name": "MicrodroidHostTestCases"}], "imports": [{"path": "frameworks/base/tests/StagedInstallTest"}]}