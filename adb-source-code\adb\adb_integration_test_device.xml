<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<configuration description="Config to run adb integration tests for device">
    <option name="test-suite-tag" value="adb_tests" />
    <option name="test-suite-tag" value="adb_integration" />
    <target_preparer class="com.android.tradefed.targetprep.SemaphoreTokenTargetPreparer">
        <option name="disable" value="false" />
    </target_preparer>

    <target_preparer class="com.android.tradefed.targetprep.adb.AdbStopServerPreparer" />
    <test class="com.android.tradefed.testtype.python.PythonBinaryHostTest" >
        <option name="par-file-name" value="adb_integration_test_device" />
        <option name="inject-android-serial" value="true" />
        <option name="test-timeout" value="2m" />
    </test>
</configuration>
