<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- This test config file is auto-generated. -->
<configuration description="Runs adbd_test.">
    <option name="test-suite-tag" value="apct" />
    <option name="test-suite-tag" value="apct-native" />
    <option name="config-descriptor:metadata" key="mainline-param" value="com.google.android.adbd.apex" />

    <target_preparer class="com.android.tradefed.targetprep.RootTargetPreparer">
    </target_preparer>

    <target_preparer class="com.android.tradefed.targetprep.PushFilePreparer">
        <option name="cleanup" value="true" />
        <option name="push" value="adbd_test->/data/local/tmp/adbd_test" />
    </target_preparer>

    <test class="com.android.tradefed.testtype.GTest" >
        <option name="native-test-device-path" value="/data/local/tmp" />
        <option name="module-name" value="adbd_test" />
    </test>

    <object type="module_controller" class="com.android.tradefed.testtype.suite.module.MainlineTestModuleController">
      <option name="mainline-module-package-name" value="com.google.android.adbd" />
    </object>
</configuration>
