package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "packages_modules_adb_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["packages_modules_adb_license"],
}

apex_defaults {
    name: "com.android.adbd-defaults",

    defaults: ["r-launched-dcla-enabled-apex-module"],

    binaries: ["adbd"],
    compile_multilib: "both",
    multilib: {
        both: {
            native_shared_libs: [
                "libadb_pairing_auth",
                "libadb_pairing_connection",
                "libadb_pairing_server",
                "libadbconnection_client",
            ],
        },
    },
    prebuilts: ["com.android.adbd.init.rc"],

    manifest: "apex_manifest.json",
    key: "com.android.adbd.key",
    certificate: ":com.android.adbd.certificate",
    compressible: true,
}

apex {
    name: "com.android.adbd",
    defaults: [
        "com.android.adbd-defaults",
    ],
}

// adbd apex with INT_MAX version code, to allow for upgrade/rollback testing.
apex_test {
    name: "test_com.android.adbd",
    defaults: ["com.android.adbd-defaults"],
    manifest: "test_apex_manifest.json",
    file_contexts: ":com.android.adbd-file_contexts",
    apex_available_name: "com.android.adbd",
    installable: false,
}

prebuilt_etc {
    name: "com.android.adbd.init.rc",
    src: "adbd.rc",
    filename: "init.rc",
    installable: false,
}

apex_key {
    name: "com.android.adbd.key",
    public_key: "com.android.adbd.avbpubkey",
    private_key: "com.android.adbd.pem",
}

android_app_certificate {
    name: "com.android.adbd.certificate",
    certificate: "com.android.adbd",
}
