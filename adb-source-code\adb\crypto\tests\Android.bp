//
// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

package {
    default_team: "trendy_team_native_tools_libraries",
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "packages_modules_adb_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["packages_modules_adb_license"],
}

cc_test {
    name: "adb_crypto_test",
    srcs: [
        "rsa_2048_key_test.cpp",
        "x509_generator_test.cpp",
    ],

    compile_multilib: "first",

    shared_libs: [
        "libbase",
        "libcrypto",
        "libcrypto_utils",
        "libprotobuf-cpp-lite",
    ],

    // Let's statically link them so we don't have to install it onto the
    // system image for testing.
    static_libs: [
        "libadb_crypto_static",
        "libadb_protos_static",
        "libadb_sysdeps",
    ],

    test_suites: [
        "general-tests",
    ],
}
