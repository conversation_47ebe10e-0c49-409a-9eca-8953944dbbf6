# ADB Developer documentation

- [Architecture](internals.md)
- [Debugging](debugging.md)
- [How root/unroot works](root.md)
- [Understanding asocket](asocket.md)
- [Trade-In Mode](adb_tradeinmode.md)
- [How ADB uses USB Zero-length packets](zero_length_packet.md)
- [How adbd starts](how_adbd_starts.md)
- [How burst mode works](delayed_ack.md)
- [How adbd and framework communicate](adbd_framework.md)
- [How ADB Wifi works](adb_wifi.md)
- [How ADB Incremental install works](incremental-install.md)
