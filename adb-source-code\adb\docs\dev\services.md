```
This file tries to document all requests a client can make
to the ADB server of an adbd daemon. See the OVERVIEW.TXT document
to understand what's going on here.

HOST SERVICES:

host:version
    Ask the ADB server for its internal version number.

host:kill
    Ask the ADB server to quit immediately. This is used when the
    ADB client detects that an obsolete server is running after an
    upgrade.

host:devices
host:devices-l
    Ask to return the list of available Android devices and their
    state. devices-l includes the device paths in the state.
    After the OKAY, this is followed by a 4-byte hex len,
    and a string that will be dumped as-is by the client, then
    the connection is closed

host:track-devices
host:track-devices-proto-binary
host:track-devices-proto-text
    These are variants of host:devices which doesn't close the
    connection. Instead, a new device list description is sent
    each time a device is added/removed or the state of a given
    device changes.
    Variant [-proto-binary] is binary protobuf format.
    Variant [-proto-text] is text protobuf format.

host:emulator:<port>
    This is a special query that is sent to the ADB server when a
    new emulator starts up. <port> is a decimal number corresponding
    to the emulator's ADB control port, i.e. the TCP port that the
    emulator will forward automatically to the adbd daemon running
    in the emulator system.

    This mechanism allows the ADB server to know when new emulator
    instances start.

host:transport:<serial-number>
    Ask to switch the connection to the device/emulator identified by
    <serial-number>. After the OKAY response, every client request will
    be sent directly to the adbd daemon running on the device.
    (Used to implement the -s option)

host:transport-usb
    Ask to switch the connection to one device connected through USB
    to the host machine. This will fail if there are more than one such
    devices. (Used to implement the -d convenience option)

host:transport-local
    Ask to switch the connection to one emulator connected through TCP.
    This will fail if there is more than one such emulator instance
    running. (Used to implement the -e convenience option)

host:transport-any
    Another host:transport variant. Ask to switch the connection to
    either the device or emulator connect to/running on the host.
    Will fail if there is more than one such device/emulator available.
    (Used when neither -s, -d or -e are provided)

host-serial:<serial-number>:<request>
    This is a special form of query, where the 'host-serial:<serial-number>:'
    prefix can be used to indicate that the client is asking the ADB server
    for information related to a specific device. <request> can be in one
    of the format described below.

host-usb:<request>
    A variant of host-serial used to target the single USB device connected
    to the host. This will fail if there is none or more than one.

host-local:<request>
    A variant of host-serial used to target the single emulator instance
    running on the host. This will fail if there is none or more than one.

host:<request>
    When asking for information related to a device, 'host:' can also be
    interpreted as 'any single device or emulator connected to/running on
    the host'.

host:server-status
    Return adb server status (version, build, usb backend, mdns backend, ...).
    See adb_host.proto AdbServerStatus for more details.

<host-prefix>:get-serialno
    Returns the serial number of the corresponding device/emulator.
    Note that emulator serial numbers are of the form "emulator-5554"

<host-prefix>:get-devpath
    Returns the device path of the corresponding device/emulator.

<host-prefix>:get-state
    Returns the state of a given device as a string.

<host-prefix>:forward:<local>;<remote>
    Asks the ADB server to forward local connections from <local>
    to the <remote> address on a given device.

    There, <host-prefix> can be one of the
    host-serial/host-usb/host-local/host prefixes as described previously
    and indicates which device/emulator to target.

    the format of <local> is one of:

        tcp:<port>      -> TCP connection on localhost:<port>
        local:<path>    -> Unix local domain socket on <path>

    the format of <remote> is one of:

        tcp:<port>      -> TCP localhost:<port> on device
        local:<path>    -> Unix local domain socket on device
        jdwp:<pid>      -> JDWP thread on VM process <pid>
        vsock:<CID>:<port> -> vsock on the given CID and port

    or even any one of the local services described below.

<host-prefix>:forward:norebind:<local>;<remote>
    Same as <host-prefix>:forward:<local>;<remote> except that it will
    fail it there is already a forward connection from <local>.

    Used to implement 'adb forward --no-rebind <local> <remote>'

<host-prefix>:killforward:<local>
    Remove any existing forward local connection from <local>.
    This is used to implement 'adb forward --remove <local>'

<host-prefix>:killforward-all
    Remove all forward network connections.
    This is used to implement 'adb forward --remove-all'.

<host-prefix>:list-forward
    List all existing forward connections from this server.
    This returns something that looks like the following:

       <hex4>: The length of the payload, as 4 hexadecimal chars.
       <payload>: A series of lines of the following format:

         <serial> " " <local> " " <remote> "\n"

    Where <serial> is a device serial number.
          <local>  is the host-specific endpoint (e.g. tcp:9000).
          <remote> is the device-specific endpoint.

    Used to implement 'adb forward --list'.

LOCAL SERVICES:

All the queries below assumed that you already switched the transport
to a real device, or that you have used a query prefix as described
above.

shell:command arg1 arg2 ...
    Run 'command arg1 arg2 ...' in a shell on the device, and return
    its output and error streams. Note that arguments must be separated
    by spaces. If an argument contains a space, it must be quoted with
    double-quotes. Arguments cannot contain double quotes or things
    will go very wrong.

    Note that this is the non-interactive version of "adb shell"

shell:
    Start an interactive shell session on the device. Redirect
    stdin/stdout/stderr as appropriate. Note that the ADB server uses
    this to implement "adb shell", but will also cook the input before
    sending it to the device (see interactive_shell() in commandline.c)

shell,v2: (API>=24)
    Variant of shell service which uses "shell protocol" in order to
    differentiate stdin, stderr, and also retrieve exit code.

exec:
    Variant of shell which uses a raw PTY in order to not mangle output.

abb: (API>=30)
    Direct connection to Binder on device. This service does not use space
    for parameter separator but "\u0000". Example:
    abb:package0install-create

abb_exec: (API>=30)
    Variant of abb. Use a raw PTY in order to not mangle output. Example:
    abb_exec:package0install-write

remount:
    Ask adbd to remount the device's filesystem in read-write mode,
    instead of read-only. This is usually necessary before performing
    an "adb sync" or "adb push" request.

    This request may not succeed on certain builds which do not allow
    that.

dev:<path>
    Opens a device file and connects the client directly to it for
    read/write purposes. Useful for debugging, but may require special
    privileges and thus may not run on all devices. <path> is a full
    path from the root of the filesystem. Besides debugging this is
    useful for allowing test automation running on host (not Android
    device) to directly interact with the device file when a
    non-trivial protocol is needed and adb shell is not suitable. Use
    cases: factory tests for device peripherals, emulated peripheral
    control (cuttlefish) for test automation.

dev-raw:<path>
    Similar with dev:<path>, the only difference being that the device
    is opened in raw tty mode. Useful when default tty settings
    interferes with protocol that is used to control the device.

tcp:<port>
    Tries to connect to tcp port <port> on localhost.

tcp:<port>:<server-name>
    Tries to connect to tcp port <port> on machine <server-name> from
    the device. This can be useful to debug some networking/proxy
    issues that can only be revealed on the device itself.

local:<path>
    Tries to connect to a Unix domain socket <path> on the device

localreserved:<path>
localabstract:<path>
localfilesystem:<path>
    Variants of local:<path> that are used to access other Android
    socket namespaces.

framebuffer:
    This service is used to send snapshots of the framebuffer to a client.
    It requires sufficient privileges but works as follow:

      After the OKAY, the service sends 16-byte binary structure
      containing the following fields (little-endian format):

            depth:   uint32_t:    framebuffer depth
            size:    uint32_t:    framebuffer size in bytes
            width:   uint32_t:    framebuffer width in pixels
            height:  uint32_t:    framebuffer height in pixels

      With the current implementation, depth is always 16, and
      size is always width*height*2

      Then, each time the client wants a snapshot, it should send
      one byte through the channel, which will trigger the service
      to send it 'size' bytes of framebuffer data.

      If the adbd daemon doesn't have sufficient privileges to open
      the framebuffer device, the connection is simply closed immediately.

jdwp:<pid>
    Connects to the JDWP thread running in the VM of process <pid>.

track-jdwp
    This is used to send the list of JDWP pids periodically to the client.
    The format of the returned data is the following:

        <hex4>:    the length of all content as a 4-char hexadecimal string
        <content>: a series of ASCII lines of the following format:
                        <pid> "\n"

    This service is used by DDMS to know which debuggable processes are running
    on the device/emulator.

    Note that there is no single-shot service to retrieve the list only once.

track-app:
    Improved version of "track-jdwp" service which also mentions whether the
    app is profileable and its architecture. Each time the list changes,
    a new messeage is sent (this service never stops).

    Each message features a hex4 length prefix followed by a
    binary protocol buffer. e.g.:

    process {
      pid: 18595
      debuggable: true
      architecture: "arm64"
    }
    process {
      pid: 18407
      debuggable: true
      profileable: true
      architecture: "arm64"
    }

    Note: Generate a parser from [app_processes.proto].

sync:
    This starts the file synchronization service, used to implement "adb push"
    and "adb pull". Since this service is pretty complex, it will be detailed
    in a companion document named SYNC.TXT

reverse:<forward-command>
    This implements the 'adb reverse' feature, i.e. the ability to reverse
    socket connections from a device to the host. <forward-command> is one
    of the forwarding commands that are described above, as in:

      list-forward
      forward:<local>;<remote>
      forward:norebind:<local>;<remote>
      killforward-all
      killforward:<local>

    Note that in this case, <local> corresponds to the socket on the device
    and <remote> corresponds to the socket on the host.

    The output of reverse:list-forward is the same as host:list-forward
    except that <serial> will be just 'host'.
```