//
// Copyright (C) 2018 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
package {
    default_team: "trendy_team_framework_android_packages",
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "packages_modules_adb_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["packages_modules_adb_license"],
}

java_library {
    name: "deployagent_lib",
    sdk_version: "24",
    srcs: [
        "deployagent/src/**/*.java",
        "proto/**/*.proto",
    ],
    proto: {
        type: "lite",
    },
}

java_binary {
    name: "deployagent",
    wrapper: "deployagent/deployagent.sh",
    sdk_version: "24",
    static_libs: [
        "deployagent_lib",
    ],
    dex_preopt: {
        enabled: false,
    },
}

android_test {
    name: "FastDeployTests",

    manifest: "AndroidManifest.xml",

    srcs: [
        "deployagent/test/com/android/fastdeploy/ApkArchiveTest.java",
    ],

    static_libs: [
        "androidx.test.core",
        "androidx.test.runner",
        "androidx.test.rules",
        "deployagent_lib",
        "mockito-target-inline-minus-junit4",
    ],

    libs: [
        "android.test.runner.stubs.system",
        "android.test.base.stubs.system",
        "android.test.mock.stubs.system",
    ],

    data: [
        "testdata/sample.apk",
        "testdata/sample.cd",
    ],

    optimize: {
        enabled: false,
    },

    test_config: "FastDeployTests.xml",

    test_suites: [
        "general-tests",
    ],
}

java_test_host {
    name: "FastDeployHostTests",
    srcs: [
        "deployagent/test/com/android/fastdeploy/FastDeployTest.java",
    ],
    data: [
        "testdata/helloworld5.apk",
        "testdata/helloworld7.apk",
        "testdata/sample.apk",
        "testdata/sample.cd",
    ],
    libs: [
        "compatibility-host-util",
        "cts-tradefed",
        "tradefed",
    ],
    test_suites: [
        "general-tests",
    ],
    test_config: "FastDeployHostDrivenTests.xml",
}
