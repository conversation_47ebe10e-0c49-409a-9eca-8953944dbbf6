// libadbconnection
// =========================================================
// libadbconnection_client/server implement the socket handling for jdwp
// forwarding and the track-jdwp service.
package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "packages_modules_adb_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["packages_modules_adb_license"],
}

cc_library {
    name: "libadbconnection_server",
    srcs: [
        "adbconnection_server.cpp",
        "common.cpp",
    ],

    export_include_dirs: ["include"],

    stl: "libc++_static",
    shared_libs: ["liblog"],
    static_libs: [
        "libbase",
        "libapp_processes_protos_lite",
        "libprotobuf-cpp-lite",
    ],

    defaults: [
        "adbd_defaults",
        "host_adbd_supported",
    ],

    // Avoid getting duplicate symbol of android::build::GetBuildNumber().
    use_version_lib: false,

    recovery_available: true,
    min_sdk_version: "30",
    apex_available: [
        "com.android.adbd",
        // TODO(b/151398197) remove the below
        "//apex_available:platform",
    ],
    compile_multilib: "both",
}

cc_library {
    name: "libadbconnection_client",
    srcs: [
        "adbconnection_client.cpp",
        "common.cpp",
    ],

    export_include_dirs: ["include"],

    stl: "libc++_static",
    shared_libs: ["liblog"],
    static_libs: [
        "libbase",
        "libapp_processes_protos_lite",
        "libprotobuf-cpp-lite",
    ],

    defaults: ["adbd_defaults"],
    visibility: [
        "//art:__subpackages__",
        "//packages/modules/adb/apex:__subpackages__",
    ],
    min_sdk_version: "30",
    apex_available: ["com.android.adbd"],

    // libadbconnection_client doesn't need an embedded build number.
    use_version_lib: false,

    target: {
        linux: {
            version_script: "libadbconnection_client.map.txt",
        },
        darwin: {
            enabled: false,
        },
    },
    stubs: {
        symbol_file: "libadbconnection_client.map.txt",
        versions: ["1"],
    },

    host_supported: true,
    compile_multilib: "both",
}

cc_test_host {
    name: "libadbconnection_test",
    srcs: ["tests.cc"],
     static_libs: [
         "libandroidfw",
         "libbase",
         "libadbconnection_client",
         "libadbconnection_server",
         "libapp_processes_protos_lite",
         "libprotobuf-cpp-lite",
         "liblog",
     ],
     target: {
        windows: {
            enabled: false,
        },
        darwin: {
            enabled: false,
        },
     }
}
