/*
 * Copyright (C) 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <sys/types.h>

#include "common.h"

#include <android-base/unique_fd.h>

#include <stdint.h>
#include <optional>

// Note this is NOT an apex interface as it's linked only into adbd.
void adbconnection_listen(void (*callback)(int fd, ProcessInfo process));

std::optional<ProcessInfo> readProcessInfoFromSocket(int socket);
