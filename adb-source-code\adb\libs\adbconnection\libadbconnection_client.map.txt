#
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

LIBADBCONNECTION_CLIENT_1 {
  global:
    adbconnection_client_new; # apex
    adbconnection_client_destroy; # apex
    adbconnection_client_pollfd; # apex
    adbconnection_client_receive_jdwp_fd; # apex
  local:
    *;
};

LIBADBCONNECTION_CLIENT_37 { # introduced=37
  global:
    adbconnection_client_set_current_process_name; # apex
    adbconnection_client_add_application; # apex
    adbconnection_client_remove_application; # apex
    adbconnection_client_set_waiting_for_debugger; # apex
    adbconnection_client_has_pending_update; # apex
    adbconnection_client_set_user_id; # apex
    adbconnection_client_send_update; # apex
  local:
    *;
};
