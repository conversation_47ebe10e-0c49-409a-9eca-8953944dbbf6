// libadbd_fs
// =========================================================
package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "packages_modules_adb_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["packages_modules_adb_license"],
}

cc_library {
    name: "libadbd_fs",
    defaults: ["adbd_defaults"],

    srcs: ["adbd_fs.cpp"],
    static_libs: [
        "libbase",
        "libcutils",
        "liblog",
    ],
    export_include_dirs: ["include"],

    version_script: "libadbd_fs.map.txt",
    stubs: {
        versions: ["1"],
        symbol_file: "libadbd_fs.map.txt",
    },

    host_supported: true,
    recovery_available: true,
    compile_multilib: "both",

    target: {
        darwin: {
            enabled: false,
        }
    },
    // This doesn't need to depend on libbuildversion.
    use_version_lib: false,
}
