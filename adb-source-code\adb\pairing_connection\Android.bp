// Copyright (C) 2020 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "packages_modules_adb_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["packages_modules_adb_license"],
}

cc_defaults {
    name: "libadb_pairing_connection_defaults",
    cflags: [
        "-Wall",
        "-Wextra",
        "-Wthread-safety",
        "-Werror",
    ],

    compile_multilib: "both",

    srcs: [
        "pairing_connection.cpp",
    ],
    target: {
        android: {
            version_script: "libadb_pairing_connection.map.txt",
        },
        windows: {
            compile_multilib: "first",
            enabled: true,
        },
    },
    export_include_dirs: ["include"],

    visibility: [
        "//art:__subpackages__",
        "//frameworks/base/services:__subpackages__",
        "//packages/modules/adb:__subpackages__",

        // This needs to be visible to minadbd, even though it's removed via exclude_shared_libs.
        "//bootable/recovery/minadbd:__subpackages__",
    ],
    apex_available: [
        "com.android.adbd",
    ],

    // libadb_pairing_connection doesn't need an embedded build number.
    use_version_lib: false,

    stl: "libc++_static",

    host_supported: true,
    recovery_available: false,

    static_libs: [
        "libbase",
        "libssl",
    ],
    shared_libs: [
        "libcrypto",
        "liblog",
        "libadb_pairing_auth",
    ],
}

cc_library {
    name: "libadb_pairing_connection",
    defaults: ["libadb_pairing_connection_defaults"],

    min_sdk_version: "30",
    apex_available: [
        "com.android.adbd",
    ],

    stubs: {
        symbol_file: "libadb_pairing_connection.map.txt",
        versions: ["30"],
    },

    static_libs: [
        "libadb_protos",
        // Statically link libadb_tls_connection because it is not
        // ABI-stable.
        "libadb_tls_connection",
        "libprotobuf-cpp-lite",
    ],
}

// For running atest (b/147158681)
cc_library_static {
    name: "libadb_pairing_connection_static",
    defaults: ["libadb_pairing_connection_defaults"],

    apex_available: [
        "//apex_available:platform",
    ],

    static_libs: [
        "libadb_protos_static",
        "libprotobuf-cpp-lite",
        "libadb_tls_connection_static",
    ],
}

cc_defaults {
    name: "libadb_pairing_server_defaults",
    cflags: [
        "-Wall",
        "-Wextra",
        "-Wthread-safety",
        "-Werror",
    ],

    compile_multilib: "both",

    srcs: [
        "pairing_server.cpp",
    ],
    target: {
        android: {
            version_script: "libadb_pairing_server.map.txt",
        },
    },
    export_include_dirs: ["include"],

    visibility: [
        "//art:__subpackages__",
        "//frameworks/base/services:__subpackages__",
        "//packages/modules/adb:__subpackages__",
    ],

    recovery_available: false,

    stl: "libc++_static",

    static_libs: [
        "libbase",
    ],
    shared_libs: [
        "libcrypto",
        "libcrypto_utils",
        "libcutils",
        "liblog",
        "libadb_pairing_auth",
        "libadb_pairing_connection",
    ],
}

cc_library {
    name: "libadb_pairing_server",
    defaults: ["libadb_pairing_server_defaults"],

    min_sdk_version: "30",
    apex_available: [
        "com.android.adbd",
    ],

    stubs: {
        symbol_file: "libadb_pairing_server.map.txt",
        versions: ["30"],
    },

    static_libs: [
        // Statically link libadb_crypto because it is not
        // ABI-stable.
        "libadb_crypto",
        "libadb_protos",
    ],
}

// For running atest (b/147158681)
cc_library_static {
    name: "libadb_pairing_server_static",
    defaults: ["libadb_pairing_server_defaults"],

    apex_available: [
        "//apex_available:platform",
    ],

    static_libs: [
        "libadb_crypto_static",
        "libadb_protos_static",
    ],
}
