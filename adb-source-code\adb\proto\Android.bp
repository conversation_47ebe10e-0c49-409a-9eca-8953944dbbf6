// Copyright (C) 2020 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "packages_modules_adb_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["packages_modules_adb_license"],
}

cc_defaults {
    name: "libadb_protos_defaults",
    cflags: [
        "-Wall",
        "-Wextra",
        "-Wthread-safety",
        "-Werror",
    ],

    compile_multilib: "both",

    proto: {
        export_proto_headers: true,
        type: "lite",
    },
    srcs: [
        "adb_known_hosts.proto",
        "key_type.proto",
        "pairing.proto",
    ],
    target: {
        windows: {
            compile_multilib: "first",
            enabled: true,
        },
    },

    visibility: [
        "//packages/modules/adb:__subpackages__",

        // This needs to be visible to minadbd, even though it's removed via exclude_shared_libs.
        "//bootable/recovery/minadbd:__subpackages__",
    ],

    stl: "libc++_static",

    host_supported: true,
    recovery_available: true,
}

cc_library {
    name: "libadb_protos",
    defaults: ["libadb_protos_defaults"],

    min_sdk_version: "30",
    apex_available: ["com.android.adbd"],
}

// For running atest (b/147158681)
cc_library_static {
    name: "libadb_protos_static",
    defaults: ["libadb_protos_defaults"],

    apex_available: [
        "//apex_available:platform",
    ],
}

cc_defaults {
    name: "libapp_processes_protos_defaults",
    cflags: [
        "-Wall",
        "-Wextra",
        "-Wthread-safety",
        "-Werror",
    ],

    compile_multilib: "both",

    srcs: [
        "app_processes.proto",
    ],
    target: {
        windows: {
            compile_multilib: "first",
            enabled: true,
        },
    },

    visibility: [
        "//packages/modules/adb:__subpackages__",

        // This needs to be visible to minadbd, even though it's removed via exclude_shared_libs.
        "//bootable/recovery/minadbd:__subpackages__",
    ],

    stl: "libc++_static",

    apex_available: ["com.android.adbd"],
}

cc_library {
    name: "libapp_processes_protos_lite",
    defaults: ["libapp_processes_protos_defaults"],

    min_sdk_version: "30",
    apex_available: ["//apex_available:platform"],

    proto: {
        export_proto_headers: true,
        type: "lite",
    },

    host_supported: true,
    recovery_available: true,
}

cc_library_host_static {
    name: "libapp_processes_protos_full",
    defaults: ["libapp_processes_protos_defaults"],

    proto: {
        export_proto_headers: true,
        type: "full",
    },
}

cc_defaults {
    name: "adb_host_protos_defaults",
    cflags: [
        "-Wall",
        "-Wextra",
        "-Wthread-safety",
        "-Werror",
    ],

    compile_multilib: "both",

    srcs: [
        ":adb_host_proto",
    ],
    target: {
        windows: {
            compile_multilib: "first",
            enabled: true,
        },
    },

    visibility: [
        "//packages/modules/adb:__subpackages__",
    ],

    stl: "libc++_static",

    apex_available: ["com.android.adbd"],
}

cc_library_host_static {
    name: "libadb_host_protos",
    defaults: ["adb_host_protos_defaults"],
    static_libs: [
        "libprotobuf-cpp-full",
    ],

    proto: {
        export_proto_headers: true,
        type: "full",
    },
}

filegroup {
    name: "adb_host_proto",
    srcs: [
        "adb_host.proto",
    ],
    visibility: [
        "//packages/modules/adb:__subpackages__",
        "//tools/asuite",
    ],
}
