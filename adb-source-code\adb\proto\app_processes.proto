/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

option java_package = "com.android.server.adb.protos";
option java_outer_classname = "AppProcessesProto";

package adb.proto;

message ProcessEntry {
    int64 pid = 1;
    bool debuggable = 2;
    bool profileable = 3;
    string architecture = 4;  // ISA name, e.g., "arm64"
    optional int64  user_id = 5;
    optional string process_name = 6;
    repeated string package_names = 7;
    optional bool waiting_for_debugger = 8;
    optional int64 uid = 9;
}

message AppProcesses {
  repeated ProcessEntry process = 1;
}
