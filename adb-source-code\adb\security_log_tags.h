/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef __SECURITY_LOG_TAGS_H
#define __SECURITY_LOG_TAGS_H

/* TODO: Automatically generate this file from the logtags file when build
 * infrastructure is in place.
 * Defined in frameworks/base/core/java/android/auditing/SecurityLog.logtags
 */
#define SEC_TAG_ADB_SHELL_INTERACTIVE 210001
#define SEC_TAG_ADB_SHELL_CMD         210002
#define SEC_TAG_ADB_RECV_FILE         210003
#define SEC_TAG_ADB_SEND_FILE         210004

#endif
