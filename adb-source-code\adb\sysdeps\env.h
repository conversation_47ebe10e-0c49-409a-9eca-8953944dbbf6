/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <optional>
#include <string>

namespace adb {
namespace sysdeps {

// Attempts to retrieve the environment variable value for |var|. Returns std::nullopt
// if unset.
std::optional<std::string> GetEnvironmentVariableUTF8(std::string_view var);

// Gets the host name of the system. Returns empty string on failure.
std::string GetHostNameUTF8();
// Gets the current login user. Returns empty string on failure.
std::string GetLoginNameUTF8();

}  // namespace sysdeps
}  // namespace adb
