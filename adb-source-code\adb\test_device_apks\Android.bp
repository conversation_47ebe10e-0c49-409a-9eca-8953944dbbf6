// Copyright (C) 2024 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

android_test {
    name: "adb_test_app1",
    srcs: [
        "test_app1/src/**/*.java",
    ],
    manifest: "test_app1/AndroidManifest.xml",
    certificate: "platform",
    sdk_version: "current",
}

android_test {
    name: "adb_test_app2",
    srcs: [
        "test_app2/src/**/*.java",
    ],
    manifest: "test_app2/AndroidManifest.xml",
    certificate: "platform",
    sdk_version: "current",
}
