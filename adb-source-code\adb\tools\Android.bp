// Copyright (C) 2017 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "packages_modules_adb_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["packages_modules_adb_license"],
}

cc_binary_host {
    name: "check_ms_os_desc",

    defaults: ["adb_defaults"],

    srcs: [
        "check_ms_os_desc.cpp",
    ],

    static_libs: [
        "libbase",
        "libusb",
    ],

    stl: "libc++_static",

    dist: {
        targets: [
            "sdk",
        ],
    },
}

cc_binary_host {
    name: "adb_usbreset",

    defaults: ["adb_defaults"],

    srcs: [
        "adb_usbreset.cpp",
    ],

    static_libs: [
        "libbase",
        "libusb",
    ],

    stl: "libc++_static",

    dist: {
        targets: [
            "sdk",
        ],
    },
}
