#!/bin/sh

# cleanup
rm -fr packages repo adb

# install repo tool
curl https://storage.googleapis.com/git-repo-downloads/repo > repo
chmod a+x repo

# config
git config --global user.name "Github Bot"
git config --global user.email "<EMAIL>"

# clone
./repo init \
  --partial-clone --no-use-superproject \
  -u https://android.googlesource.com/platform/manifest \
  -b main \
  --depth=1
./repo sync -c -j8 packages/modules/adb
mv packages/modules/adb ./adb
rm -fr packages

# commit
git add .

