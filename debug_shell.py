#!/usr/bin/env python3
"""
Debug script to test shell service functionality step by step.
"""

import logging
import sys
from pyadb import AdbTcpConnection, AdbShellService

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    print("🔧 PyADB Shell Service Debug")
    print("=" * 40)
    
    try:
        # Connect to emulator
        print("📡 Connecting to emulator...")
        connection = AdbTcpConnection('localhost', 5555, timeout=10.0)
        
        if not connection.connect():
            print("❌ Failed to connect to emulator")
            return 1
        
        print("✅ Connected successfully")
        
        # Create shell service
        print("\n🐚 Creating shell service...")
        shell = AdbShellService(connection, connection.features)
        
        print(f"   Features: {shell.features}")
        print(f"   Shell v2 support: {shell.supports_shell2}")
        print(f"   CMD support: {shell.supports_cmd}")
        
        # Test simple command
        print("\n📋 Testing simple command...")
        try:
            result = shell.execute_command('echo "Hello PyADB"')
            print(f"   Exit code: {result.exit_code}")
            print(f"   Stdout: {result.stdout}")
            print(f"   Stderr: {result.stderr}")
        except Exception as e:
            print(f"   ❌ Command failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Test whoami
        print("\n👤 Testing whoami...")
        try:
            result = shell.execute_command('whoami')
            print(f"   Exit code: {result.exit_code}")
            print(f"   User: {result.stdout.decode().strip()}")
        except Exception as e:
            print(f"   ❌ Whoami failed: {e}")
        
        # Test properties
        print("\n🔧 Testing properties...")
        try:
            properties = shell.get_properties()
            print(f"   Found {len(properties)} properties")
            for key in sorted(properties.keys())[:5]:  # Show first 5
                print(f"   {key}: {properties[key]}")
        except Exception as e:
            print(f"   ❌ Properties failed: {e}")
        
        # Test device info
        print("\n📱 Testing device info...")
        try:
            info = shell.get_device_info()
            print(f"   Device info: {info}")
        except Exception as e:
            print(f"   ❌ Device info failed: {e}")
        
        print("\n✅ Debug completed")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        try:
            connection.disconnect()
        except:
            pass
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
