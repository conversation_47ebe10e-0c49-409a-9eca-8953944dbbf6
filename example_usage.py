#!/usr/bin/env python3
"""
PyADB Usage Examples

This script demonstrates how to use all the PyADB features including:
- Device connection (USB and TCP)
- Shell command execution
- File pull/push operations
- Root functionality
- Remount functionality
"""

import os
import sys
import time
import logging
from pathlib import Path

# Add pyadb to path
sys.path.insert(0, str(Path(__file__).parent))

from pyadb import (
    AdbTcpConnection, AdbUsbConnection, AdbShellService,
    AdbPullService, AdbPushService, AdbRootService, AdbRemountService,
    find_usb_devices, create_tcp_connection, create_usb_connection
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def connect_to_device():
    """Try to connect to any available device"""
    print("🔍 Looking for devices...")
    
    # Try USB first
    try:
        devices = find_usb_devices()
        if devices:
            print(f"📱 Found {len(devices)} USB device(s)")
            device = devices[0]
            print(f"   Connecting to: {device}")
            
            conn = create_usb_connection(device)
            if conn.connect():
                print("✅ USB connection established")
                return conn
    except ImportError:
        print("⚠️  USB support not available (install PyUSB)")
    except Exception as e:
        print(f"❌ USB connection failed: {e}")
    
    # Try TCP/IP (emulator)
    for port in [5555, 5557, 5559, 5561]:
        try:
            print(f"🌐 Trying TCP connection to localhost:{port}")
            conn = create_tcp_connection('localhost', port, timeout=3.0)
            if conn.connect():
                print(f"✅ TCP connection established to localhost:{port}")
                return conn
        except Exception as e:
            print(f"   Failed: {e}")
    
    print("❌ No devices found")
    print("💡 Make sure:")
    print("   - An Android device is connected via USB with debugging enabled")
    print("   - Or an Android emulator is running")
    return None


def demonstrate_shell_commands(connection):
    """Demonstrate shell command execution"""
    print("\n🐚 Shell Command Examples")
    print("-" * 40)
    
    shell = AdbShellService(connection)
    
    # Basic command
    print("📋 Getting device info...")
    info = shell.get_device_info()
    for key, value in info.items():
        print(f"   {key}: {value}")
    
    # Check current user
    print("\n👤 Current user:")
    result = shell.execute_command('whoami')
    print(f"   {result.stdout.decode().strip()}")
    
    # List some files
    print("\n📁 Files in /system:")
    result = shell.execute_command('ls -la /system | head -10')
    for line in result.stdout.decode().strip().split('\n'):
        if line.strip():
            print(f"   {line}")
    
    # System info
    print("\n💾 Storage info:")
    result = shell.execute_command('df -h /system /data')
    for line in result.stdout.decode().strip().split('\n'):
        if line.strip():
            print(f"   {line}")


def demonstrate_file_operations(connection):
    """Demonstrate file pull/push operations"""
    print("\n📁 File Transfer Examples")
    print("-" * 40)
    
    pull_service = AdbPullService(connection)
    push_service = AdbPushService(connection)
    
    # Pull a file
    remote_file = "/system/build.prop"
    local_file = "build.prop.pulled"
    
    print(f"⬇️  Pulling {remote_file}...")
    
    def progress_callback(bytes_transferred, total_bytes):
        percent = (bytes_transferred / total_bytes) * 100
        print(f"   Progress: {bytes_transferred}/{total_bytes} bytes ({percent:.1f}%)")
    
    if pull_service.pull_file(remote_file, local_file, progress_callback):
        print(f"✅ Successfully pulled to {local_file}")
        
        # Show file info
        if os.path.exists(local_file):
            size = os.path.getsize(local_file)
            print(f"   File size: {size} bytes")
            
            # Show first few lines
            print("   First few lines:")
            with open(local_file, 'r', encoding='utf-8', errors='ignore') as f:
                for i, line in enumerate(f):
                    if i >= 5:
                        break
                    print(f"     {line.strip()}")
        
        # Push it back to a different location
        remote_test_file = "/data/local/tmp/test_build.prop"
        print(f"\n⬆️  Pushing to {remote_test_file}...")
        
        if push_service.push_file(local_file, remote_test_file):
            print("✅ Successfully pushed file")
            
            # Verify it exists
            shell = AdbShellService(connection)
            result = shell.execute_command(f'ls -la {remote_test_file}')
            if result.exit_code == 0:
                print(f"   Verified: {result.stdout.decode().strip()}")
            
            # Clean up remote file
            shell.execute_command(f'rm {remote_test_file}')
        else:
            print("❌ Push failed")
        
        # Clean up local file
        os.remove(local_file)
    else:
        print("❌ Pull failed")


def demonstrate_root_functionality(connection):
    """Demonstrate root functionality"""
    print("\n🔐 Root Access Examples")
    print("-" * 40)
    
    root_service = AdbRootService(connection)
    
    # Check current root status
    print("🔍 Checking current root status...")
    is_root = root_service.check_root_status()
    if is_root is not None:
        status = "root" if is_root else "non-root"
        print(f"   Current status: {status}")
    else:
        print("   Unable to determine root status")
    
    # Try to get root (this will only work on debuggable builds)
    print("\n🚀 Attempting to get root access...")
    print("   Note: This only works on debuggable builds (emulators, rooted devices)")
    
    if root_service.enable_root():
        print("✅ Root command sent successfully")
        print("   Device may restart - this is normal")
        
        # Wait a bit for potential restart
        print("   Waiting for potential device restart...")
        time.sleep(3)
        
        # Try to reconnect
        if connection.connect():
            print("   Reconnected successfully")
            
            # Check new status
            new_status = root_service.check_root_status()
            if new_status is not None:
                status = "root" if new_status else "non-root"
                print(f"   New status: {status}")
                
                if new_status:
                    print("🎉 Root access obtained!")
                else:
                    print("ℹ️  Root access not obtained (normal for production devices)")
        else:
            print("   Could not reconnect after root attempt")
    else:
        print("❌ Root command failed")


def demonstrate_remount_functionality(connection):
    """Demonstrate remount functionality"""
    print("\n🔧 Remount Examples")
    print("-" * 40)
    
    remount_service = AdbRemountService(connection)
    
    # Check current mount status
    print("🔍 Checking current mount status...")
    mount_info = remount_service.check_mount_status()
    if mount_info:
        print("   System partitions:")
        for mount in mount_info:
            readonly = "RO" if mount['readonly'] else "RW"
            print(f"     {mount['mountpoint']}: {readonly} ({mount['filesystem']})")
    
    # Check if system is writable
    print("\n✏️  Checking system writability...")
    is_writable = remount_service.is_system_writable()
    if is_writable is not None:
        status = "writable" if is_writable else "read-only"
        print(f"   System partition: {status}")
    
    # Try remount (requires root)
    print("\n🔄 Attempting to remount system as read-write...")
    print("   Note: This requires root access")
    
    if remount_service.remount_system():
        print("✅ Remount command sent successfully")
        
        # Check new writability
        time.sleep(1)
        new_writable = remount_service.is_system_writable()
        if new_writable is not None:
            status = "writable" if new_writable else "read-only"
            print(f"   New system status: {status}")
            
            if new_writable:
                print("🎉 System is now writable!")
            else:
                print("ℹ️  System is still read-only (normal without root)")
    else:
        print("❌ Remount command failed")


def main():
    """Main demonstration function"""
    print("🚀 PyADB Feature Demonstration")
    print("=" * 50)
    
    # Connect to device
    connection = connect_to_device()
    if not connection:
        print("\n💡 To test PyADB features:")
        print("   1. Connect an Android device via USB with debugging enabled")
        print("   2. Or start an Android emulator")
        print("   3. Run this script again")
        return 1
    
    try:
        # Demonstrate all features
        demonstrate_shell_commands(connection)
        demonstrate_file_operations(connection)
        demonstrate_root_functionality(connection)
        demonstrate_remount_functionality(connection)
        
        print("\n" + "=" * 50)
        print("🎉 PyADB demonstration completed!")
        print("\n📚 Available services:")
        print("   • AdbShellService - Execute shell commands")
        print("   • AdbPullService - Download files from device")
        print("   • AdbPushService - Upload files to device")
        print("   • AdbRootService - Manage root access")
        print("   • AdbRemountService - Remount system partitions")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demonstration interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        if connection:
            connection.disconnect()
            print("\n🔌 Disconnected from device")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
