"""
PyADB - Pure Python ADB Implementation

A pure Python implementation of the Android Debug Bridge (ADB) protocol
for communicating with Android devices over USB and TCP/IP connections.

This library provides:
- Core ADB protocol implementation
- USB and TCP/IP transport layers
- WiFi device discovery and pairing
- Shell command execution
- Device management utilities

Example usage:
    from pyadb import AdbTcpConnection, AdbShellService

    # Connect via TCP/IP
    with AdbTcpConnection('*************', 5555) as conn:
        shell = AdbShellService(conn)
        result = shell.execute_command('ls -la')
        print(result.stdout.decode())
"""

__version__ = "0.1.0"
__author__ = "PyADB Contributors"
__license__ = "Apache 2.0"

# Core protocol components
from .core import (
    AdbMessage,
    AdbPacket,
    calculate_checksum,
    create_message,
    create_packet,
    create_connect_packet,
    create_auth_packet,
    create_open_packet,
    create_ready_packet,
    create_write_packet,
    create_close_packet,
)

# Constants and enums
from .const import (
    # Protocol commands
    A_<PERSON>YNC, A_CNXN, A_OPEN, A_OKAY, A_<PERSON><PERSON><PERSON>, A_WRTE, A_AUTH, A_STLS,

    # Protocol versions
    A_VERSION, A_VERSION_MIN, A_VERSION_SKIP_CHECKSUM,

    # Payload limits
    MAX_PAYLOAD, MAX_PAYLOAD_V1,

    # Enums
    TransportType,
    ConnectionState,

    # Helper functions
    command_to_string,
    connection_state_is_online,

    # Feature constants
    FEATURE_SHELL2, FEATURE_CMD, FEATURE_STAT2, FEATURE_LS2,
    DEFAULT_FEATURES,
)

# Connection implementations
from .conn_wifi import AdbTcpConnection
from .conn_usb import AdbUsbConnection, UsbDevice

# Shell service
from .shell import (
    AdbShellService,
    ShellResult,
    ShellProtocolPacket,
    ShellProtocolId,
)

# File sync services
from .pull import AdbPullService
from .push import AdbPushService

# System services
from .root import AdbRootService
from .remount import AdbRemountService

# WiFi discovery and pairing
from .wifi_discovery import (
    AdbWifiManager,
    DiscoveredDevice,
    discover_wifi_devices,
    pair_wifi_device,
    connect_wifi_device,
    connect_wifi_address,
    get_wifi_manager,
)
from .pairing import PairingResult
from .device_registry import DeviceInfo
from .auth import get_key_manager
from .device_registry import get_device_registry
from .port_forward import AdbPortForwarder, ForwardResult, ForwardType
from .port_forward_service import AdbPortForwardService

# Convenience imports for common use cases
__all__ = [
    # Core classes
    'AdbMessage',
    'AdbPacket',
    'AdbTcpConnection',
    'AdbUsbConnection',
    'AdbShellService',

    # File sync services
    'AdbPullService',
    'AdbPushService',

    # System services
    'AdbRootService',
    'AdbRemountService',

    # WiFi discovery and pairing
    'AdbWifiManager',
    'DiscoveredDevice',
    'PairingResult',
    'DeviceInfo',
    'discover_wifi_devices',
    'pair_wifi_device',
    'connect_wifi_device',
    'connect_wifi_address',
    'get_wifi_manager',
    'get_key_manager',
    'get_device_registry',

    # Port forwarding
    'AdbPortForwarder',
    'AdbPortForwardService',
    'ForwardResult',
    'ForwardType',

    # Data classes
    'ShellResult',
    'UsbDevice',

    # Enums
    'TransportType',
    'ConnectionState',
    'ShellProtocolId',

    # Protocol functions
    'create_connect_packet',
    'create_open_packet',
    'create_write_packet',
    'create_close_packet',

    # Utility functions
    'command_to_string',
    'connection_state_is_online',

    # Constants
    'A_VERSION',
    'MAX_PAYLOAD',
    'DEFAULT_FEATURES',
]


def get_version() -> str:
    """Get the PyADB version string"""
    return __version__


def create_tcp_connection(host: str = 'localhost', port: int = 5555,
                         timeout: float = 10.0) -> AdbTcpConnection:
    """
    Create a TCP/IP ADB connection.

    Args:
        host: Target host address
        port: Target port number
        timeout: Connection timeout in seconds

    Returns:
        AdbTcpConnection instance
    """
    return AdbTcpConnection(host, port, timeout)


def create_usb_connection(device: UsbDevice = None,
                         timeout: int = 5000) -> AdbUsbConnection:
    """
    Create a USB ADB connection.

    Args:
        device: Specific USB device (None for auto-detect)
        timeout: USB transfer timeout in milliseconds

    Returns:
        AdbUsbConnection instance
    """
    return AdbUsbConnection(device, timeout)


def find_usb_devices() -> list:
    """
    Find all USB devices that support ADB.

    Returns:
        List of UsbDevice objects
    """
    return AdbUsbConnection.find_adb_devices()


def create_wifi_connection(host: str, port: int = 5555, use_tls: bool = False,
                          timeout: float = 10.0) -> AdbTcpConnection:
    """
    Create a WiFi ADB connection with optional TLS encryption.

    Args:
        host: Target host address
        port: Target port number (default: 5555)
        use_tls: Whether to use TLS encryption
        timeout: Connection timeout in seconds

    Returns:
        AdbTcpConnection instance
    """
    return AdbTcpConnection(host, port, timeout, use_tls)


def discover_and_connect_wifi(timeout: float = 10.0) -> list:
    """
    Discover WiFi ADB devices and return connection-ready information.

    Args:
        timeout: Discovery timeout in seconds

    Returns:
        List of DiscoveredDevice objects
    """
    return discover_wifi_devices(timeout)


def pair_and_connect_wifi(ip_address: str, password: str,
                         use_tls: bool = True) -> tuple:
    """
    Pair with a WiFi device and establish connection.

    Args:
        ip_address: Device IP address
        password: Pairing password (6-digit code)
        use_tls: Whether to use TLS for connection

    Returns:
        Tuple of (PairingResult, AdbTcpConnection or None)
    """
    # First pair the device
    pairing_result = pair_wifi_device(ip_address, password)

    if not pairing_result.success:
        return pairing_result, None

    # Then connect to it
    connection = connect_wifi_device(pairing_result.device_guid)

    return pairing_result, connection