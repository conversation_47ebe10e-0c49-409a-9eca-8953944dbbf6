"""
ADB Socket Implementation

This module implements ADB socket functionality for port forwarding and service connections.
It provides a socket-like interface that communicates via the ADB protocol.
"""

import socket
import threading
import queue
import struct
import time
from typing import Optional, Callable, Any, Dict, Tuple
import logging

from typing import Union
from .core import AdbPacket, AdbMessage, create_packet
from .const import A_OPEN, A_OKAY, A_CLSE, A_WRTE

# Type alias for ADB connections
AdbConnection = Union['AdbTcpConnection', 'AdbUsbConnection']

logger = logging.getLogger(__name__)


class AdbSocketError(Exception):
    """Exception raised by ADB socket operations"""
    pass


class AdbSocket:
    """
    Socket-like interface for ADB connections
    
    This class provides a socket-like interface that communicates with
    services on the Android device via the ADB protocol.
    """
    
    def __init__(self, connection: AdbConnection, destination: str):
        """
        Initialize ADB socket
        
        Args:
            connection: ADB connection to use
            destination: Service destination (e.g., "tcp:8080", "shell:")
        """
        self.connection = connection
        self.destination = destination
        self.local_id = self._generate_local_id()
        self.remote_id: Optional[int] = None
        self.connected = False
        self.closed = False
        
        # Data queues for socket-like interface
        self._recv_queue: queue.Queue = queue.Queue()
        self._recv_buffer = b''
        self._send_lock = threading.Lock()
        self._recv_lock = threading.Lock()
        
        # Connection state
        self._connect_event = threading.Event()
        self._close_event = threading.Event()
        
        logger.debug(f"Created ADB socket {self.local_id} for {destination}")
    
    def connect(self, timeout: float = 10.0) -> bool:
        """
        Connect to the service on the device
        
        Args:
            timeout: Connection timeout in seconds
            
        Returns:
            True if connected successfully
        """
        if self.connected:
            return True
        
        if self.closed:
            raise AdbSocketError("Socket is closed")
        
        logger.debug(f"Connecting ADB socket {self.local_id} to {self.destination}")
        
        try:
            # Payload is destination with null terminator
            payload = self.destination.encode('utf-8') + b'\x00'

            # Create A_OPEN packet using helper function
            packet = create_packet(A_OPEN, self.local_id, 0, payload)
            
            # Send the packet
            if not self.connection.send_packet(packet):
                return False
            
            # Wait for response
            if self._connect_event.wait(timeout):
                return self.connected
            else:
                logger.error(f"Connection timeout for socket {self.local_id}")
                return False
        
        except Exception as e:
            logger.error(f"Connection error for socket {self.local_id}: {e}")
            return False
    
    def send(self, data: bytes) -> int:
        """
        Send data through the socket
        
        Args:
            data: Data to send
            
        Returns:
            Number of bytes sent
        """
        if not self.connected:
            raise AdbSocketError("Socket not connected")
        
        if self.closed:
            raise AdbSocketError("Socket is closed")
        
        with self._send_lock:
            try:
                # Create A_WRTE packet using helper function
                packet = create_packet(A_WRTE, self.local_id, self.remote_id, data)
                
                if self.connection.send_packet(packet):
                    logger.debug(f"Sent {len(data)} bytes via socket {self.local_id}")
                    return len(data)
                else:
                    raise AdbSocketError("Failed to send packet")
            
            except Exception as e:
                logger.error(f"Send error for socket {self.local_id}: {e}")
                raise AdbSocketError(f"Send failed: {e}")
    
    def sendall(self, data: bytes) -> None:
        """
        Send all data through the socket
        
        Args:
            data: Data to send
        """
        total_sent = 0
        while total_sent < len(data):
            sent = self.send(data[total_sent:])
            total_sent += sent
    
    def recv(self, bufsize: int, timeout: float = 1.0) -> bytes:
        """
        Receive data from the socket

        Args:
            bufsize: Maximum number of bytes to receive
            timeout: Timeout in seconds (default: 1.0)

        Returns:
            Received data
        """
        if self.closed:
            return b''

        with self._recv_lock:
            # Return data from buffer if available
            if self._recv_buffer:
                data = self._recv_buffer[:bufsize]
                self._recv_buffer = self._recv_buffer[bufsize:]
                return data

            # Wait for new data
            try:
                data = self._recv_queue.get(timeout=timeout)
                if len(data) <= bufsize:
                    return data
                else:
                    # Split data if larger than requested
                    self._recv_buffer = data[bufsize:]
                    return data[:bufsize]

            except queue.Empty:
                return b''
    
    def close(self) -> None:
        """Close the socket"""
        if self.closed:
            return
        
        logger.debug(f"Closing ADB socket {self.local_id}")
        
        self.closed = True
        self._close_event.set()
        
        if self.connected and self.remote_id is not None:
            try:
                # Send A_CLSE packet using helper function
                packet = create_packet(A_CLSE, self.local_id, self.remote_id)
                self.connection.send_packet(packet)
            
            except Exception as e:
                logger.debug(f"Error sending close packet: {e}")
        
        self.connected = False
    
    def settimeout(self, timeout: Optional[float]) -> None:
        """Set socket timeout (for compatibility)"""
        # ADB sockets don't have traditional timeouts
        pass
    
    def getsockname(self) -> Tuple[str, int]:
        """Get socket name (for compatibility)"""
        return ("adb", self.local_id)
    
    def getpeername(self) -> Tuple[str, int]:
        """Get peer name (for compatibility)"""
        return (self.destination, self.remote_id or 0)
    
    def handle_packet(self, packet: AdbPacket) -> None:
        """
        Handle incoming ADB packet for this socket
        
        Args:
            packet: Received ADB packet
        """
        message = packet.message
        
        if message.command == A_OKAY:
            # Connection established
            self.remote_id = message.arg0
            self.connected = True
            self._connect_event.set()
            logger.debug(f"Socket {self.local_id} connected (remote_id: {self.remote_id})")
        
        elif message.command == A_WRTE:
            # Data received
            if packet.payload:
                self._recv_queue.put(packet.payload)
                logger.debug(f"Socket {self.local_id} received {len(packet.payload)} bytes")
            
            # Send ACK using helper function
            try:
                ack_packet = create_packet(A_OKAY, self.local_id, self.remote_id)
                self.connection.send_packet(ack_packet)
            except Exception as e:
                logger.error(f"Failed to send ACK: {e}")
        
        elif message.command == A_CLSE:
            # Connection closed
            logger.debug(f"Socket {self.local_id} closed by remote")
            self.connected = False
            self.closed = True
            self._close_event.set()
        
        else:
            logger.warning(f"Unexpected packet for socket {self.local_id}: {message.command}")
    
    @staticmethod
    def _generate_local_id() -> int:
        """Generate a unique local socket ID"""
        # Simple ID generation - in practice, this should be more sophisticated
        import random
        return random.randint(1, 0xFFFFFFFF)
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()


class AdbSocketManager:
    """
    Manager for ADB sockets
    
    This class manages multiple ADB sockets and routes incoming packets
    to the appropriate socket based on the local ID.
    """
    
    def __init__(self, connection: AdbConnection):
        """
        Initialize socket manager
        
        Args:
            connection: ADB connection to use
        """
        self.connection = connection
        self.sockets: Dict[int, AdbSocket] = {}
        self._lock = threading.RLock()
        
        # Start packet handling thread
        self._packet_thread = threading.Thread(
            target=self._handle_packets,
            daemon=True
        )
        self._running = True
        self._packet_thread.start()
    
    def create_socket(self, destination: str) -> AdbSocket:
        """
        Create a new ADB socket
        
        Args:
            destination: Service destination
            
        Returns:
            New AdbSocket instance
        """
        socket_obj = AdbSocket(self.connection, destination)
        
        with self._lock:
            self.sockets[socket_obj.local_id] = socket_obj
        
        return socket_obj
    
    def remove_socket(self, socket_obj: AdbSocket) -> None:
        """
        Remove a socket from management
        
        Args:
            socket_obj: Socket to remove
        """
        with self._lock:
            self.sockets.pop(socket_obj.local_id, None)
    
    def _handle_packets(self) -> None:
        """Handle incoming packets and route to appropriate sockets"""
        while self._running:
            try:
                # Receive packet from connection
                packet = self.connection.receive_packet()
                if packet is None:
                    time.sleep(0.1)
                    continue
                
                # Route packet to appropriate socket
                local_id = packet.message.arg1  # For responses, arg1 is our local_id
                
                with self._lock:
                    if local_id in self.sockets:
                        self.sockets[local_id].handle_packet(packet)
                    else:
                        logger.warning(f"Received packet for unknown socket {local_id}")
            
            except Exception as e:
                logger.error(f"Packet handling error: {e}")
                time.sleep(0.1)
    
    def stop(self) -> None:
        """Stop the socket manager"""
        self._running = False
        
        # Close all sockets
        with self._lock:
            for socket_obj in list(self.sockets.values()):
                socket_obj.close()
            self.sockets.clear()
        
        # Wait for packet thread
        if self._packet_thread.is_alive():
            self._packet_thread.join(timeout=2.0)

    def _route_packet(self, packet: AdbPacket) -> None:
        """Route packet to appropriate socket (for testing)"""
        local_id = packet.message.arg1  # For responses, arg1 is our local_id

        with self._lock:
            if local_id in self.sockets:
                self.sockets[local_id].handle_packet(packet)
            else:
                logger.warning(f"Received packet for unknown socket {local_id}")

    def shutdown(self) -> None:
        """Alias for stop() method"""
        self.stop()
