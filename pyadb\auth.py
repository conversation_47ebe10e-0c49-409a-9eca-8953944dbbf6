"""
ADB Authentication and Key Management

This module implements RSA key generation, storage, and authentication
mechanisms similar to the Android ADB implementation.
"""

import os
import hashlib
import base64
import struct
import secrets
from pathlib import Path
from typing import Optional, Dict, List, Tuple
import logging

try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.primitives.serialization import load_pem_private_key
    from cryptography.hazmat.backends import default_backend
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

from .const import TOKEN_SIZE

logger = logging.getLogger(__name__)


class AdbAuthError(Exception):
    """Exception raised for ADB authentication errors"""
    pass


class AdbKeyManager:
    """Manages RSA keys for ADB authentication"""
    
    def __init__(self, key_dir: Optional[str] = None):
        """
        Initialize key manager
        
        Args:
            key_dir: Directory to store keys (default: ~/.android)
        """
        if not CRYPTO_AVAILABLE:
            raise AdbAuthError("cryptography library not available. Install with: pip install cryptography")
        
        if key_dir is None:
            key_dir = os.path.expanduser("~/.android")
        
        self.key_dir = Path(key_dir)
        self.key_dir.mkdir(parents=True, exist_ok=True)
        
        self.private_key_path = self.key_dir / "adbkey"
        self.public_key_path = self.key_dir / "adbkey.pub"
        
        self._private_key: Optional[rsa.RSAPrivateKey] = None
        self._public_key_str: Optional[str] = None
    
    def _generate_rsa_key(self) -> rsa.RSAPrivateKey:
        """Generate a new 2048-bit RSA key"""
        logger.info("Generating new RSA key pair")
        return rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )
    
    def _android_pubkey_encode(self, private_key: rsa.RSAPrivateKey) -> bytes:
        """
        Encode RSA public key in Android format (ANDROID_PUBKEY_ENCODED_SIZE = 524 bytes)

        This matches the android_pubkey_encode function from ADB source code.

        Args:
            private_key: RSA private key

        Returns:
            524-byte encoded public key
        """
        public_key = private_key.public_key()
        public_numbers = public_key.public_numbers()

        # Android public key format (524 bytes total):
        # - 4 bytes: key length in words (should be 64 for 2048-bit key)
        # - 4 bytes: n0inv (negative inverse of n[0] mod 2^32)
        # - 256 bytes: modulus n in little-endian 32-bit words
        # - 256 bytes: rr (R^2 mod n for Montgomery reduction)
        # - 4 bytes: exponent e

        # Convert modulus to little-endian 32-bit words
        n = public_numbers.n
        e = public_numbers.e

        # Key length in 32-bit words (2048 bits = 64 words)
        key_len_words = 64

        # Convert n to little-endian bytes, then to 32-bit words
        n_bytes = n.to_bytes(256, 'little')  # 2048 bits = 256 bytes

        # Calculate n0inv: negative inverse of n[0] mod 2^32
        n0 = struct.unpack('<I', n_bytes[:4])[0]  # First 32-bit word of n
        n0inv = pow(n0, -1, 2**32)  # Modular inverse
        n0inv = (2**32 - n0inv) % (2**32)  # Negative inverse

        # Calculate rr = R^2 mod n where R = 2^2048
        R = 2**2048
        rr = (R * R) % n
        rr_bytes = rr.to_bytes(256, 'little')

        # Pack the Android public key format
        android_key = (
            struct.pack('<I', key_len_words) +  # key length in words
            struct.pack('<I', n0inv) +          # n0inv
            n_bytes +                           # modulus n (256 bytes)
            rr_bytes +                          # rr (256 bytes)
            struct.pack('<I', e)                # exponent e
        )

        assert len(android_key) == 524, f"Android key should be 524 bytes, got {len(android_key)}"
        return android_key

    def _calculate_public_key_string(self, private_key: rsa.RSAPrivateKey) -> str:
        """
        Calculate the public key string in ADB format (compatible with official ADB)

        Args:
            private_key: RSA private key

        Returns:
            Base64-encoded public key string with identifier
        """
        # Encode in Android format
        android_key = self._android_pubkey_encode(private_key)

        # Base64 encode
        key_b64 = base64.b64encode(android_key).decode('ascii')

        # Add identifier (username@hostname)
        hostname = os.environ.get('COMPUTERNAME', os.environ.get('HOSTNAME', 'unknown'))
        username = os.environ.get('USERNAME', os.environ.get('USER', 'unknown'))

        return f"{key_b64} {username}@{hostname}"
    
    def _save_keys(self, private_key: rsa.RSAPrivateKey, public_key_str: str) -> None:
        """
        Save private and public keys to disk
        
        Args:
            private_key: RSA private key
            public_key_str: Public key string in ADB format
        """
        # Save private key in PEM format
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        # Set restrictive permissions before writing
        old_umask = os.umask(0o077)
        try:
            with open(self.private_key_path, 'wb') as f:
                f.write(private_pem)
            
            with open(self.public_key_path, 'w') as f:
                f.write(public_key_str + '\n')
        finally:
            os.umask(old_umask)
        
        logger.info(f"Saved keys to {self.private_key_path} and {self.public_key_path}")
    
    def _load_private_key(self) -> Optional[rsa.RSAPrivateKey]:
        """Load private key from disk"""
        if not self.private_key_path.exists():
            return None
        
        try:
            with open(self.private_key_path, 'rb') as f:
                private_key = load_pem_private_key(
                    f.read(),
                    password=None,
                    backend=default_backend()
                )
            
            if not isinstance(private_key, rsa.RSAPrivateKey):
                logger.error("Loaded key is not an RSA key")
                return None
            
            return private_key
        except Exception as e:
            logger.error(f"Failed to load private key: {e}")
            return None
    
    def _load_public_key_string(self) -> Optional[str]:
        """Load public key string from disk"""
        if not self.public_key_path.exists():
            return None
        
        try:
            with open(self.public_key_path, 'r') as f:
                return f.read().strip()
        except Exception as e:
            logger.error(f"Failed to load public key string: {e}")
            return None
    
    def get_private_key(self) -> rsa.RSAPrivateKey:
        """
        Get the private key, generating if necessary
        
        Returns:
            RSA private key
        """
        if self._private_key is None:
            self._private_key = self._load_private_key()
            
            if self._private_key is None:
                # Generate new key pair
                self._private_key = self._generate_rsa_key()
                self._public_key_str = self._calculate_public_key_string(self._private_key)
                self._save_keys(self._private_key, self._public_key_str)
        
        return self._private_key
    
    def get_public_key_string(self) -> str:
        """
        Get the public key string in ADB format
        
        Returns:
            Base64-encoded public key string with identifier
        """
        if self._public_key_str is None:
            self._public_key_str = self._load_public_key_string()
            
            if self._public_key_str is None:
                # Generate keys if they don't exist
                private_key = self.get_private_key()
                self._public_key_str = self._calculate_public_key_string(private_key)
        
        return self._public_key_str
    
    def sign_token(self, token: bytes) -> bytes:
        """
        Sign a token with the private key
        
        Args:
            token: Token to sign (typically 20 bytes)
            
        Returns:
            Signature bytes
        """
        private_key = self.get_private_key()
        
        # ADB uses SHA-1 with RSA (PKCS#1 v1.5 padding)
        signature = private_key.sign(
            token,
            padding.PKCS1v15(),
            hashes.SHA1()
        )
        
        return signature
    
    def generate_token(self) -> bytes:
        """
        Generate a random authentication token

        Returns:
            Random token bytes
        """
        return secrets.token_bytes(TOKEN_SIZE)

    def verify_signature(self, token: bytes, signature: bytes, public_key_str: str) -> bool:
        """
        Verify a signature against a token using a public key

        Args:
            token: Original token that was signed
            signature: Signature to verify
            public_key_str: Public key string in ADB format

        Returns:
            True if signature is valid, False otherwise
        """
        try:
            # Extract base64 key part
            key_b64 = public_key_str.split()[0]
            key_bytes = base64.b64decode(key_b64)

            # Decode Android public key format to get RSA public key
            public_key = self._android_pubkey_decode(key_bytes)

            # Verify signature using SHA-1 with PKCS#1 v1.5 padding
            public_key.verify(
                signature,
                token,
                padding.PKCS1v15(),
                hashes.SHA1()
            )
            return True

        except Exception as e:
            logger.debug(f"Signature verification failed: {e}")
            return False

    def _android_pubkey_decode(self, key_bytes: bytes) -> rsa.RSAPublicKey:
        """
        Decode Android public key format to RSA public key

        Args:
            key_bytes: Android public key bytes (524 bytes)

        Returns:
            RSA public key object
        """
        if len(key_bytes) != 524:
            raise ValueError(f"Invalid Android public key size: {len(key_bytes)}")

        # Android public key format (524 bytes total):
        # - 4 bytes: key length in words (should be 64 for 2048-bit key)
        # - 4 bytes: n0inv (negative inverse of n[0] mod 2^32)
        # - 256 bytes: modulus n in little-endian 32-bit words
        # - 256 bytes: rr (R^2 mod n for Montgomery reduction)
        # - 4 bytes: exponent e

        # Extract key length in words (should be 64)
        key_len_words = struct.unpack('<I', key_bytes[0:4])[0]
        if key_len_words != 64:
            raise ValueError(f"Invalid key length: expected 64 words, got {key_len_words}")

        # Skip n0inv (4 bytes)
        # Extract modulus n (256 bytes at offset 8)
        n_bytes = key_bytes[8:8+256]
        n = int.from_bytes(n_bytes, byteorder='little')

        # Skip rr (256 bytes)
        # Extract exponent e (4 bytes at offset 8+256+256=520)
        e_bytes = key_bytes[520:524]
        e = int.from_bytes(e_bytes, byteorder='little')

        # Create RSA public key
        public_numbers = rsa.RSAPublicNumbers(e, n)
        return public_numbers.public_key(backend=default_backend())
    
    def get_key_fingerprint(self) -> str:
        """
        Get the SHA-256 fingerprint of the public key
        
        Returns:
            Hex-encoded SHA-256 hash of the public key
        """
        public_key_str = self.get_public_key_string()
        # Extract just the base64 part (before the space)
        key_b64 = public_key_str.split()[0]
        key_bytes = base64.b64decode(key_b64)
        
        digest = hashlib.sha256(key_bytes).digest()
        return digest.hex()


# Global key manager instance
_key_manager: Optional[AdbKeyManager] = None


def get_key_manager(key_dir: Optional[str] = None) -> AdbKeyManager:
    """
    Get the global key manager instance
    
    Args:
        key_dir: Directory to store keys (only used on first call)
        
    Returns:
        AdbKeyManager instance
    """
    global _key_manager
    if _key_manager is None:
        _key_manager = AdbKeyManager(key_dir)
    return _key_manager


def sign_auth_token(token: bytes, key_dir: Optional[str] = None) -> bytes:
    """
    Sign an authentication token
    
    Args:
        token: Token to sign
        key_dir: Key directory (optional)
        
    Returns:
        Signature bytes
    """
    key_manager = get_key_manager(key_dir)
    return key_manager.sign_token(token)


def get_public_key(key_dir: Optional[str] = None) -> str:
    """
    Get the public key string
    
    Args:
        key_dir: Key directory (optional)
        
    Returns:
        Public key string in ADB format
    """
    key_manager = get_key_manager(key_dir)
    return key_manager.get_public_key_string()
