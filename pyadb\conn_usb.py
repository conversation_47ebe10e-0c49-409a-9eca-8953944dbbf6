"""
ADB USB Connection Implementation

This module implements USB-based ADB connections using PyUSB.
It handles USB device discovery, bulk transfer communication,
and low-level USB protocol handling for ADB.
"""

import time
import threading
from typing import Optional, List, Tuple, Any
import logging

try:
    import usb.core
    import usb.util
    USB_AVAILABLE = True
except ImportError:
    USB_AVAILABLE = False
    usb = None

from .const import (
    ADB_CLASS, ADB_SUBCLASS, ADB_PROTOCOL,
    TransportType, ConnectionState
)
from .core import AdbPacket, AdbMessage, MESSAGE_SIZE

logger = logging.getLogger(__name__)


class UsbDevice:
    """Represents a USB device that supports ADB"""

    def __init__(self, device, interface_number: int,
                 endpoint_in: int, endpoint_out: int):
        self.device = device
        self.interface_number = interface_number
        self.endpoint_in = endpoint_in
        self.endpoint_out = endpoint_out
        self.serial_number = self._get_serial_number()
        self.product_name = self._get_product_name()

    def _get_serial_number(self) -> str:
        """Get device serial number"""
        try:
            if self.device.serial_number:
                return self.device.serial_number
        except (ValueError, usb.core.USBError):
            pass
        return f"usb:{self.device.bus:03d}:{self.device.address:03d}"

    def _get_product_name(self) -> str:
        """Get device product name"""
        try:
            if self.device.product:
                return self.device.product
        except (ValueError, usb.core.USBError):
            pass
        return "Unknown Device"

    def __str__(self) -> str:
        return f"UsbDevice({self.serial_number}, {self.product_name})"


class AdbUsbConnection:
    """
    USB-based ADB connection using PyUSB.

    Handles USB bulk transfer communication with ADB-enabled devices.
    Requires PyUSB library and appropriate USB drivers.
    """

    def __init__(self, device: Optional[UsbDevice] = None, timeout: int = 5000):
        """
        Initialize USB connection.

        Args:
            device: Specific USB device to connect to (None for auto-detect)
            timeout: USB transfer timeout in milliseconds
        """
        if not USB_AVAILABLE:
            raise ImportError("PyUSB is required for USB connections. Install with: pip install pyusb")

        self.device = device
        self.timeout = timeout
        self.usb_device = None
        self.interface = None
        self.endpoint_in = None
        self.endpoint_out = None
        self.connected = False
        self.transport_type = TransportType.USB
        self._lock = threading.RLock()

        logger.debug(f"Initialized USB connection with timeout {timeout}ms")

    @staticmethod
    def find_adb_devices() -> List[UsbDevice]:
        """
        Find all USB devices that support ADB.

        Returns:
            List of UsbDevice objects representing ADB-capable devices
        """
        if not USB_AVAILABLE:
            logger.error("PyUSB not available")
            return []

        devices = []

        try:
            # Find all USB devices
            usb_devices = usb.core.find(find_all=True)

            for dev in usb_devices:
                try:
                    # Check each configuration
                    for cfg in dev:
                        # Check each interface
                        for intf in cfg:
                            # Look for ADB interface
                            if (intf.bInterfaceClass == ADB_CLASS and
                                intf.bInterfaceSubClass == ADB_SUBCLASS and
                                intf.bInterfaceProtocol == ADB_PROTOCOL):

                                # Find bulk endpoints
                                endpoint_in = None
                                endpoint_out = None

                                for ep in intf:
                                    if usb.util.endpoint_direction(ep.bEndpointAddress) == usb.util.ENDPOINT_IN:
                                        if usb.util.endpoint_type(ep.bmAttributes) == usb.util.ENDPOINT_TYPE_BULK:
                                            endpoint_in = ep.bEndpointAddress
                                    elif usb.util.endpoint_direction(ep.bEndpointAddress) == usb.util.ENDPOINT_OUT:
                                        if usb.util.endpoint_type(ep.bmAttributes) == usb.util.ENDPOINT_TYPE_BULK:
                                            endpoint_out = ep.bEndpointAddress

                                if endpoint_in and endpoint_out:
                                    adb_device = UsbDevice(dev, intf.bInterfaceNumber,
                                                         endpoint_in, endpoint_out)
                                    devices.append(adb_device)
                                    logger.debug(f"Found ADB device: {adb_device}")
                                    break
                        else:
                            continue
                        break

                except (usb.core.USBError, ValueError) as e:
                    logger.debug(f"Error checking device {dev}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error finding USB devices: {e}")

        logger.info(f"Found {len(devices)} ADB USB devices")
        return devices

    def connect(self, device: Optional[UsbDevice] = None) -> bool:
        """
        Connect to USB ADB device.

        Args:
            device: Specific device to connect to (None for auto-detect)

        Returns:
            True if connection successful, False otherwise
        """
        with self._lock:
            if self.connected:
                logger.warning("Already connected")
                return True

            target_device = device or self.device

            if not target_device:
                # Auto-detect first available device
                devices = self.find_adb_devices()
                if not devices:
                    logger.error("No ADB USB devices found")
                    return False
                target_device = devices[0]
                logger.info(f"Auto-selected device: {target_device}")

            try:
                self.usb_device = target_device.device

                # Set configuration (usually the first one)
                try:
                    self.usb_device.set_configuration()
                except usb.core.USBError as e:
                    logger.debug(f"Could not set configuration: {e}")

                # Claim the ADB interface
                self.interface = target_device.interface_number
                try:
                    usb.util.claim_interface(self.usb_device, self.interface)
                except usb.core.USBError as e:
                    logger.error(f"Could not claim interface {self.interface}: {e}")
                    return False

                # Store endpoints
                self.endpoint_in = target_device.endpoint_in
                self.endpoint_out = target_device.endpoint_out

                self.device = target_device
                self.connected = True

                logger.info(f"Connected to USB device: {target_device}")
                return True

            except usb.core.USBError as e:
                logger.error(f"USB error during connection: {e}")
                self.disconnect()
                return False
            except Exception as e:
                logger.error(f"Unexpected error during USB connection: {e}")
                self.disconnect()
                return False

    def disconnect(self) -> None:
        """Disconnect from USB device"""
        with self._lock:
            if self.usb_device and self.interface is not None:
                try:
                    usb.util.release_interface(self.usb_device, self.interface)
                except usb.core.USBError as e:
                    logger.debug(f"Error releasing interface: {e}")

            self.usb_device = None
            self.interface = None
            self.endpoint_in = None
            self.endpoint_out = None
            self.connected = False

            logger.debug("Disconnected from USB device")

    def send_packet(self, packet: AdbPacket) -> bool:
        """
        Send an ADB packet over USB bulk transfer.

        Args:
            packet: ADB packet to send

        Returns:
            True if sent successfully, False otherwise
        """
        with self._lock:
            if not self.connected or not self.usb_device:
                logger.error("Not connected")
                return False

            try:
                data = packet.to_bytes()
                bytes_sent = self.usb_device.write(self.endpoint_out, data, self.timeout)

                if bytes_sent != len(data):
                    logger.error(f"Incomplete USB write: sent {bytes_sent}/{len(data)} bytes")
                    return False

                logger.debug(f"Sent USB packet: {packet.message}")
                return True

            except usb.core.USBError as e:
                logger.error(f"USB error sending packet: {e}")
                self.disconnect()
                return False
            except Exception as e:
                logger.error(f"Unexpected error sending USB packet: {e}")
                return False

    def receive_packet(self) -> Optional[AdbPacket]:
        """
        Receive an ADB packet from USB bulk transfer.

        Returns:
            AdbPacket if received successfully, None otherwise
        """
        with self._lock:
            if not self.connected or not self.usb_device:
                logger.error("Not connected")
                return None

            try:
                # First, receive the message header
                header_data = self._receive_exact(MESSAGE_SIZE)
                if not header_data:
                    return None

                # Parse the message header
                message = AdbMessage.from_bytes(header_data)

                # Receive payload if present
                payload = b''
                if message.data_length > 0:
                    payload = self._receive_exact(message.data_length)
                    if not payload:
                        logger.error("Failed to receive complete USB payload")
                        return None

                packet = AdbPacket(message, payload)
                logger.debug(f"Received USB packet: {packet.message}")
                return packet

            except usb.core.USBError as e:
                logger.error(f"USB error receiving packet: {e}")
                self.disconnect()
                return None
            except ValueError as e:
                logger.error(f"Invalid USB packet received: {e}")
                return None

    def _receive_exact(self, size: int) -> Optional[bytes]:
        """
        Receive exactly the specified number of bytes via USB.

        Args:
            size: Number of bytes to receive

        Returns:
            Bytes received, or None if transfer failed
        """
        if not self.usb_device:
            return None

        data = b''
        while len(data) < size:
            try:
                # USB bulk transfers may return less data than requested
                remaining = size - len(data)
                chunk = self.usb_device.read(self.endpoint_in, remaining, self.timeout)

                if not chunk:
                    logger.error("USB bulk transfer returned no data")
                    return None

                # Convert array to bytes if necessary
                if hasattr(chunk, 'tobytes'):
                    chunk = chunk.tobytes()
                elif hasattr(chunk, 'tostring'):
                    chunk = chunk.tostring()

                data += chunk

            except usb.core.USBTimeoutError:
                logger.error("USB receive timeout")
                return None
            except usb.core.USBError as e:
                logger.error(f"USB error during receive: {e}")
                self.disconnect()
                return None

        return data

    def is_connected(self) -> bool:
        """Check if USB connection is active"""
        return self.connected and self.usb_device is not None

    def get_device_info(self) -> Optional[Tuple[str, str]]:
        """Get device serial number and product name"""
        if self.device:
            return (self.device.serial_number, self.device.product_name)
        return None

    def reset_device(self) -> bool:
        """Reset the USB device"""
        if self.usb_device:
            try:
                self.usb_device.reset()
                logger.info("USB device reset")
                return True
            except usb.core.USBError as e:
                logger.error(f"Failed to reset USB device: {e}")
                return False
        return False

    def __enter__(self):
        """Context manager entry"""
        if not self.connect():
            raise ConnectionError("Failed to connect to USB device")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()

    def __str__(self) -> str:
        if self.device:
            status = "connected" if self.connected else "disconnected"
            return f"AdbUsbConnection({self.device.serial_number}, {status})"
        return f"AdbUsbConnection(no device, {'connected' if self.connected else 'disconnected'})"