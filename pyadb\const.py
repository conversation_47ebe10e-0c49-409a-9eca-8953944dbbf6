"""
ADB Protocol Constants

This module contains all the constants used in the ADB protocol,
based on the Android Open Source Project ADB implementation.
"""

import struct
from enum import IntEnum
from typing import Final

# ADB Protocol Commands (from adb.h)
A_SYNC: Final[int] = 0x434e5953  # 'SYNC'
A_CNXN: Final[int] = 0x4e584e43  # 'CNXN'
A_OPEN: Final[int] = 0x4e45504f  # 'OPEN'
A_OKAY: Final[int] = 0x59414b4f  # 'OKAY'
A_CLSE: Final[int] = 0x45534c43  # 'CLSE'
A_WRTE: Final[int] = 0x45545257  # 'WRTE'
A_AUTH: Final[int] = 0x48545541  # 'AUTH'
A_STLS: Final[int] = 0x534C5453  # 'STLS'

# ADB Protocol Versions
A_VERSION_MIN: Final[int] = 0x01000000
A_VERSION_SKIP_CHECKSUM: Final[int] = 0x01000001
A_VERSION: Final[int] = 0x01000001

# Stream-based TLS protocol version
A_STLS_VERSION_MIN: Final[int] = 0x01000000
A_STLS_VERSION: Final[int] = 0x01000000

# Version information
ADB_VERSION_MAJOR: Final[int] = 1
ADB_VERSION_MINOR: Final[int] = 0
ADB_SERVER_VERSION: Final[int] = 41

# Payload size limits
MAX_PAYLOAD_V1: Final[int] = 4 * 1024
MAX_PAYLOAD: Final[int] = 1024 * 1024

# Delayed ACK configuration
INITIAL_DELAYED_ACK_BYTES: Final[int] = 32 * 1024 * 1024

# Linux socket size limit
LINUX_MAX_SOCKET_SIZE: Final[int] = 4194304

# Token size for authentication
TOKEN_SIZE: Final[int] = 20

# Authentication constants (from adb_auth.h)
ADB_AUTH_TOKEN: Final[int] = 1
ADB_AUTH_SIGNATURE: Final[int] = 2
ADB_AUTH_RSAPUBLICKEY: Final[int] = 3

# Default ADB local transport port
DEFAULT_ADB_LOCAL_TRANSPORT_PORT: Final[int] = 5555

# USB class/subclass/protocol identifiers
ADB_CLASS: Final[int] = 0xff
ADB_SUBCLASS: Final[int] = 0x42
ADB_PROTOCOL: Final[int] = 0x1

# DBC (Debug Bridge Connection) identifiers
ADB_DBC_CLASS: Final[int] = 0xDC
ADB_DBC_SUBCLASS: Final[int] = 0x2

# Message header format (24 bytes total)
MESSAGE_FORMAT: Final[str] = '<6I'  # 6 unsigned 32-bit integers, little-endian
MESSAGE_SIZE: Final[int] = struct.calcsize(MESSAGE_FORMAT)


class TransportType(IntEnum):
    """Transport types for ADB connections"""
    USB = 0
    LOCAL = 1  # TCP/IP
    ANY = 2
    HOST = 3


class ConnectionState(IntEnum):
    """Connection states for ADB devices"""
    ANY = -1

    # Pre-connection states
    CONNECTING = 0      # Haven't received response from device yet
    AUTHORIZING = 1     # Authorizing with keys from ADB_VENDOR_KEYS
    UNAUTHORIZED = 2    # ADB_VENDOR_KEYS exhausted, fell back to user prompt
    NO_PERM = 3        # Insufficient permissions to communicate with device
    DETACHED = 4       # USB device detached from adb server
    OFFLINE = 5        # Peer detected but no communication started

    # Post-CNXN states (describe service type on other end)
    BOOTLOADER = 6     # Device running fastboot OS
    DEVICE = 7         # Device running Android OS (adbd)
    HOST = 8          # What device sees from its end (adb host)
    RECOVERY = 9      # Device with bootloader loaded but no ROM OS
    SIDELOAD = 10     # Device running Android OS Sideload mode
    RESCUE = 11       # Device running Android OS Rescue mode


# Helper functions for command identification
COMMAND_NAMES = {
    A_SYNC: 'SYNC',
    A_CNXN: 'CNXN',
    A_OPEN: 'OPEN',
    A_OKAY: 'OKAY',
    A_CLSE: 'CLSE',
    A_WRTE: 'WRTE',
    A_AUTH: 'AUTH',
    A_STLS: 'STLS',
}


def command_to_string(cmd: int) -> str:
    """Convert command integer to human-readable string"""
    return COMMAND_NAMES.get(cmd, f'UNKNOWN(0x{cmd:08x})')


def connection_state_is_online(state: ConnectionState) -> bool:
    """Check if connection state indicates device is online"""
    return state in {
        ConnectionState.BOOTLOADER,
        ConnectionState.DEVICE,
        ConnectionState.HOST,
        ConnectionState.RECOVERY,
        ConnectionState.SIDELOAD,
        ConnectionState.RESCUE,
    }


# Feature strings (do not use [:;=,] in feature strings)
FEATURE_SHELL2 = "shell_v2"
FEATURE_CMD = "cmd"
FEATURE_STAT2 = "stat_v2"
FEATURE_LS2 = "ls_v2"
FEATURE_LIBUSB = "libusb"
FEATURE_PUSH_SYNC = "push_sync"
FEATURE_APEX = "apex"
FEATURE_FIXED_PUSH_MKDIR = "fixed_push_mkdir"
FEATURE_ABB = "abb"
FEATURE_ABB_EXEC = "abb_exec"
FEATURE_FIXED_PUSH_SYMLINK_TIMESTAMP = "fixed_push_symlink_timestamp"
FEATURE_REMOUNT_SHELL = "remount_shell"
FEATURE_TRACK_APP = "track_app"
FEATURE_SENDRECV2 = "sendrecv_v2"
FEATURE_SENDRECV2_BROTLI = "sendrecv_v2_brotli"
FEATURE_SENDRECV2_LZ4 = "sendrecv_v2_lz4"
FEATURE_SENDRECV2_ZSTD = "sendrecv_v2_zstd"
FEATURE_SENDRECV2_DRY_RUN = "sendrecv_v2_dry_run"

# Default supported features
DEFAULT_FEATURES = [
    FEATURE_SHELL2,
    FEATURE_CMD,
    FEATURE_STAT2,
    FEATURE_LS2,
]