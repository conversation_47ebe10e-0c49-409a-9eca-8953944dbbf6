"""
ADB Core Protocol Implementation

This module implements the core ADB protocol for packet handling,
message parsing, and basic communication primitives.
"""

import struct
import zlib
from dataclasses import dataclass
from typing import Optional, Union, List, Tuple
import logging

from .const import (
    A_SYNC, A_CNXN, A_OPEN, A_OKAY, A_CLSE, A_WRTE, A_AUTH, A_STLS,
    A_VERSION, A_VERSION_SKIP_CHECKSUM, MESSAGE_FORMAT, MESSAGE_SIZE, MAX_PAYLOAD,
    command_to_string, ConnectionState, TransportType
)

logger = logging.getLogger(__name__)


@dataclass
class AdbMessage:
    """
    ADB protocol message structure.

    Represents the 24-byte header that precedes all ADB protocol messages.
    Based on the amessage struct from the Android source.
    """
    command: int        # Command identifier constant
    arg0: int          # First argument
    arg1: int          # Second argument
    data_length: int   # Length of payload (0 is allowed)
    data_check: int    # Checksum of data payload
    magic: int         # Command ^ 0xffffffff

    def __post_init__(self):
        """Validate message after initialization"""
        if self.magic != (self.command ^ 0xffffffff):
            raise ValueError(f"Invalid magic value: expected {self.command ^ 0xffffffff:08x}, got {self.magic:08x}")

        if self.data_length > MAX_PAYLOAD:
            raise ValueError(f"Data length {self.data_length} exceeds maximum {MAX_PAYLOAD}")

    @classmethod
    def from_bytes(cls, data: bytes) -> 'AdbMessage':
        """Create AdbMessage from raw bytes"""
        if len(data) < MESSAGE_SIZE:
            raise ValueError(f"Insufficient data for message header: {len(data)} < {MESSAGE_SIZE}")

        fields = struct.unpack(MESSAGE_FORMAT, data[:MESSAGE_SIZE])
        return cls(*fields)

    def to_bytes(self) -> bytes:
        """Convert AdbMessage to raw bytes"""
        return struct.pack(MESSAGE_FORMAT,
                          self.command, self.arg0, self.arg1,
                          self.data_length, self.data_check, self.magic)

    def __str__(self) -> str:
        return (f"AdbMessage({command_to_string(self.command)}, "
                f"arg0=0x{self.arg0:08x}, arg1=0x{self.arg1:08x}, "
                f"data_length={self.data_length}, data_check=0x{self.data_check:08x})")


@dataclass
class AdbPacket:
    """
    Complete ADB packet with header and payload.

    Represents a full ADB protocol packet consisting of a message header
    and optional payload data.
    """
    message: AdbMessage
    payload: bytes = b''
    skip_checksum_validation: bool = True  # Default to True for modern ADB

    def __post_init__(self):
        """Validate packet after initialization"""
        if len(self.payload) != self.message.data_length:
            raise ValueError(f"Payload length mismatch: expected {self.message.data_length}, got {len(self.payload)}")

        # Verify checksum if payload exists and validation is not skipped
        if self.payload and not self.skip_checksum_validation:
            expected_checksum = calculate_checksum(self.payload)
            if self.message.data_check != expected_checksum:
                raise ValueError(f"Checksum mismatch: expected {expected_checksum:08x}, got {self.message.data_check:08x}")

    @classmethod
    def from_bytes(cls, data: bytes, skip_checksum_validation: bool = True) -> 'AdbPacket':
        """Create AdbPacket from raw bytes"""
        message = AdbMessage.from_bytes(data)
        payload = data[MESSAGE_SIZE:MESSAGE_SIZE + message.data_length]

        if len(payload) != message.data_length:
            raise ValueError(f"Incomplete payload: expected {message.data_length}, got {len(payload)}")

        return cls(message, payload, skip_checksum_validation)

    def to_bytes(self) -> bytes:
        """Convert AdbPacket to raw bytes"""
        return self.message.to_bytes() + self.payload

    def __str__(self) -> str:
        payload_preview = self.payload[:50] + b'...' if len(self.payload) > 50 else self.payload
        return f"AdbPacket({self.message}, payload={payload_preview!r})"


def calculate_checksum(data: bytes) -> int:
    """
    Calculate ADB checksum for payload data.

    The ADB protocol uses a simple sum-based checksum for payload verification.
    Note: Modern ADB versions (A_VERSION_SKIP_CHECKSUM) may skip this check.
    """
    if not data:
        return 0

    # Simple sum checksum as used in ADB
    checksum = sum(data) & 0xffffffff
    return checksum


def create_message(command: int, arg0: int = 0, arg1: int = 0,
                  data: bytes = b'') -> AdbMessage:
    """Create a new ADB message with proper magic and checksum"""
    data_length = len(data)
    data_check = calculate_checksum(data) if data else 0
    magic = command ^ 0xffffffff

    return AdbMessage(command, arg0, arg1, data_length, data_check, magic)


def create_packet(command: int, arg0: int = 0, arg1: int = 0,
                 payload: bytes = b'', skip_checksum_validation: bool = True) -> AdbPacket:
    """Create a new ADB packet with proper message header"""
    message = create_message(command, arg0, arg1, payload)
    return AdbPacket(message, payload, skip_checksum_validation)


# Protocol-specific packet creators
def create_connect_packet(version: int = A_VERSION, max_data: int = MAX_PAYLOAD,
                         system_identity: str = "host::") -> AdbPacket:
    """Create a CNXN (connect) packet"""
    payload = system_identity.encode('utf-8') + b'\0'
    return create_packet(A_CNXN, version, max_data, payload)


def create_auth_packet(auth_type: int, data: bytes = b'') -> AdbPacket:
    """Create an AUTH packet"""
    return create_packet(A_AUTH, auth_type, 0, data)


def create_open_packet(local_id: int, destination: str) -> AdbPacket:
    """Create an OPEN packet to establish a service connection"""
    payload = destination.encode('utf-8') + b'\0'
    return create_packet(A_OPEN, local_id, 0, payload)


def create_ready_packet(local_id: int, remote_id: int) -> AdbPacket:
    """Create an OKAY packet to acknowledge connection"""
    return create_packet(A_OKAY, local_id, remote_id)


def create_write_packet(local_id: int, remote_id: int, data: bytes) -> AdbPacket:
    """Create a WRTE packet to send data"""
    return create_packet(A_WRTE, local_id, remote_id, data)


def create_close_packet(local_id: int, remote_id: int) -> AdbPacket:
    """Create a CLSE packet to close connection"""
    return create_packet(A_CLSE, local_id, remote_id)