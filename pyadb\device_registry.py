"""
ADB Device Registry

This module manages a local registry of paired ADB devices,
storing connection information and authentication keys.
Compatible with official ADB's adb_known_hosts format.
"""

import json
import time
import struct
from pathlib import Path
from typing import Dict, List, Optional, NamedTuple
from dataclasses import dataclass, asdict
import logging

try:
    # Try to use protobuf for ADB compatibility
    import google.protobuf.message
    PROTOBUF_AVAILABLE = True
except ImportError:
    PROTOBUF_AVAILABLE = False

logger = logging.getLogger(__name__)


class AdbKnownHosts:
    """
    Simple implementation of ADB's known hosts protobuf format

    This provides compatibility with the official ADB's adb_known_hosts file
    without requiring the full protobuf library.
    """

    def __init__(self):
        self.host_infos: List[Dict[str, str]] = []

    def add_host_info(self, guid: str) -> None:
        """Add a host info entry"""
        # Check if already exists
        for host_info in self.host_infos:
            if host_info.get('guid') == guid:
                return

        self.host_infos.append({'guid': guid})

    def remove_host_info(self, guid: str) -> bool:
        """Remove a host info entry"""
        for i, host_info in enumerate(self.host_infos):
            if host_info.get('guid') == guid:
                del self.host_infos[i]
                return True
        return False

    def has_host(self, guid: str) -> bool:
        """Check if host exists"""
        return any(host_info.get('guid') == guid for host_info in self.host_infos)

    def to_binary(self) -> bytes:
        """
        Serialize to binary format (simplified protobuf-like)

        Format:
        - 4 bytes: number of host_infos
        - For each host_info:
          - 4 bytes: guid length
          - N bytes: guid string (UTF-8)
        """
        data = struct.pack('<I', len(self.host_infos))

        for host_info in self.host_infos:
            guid = host_info.get('guid', '').encode('utf-8')
            data += struct.pack('<I', len(guid))
            data += guid

        return data

    @classmethod
    def from_binary(cls, data: bytes) -> 'AdbKnownHosts':
        """Deserialize from binary format"""
        known_hosts = cls()

        if len(data) < 4:
            return known_hosts

        offset = 0
        num_hosts = struct.unpack('<I', data[offset:offset+4])[0]
        offset += 4

        for _ in range(num_hosts):
            if offset + 4 > len(data):
                break

            guid_len = struct.unpack('<I', data[offset:offset+4])[0]
            offset += 4

            if offset + guid_len > len(data):
                break

            guid = data[offset:offset+guid_len].decode('utf-8', errors='ignore')
            offset += guid_len

            known_hosts.add_host_info(guid)

        return known_hosts

    def to_json(self) -> Dict:
        """Convert to JSON format (fallback)"""
        return {
            'version': 1,
            'host_infos': [{'guid': info['guid']} for info in self.host_infos]
        }

    @classmethod
    def from_json(cls, data: Dict) -> 'AdbKnownHosts':
        """Create from JSON format (fallback)"""
        known_hosts = cls()

        for host_info in data.get('host_infos', []):
            guid = host_info.get('guid')
            if guid:
                known_hosts.add_host_info(guid)

        return known_hosts


@dataclass
class DeviceInfo:
    """Information about a paired ADB device"""
    guid: str                    # Device GUID
    name: str                    # Device name/model
    ip_address: str             # Last known IP address
    port: int                   # ADB port
    auth_key: str               # Device's public key for authentication
    paired_timestamp: float     # When device was paired (Unix timestamp)
    last_seen: float            # Last successful connection (Unix timestamp)
    connection_count: int = 0   # Number of successful connections
    is_trusted: bool = True     # Whether device is trusted
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'DeviceInfo':
        """Create from dictionary (JSON deserialization)"""
        return cls(**data)
    
    def update_last_seen(self) -> None:
        """Update the last seen timestamp to now"""
        self.last_seen = time.time()
        self.connection_count += 1


class DeviceRegistry:
    """Registry for managing paired ADB devices (ADB-compatible)"""

    def __init__(self, registry_dir: Optional[str] = None):
        """
        Initialize device registry

        Args:
            registry_dir: Directory to store registry (default: ~/.android)
        """
        if registry_dir is None:
            registry_dir = Path.home() / ".android"
        else:
            registry_dir = Path(registry_dir)

        registry_dir.mkdir(parents=True, exist_ok=True)

        # Use ADB-compatible file names
        self.known_hosts_file = registry_dir / "adb_known_hosts"  # Binary protobuf format
        self.registry_file = registry_dir / "adb_known_hosts.json"  # JSON fallback

        self._devices: Dict[str, DeviceInfo] = {}
        self._known_hosts = AdbKnownHosts()
        self._load_registry()
    
    def _load_registry(self) -> None:
        """Load device registry from disk (ADB-compatible)"""
        # Try to load ADB-compatible known hosts file first
        if self.known_hosts_file.exists():
            try:
                with open(self.known_hosts_file, 'rb') as f:
                    data = f.read()
                self._known_hosts = AdbKnownHosts.from_binary(data)
                logger.info(f"Loaded {len(self._known_hosts.host_infos)} known hosts from ADB format")
            except Exception as e:
                logger.warning(f"Failed to load ADB known hosts: {e}")
                self._known_hosts = AdbKnownHosts()

        # Load extended device info from JSON file
        if self.registry_file.exists():
            try:
                with open(self.registry_file, 'r') as f:
                    data = json.load(f)

                self._devices = {}
                for guid, device_data in data.get('devices', {}).items():
                    try:
                        self._devices[guid] = DeviceInfo.from_dict(device_data)
                    except Exception as e:
                        logger.warning(f"Failed to load device {guid}: {e}")

                logger.info(f"Loaded {len(self._devices)} devices from registry")

            except Exception as e:
                logger.error(f"Failed to load device registry: {e}")
                self._devices = {}

        if not self.registry_file.exists() and not self.known_hosts_file.exists():
            logger.info("No existing device registry found")
    
    def _save_registry(self) -> None:
        """Save device registry to disk (ADB-compatible)"""
        try:
            # Save ADB-compatible known hosts file
            binary_data = self._known_hosts.to_binary()
            temp_known_hosts = self.known_hosts_file.with_suffix('.tmp')
            with open(temp_known_hosts, 'wb') as f:
                f.write(binary_data)
            temp_known_hosts.replace(self.known_hosts_file)

            # Save extended device info to JSON
            data = {
                'version': 1,
                'devices': {
                    guid: device.to_dict()
                    for guid, device in self._devices.items()
                }
            }

            temp_file = self.registry_file.with_suffix('.tmp')
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)
            temp_file.replace(self.registry_file)

            logger.debug(f"Saved {len(self._devices)} devices to registry")

        except Exception as e:
            logger.error(f"Failed to save device registry: {e}")
    
    def add_device(self, device: DeviceInfo) -> None:
        """
        Add or update a device in the registry

        Args:
            device: Device information to add/update
        """
        self._devices[device.guid] = device
        self._known_hosts.add_host_info(device.guid)
        self._save_registry()
        logger.info(f"Added/updated device {device.guid} ({device.name})")
    
    def remove_device(self, guid: str) -> bool:
        """
        Remove a device from the registry

        Args:
            guid: Device GUID to remove

        Returns:
            True if device was removed, False if not found
        """
        removed = False
        if guid in self._devices:
            device = self._devices.pop(guid)
            removed = True
            logger.info(f"Removed device {guid} ({device.name})")

        if self._known_hosts.remove_host_info(guid):
            removed = True

        if removed:
            self._save_registry()

        return removed
    
    def get_device(self, guid: str) -> Optional[DeviceInfo]:
        """
        Get device information by GUID
        
        Args:
            guid: Device GUID
            
        Returns:
            Device information or None if not found
        """
        return self._devices.get(guid)
    
    def get_device_by_address(self, ip_address: str, port: int = 5555) -> Optional[DeviceInfo]:
        """
        Get device information by IP address and port
        
        Args:
            ip_address: Device IP address
            port: Device port (default: 5555)
            
        Returns:
            Device information or None if not found
        """
        for device in self._devices.values():
            if device.ip_address == ip_address and device.port == port:
                return device
        return None
    
    def list_devices(self, trusted_only: bool = True) -> List[DeviceInfo]:
        """
        List all registered devices
        
        Args:
            trusted_only: Only return trusted devices
            
        Returns:
            List of device information
        """
        devices = list(self._devices.values())
        if trusted_only:
            devices = [d for d in devices if d.is_trusted]
        
        # Sort by last seen (most recent first)
        devices.sort(key=lambda d: d.last_seen, reverse=True)
        return devices
    
    def update_device_connection(self, guid: str, ip_address: str, port: int) -> bool:
        """
        Update device connection information
        
        Args:
            guid: Device GUID
            ip_address: New IP address
            port: New port
            
        Returns:
            True if device was updated, False if not found
        """
        device = self._devices.get(guid)
        if device:
            device.ip_address = ip_address
            device.port = port
            device.update_last_seen()
            self._save_registry()
            logger.debug(f"Updated connection info for {guid}: {ip_address}:{port}")
            return True
        return False
    
    def is_device_known(self, guid: str) -> bool:
        """
        Check if a device is known (registered) - ADB compatible

        Args:
            guid: Device GUID

        Returns:
            True if device is known
        """
        return self._known_hosts.has_host(guid) or guid in self._devices
    
    def get_device_auth_key(self, guid: str) -> Optional[str]:
        """
        Get the authentication key for a device
        
        Args:
            guid: Device GUID
            
        Returns:
            Device's public key or None if not found
        """
        device = self._devices.get(guid)
        return device.auth_key if device else None
    
    def trust_device(self, guid: str, trusted: bool = True) -> bool:
        """
        Set device trust status
        
        Args:
            guid: Device GUID
            trusted: Whether to trust the device
            
        Returns:
            True if device was updated, False if not found
        """
        device = self._devices.get(guid)
        if device:
            device.is_trusted = trusted
            self._save_registry()
            logger.info(f"Set trust status for {guid}: {trusted}")
            return True
        return False
    
    def cleanup_old_devices(self, max_age_days: int = 30) -> int:
        """
        Remove devices that haven't been seen for a long time
        
        Args:
            max_age_days: Maximum age in days
            
        Returns:
            Number of devices removed
        """
        cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
        old_devices = [
            guid for guid, device in self._devices.items()
            if device.last_seen < cutoff_time
        ]
        
        for guid in old_devices:
            self.remove_device(guid)
        
        if old_devices:
            logger.info(f"Cleaned up {len(old_devices)} old devices")
        
        return len(old_devices)
    
    def get_stats(self) -> Dict:
        """
        Get registry statistics
        
        Returns:
            Dictionary with registry statistics
        """
        devices = list(self._devices.values())
        trusted_count = sum(1 for d in devices if d.is_trusted)
        
        return {
            'total_devices': len(devices),
            'trusted_devices': trusted_count,
            'untrusted_devices': len(devices) - trusted_count,
            'most_recent_connection': max((d.last_seen for d in devices), default=0),
            'total_connections': sum(d.connection_count for d in devices)
        }


# Global registry instance
_registry: Optional[DeviceRegistry] = None


def get_device_registry(registry_dir: Optional[str] = None) -> DeviceRegistry:
    """
    Get the global device registry instance
    
    Args:
        registry_dir: Registry directory (only used on first call)
        
    Returns:
        DeviceRegistry instance
    """
    global _registry
    if _registry is None:
        _registry = DeviceRegistry(registry_dir)
    return _registry


def register_device(guid: str, name: str, ip_address: str, port: int, 
                   auth_key: str, registry_dir: Optional[str] = None) -> None:
    """
    Register a new device
    
    Args:
        guid: Device GUID
        name: Device name
        ip_address: Device IP address
        port: Device port
        auth_key: Device's public key
        registry_dir: Registry directory (optional)
    """
    registry = get_device_registry(registry_dir)
    
    device = DeviceInfo(
        guid=guid,
        name=name,
        ip_address=ip_address,
        port=port,
        auth_key=auth_key,
        paired_timestamp=time.time(),
        last_seen=time.time()
    )
    
    registry.add_device(device)


def is_device_known(guid: str, registry_dir: Optional[str] = None) -> bool:
    """
    Check if a device is known
    
    Args:
        guid: Device GUID
        registry_dir: Registry directory (optional)
        
    Returns:
        True if device is known
    """
    registry = get_device_registry(registry_dir)
    return registry.is_device_known(guid)
