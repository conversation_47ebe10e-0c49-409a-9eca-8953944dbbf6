"""
mDNS Service Discovery for ADB

This module implements mDNS-based discovery of ADB devices on the local network,
similar to the Android ADB implementation.
"""

import socket
import struct
import threading
import time
from typing import Dict, List, Optional, Callable, NamedTuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class AdbService:
    """Information about a discovered ADB service"""
    instance_name: str      # Device instance name (usually GUID)
    service_type: str       # Service type (_adb-tls-connect._tcp, etc.)
    hostname: str           # Device hostname
    ip_address: str         # Device IP address
    port: int              # Service port
    txt_records: Dict[str, str]  # TXT record data
    discovered_time: float  # When service was discovered
    
    @property
    def is_pairing_service(self) -> bool:
        """Check if this is a pairing service"""
        return "_adb-tls-pairing._tcp" in self.service_type
    
    @property
    def is_connect_service(self) -> bool:
        """Check if this is a connect service"""
        return "_adb-tls-connect._tcp" in self.service_type or "_adb._tcp" in self.service_type
    
    @property
    def is_secure(self) -> bool:
        """Check if this is a secure (TLS) service"""
        return "tls" in self.service_type


class SimpleMdnsClient:
    """Simple mDNS client for ADB service discovery"""
    
    # ADB service types to discover
    ADB_SERVICE_TYPES = [
        "_adb._tcp.local.",              # Legacy ADB service
        "_adb-tls-connect._tcp.local.",  # Secure ADB connection
        "_adb-tls-pairing._tcp.local.",  # ADB pairing service
    ]
    
    MDNS_ADDR = "224.0.0.251"
    MDNS_PORT = 5353
    
    def __init__(self):
        self.socket: Optional[socket.socket] = None
        self.running = False
        self.services: Dict[str, AdbService] = {}
        self.callbacks: List[Callable[[AdbService], None]] = []
        self._lock = threading.Lock()
        self._discovery_thread: Optional[threading.Thread] = None
    
    def add_service_callback(self, callback: Callable[[AdbService], None]) -> None:
        """
        Add a callback to be called when services are discovered
        
        Args:
            callback: Function to call with discovered services
        """
        with self._lock:
            self.callbacks.append(callback)
    
    def remove_service_callback(self, callback: Callable[[AdbService], None]) -> None:
        """
        Remove a service discovery callback
        
        Args:
            callback: Callback function to remove
        """
        with self._lock:
            if callback in self.callbacks:
                self.callbacks.remove(callback)
    
    def _create_query_packet(self, service_type: str) -> bytes:
        """
        Create an mDNS query packet for a service type
        
        Args:
            service_type: Service type to query
            
        Returns:
            mDNS query packet bytes
        """
        # Simple mDNS query packet structure
        # Transaction ID (2 bytes) + Flags (2 bytes) + Questions (2 bytes) + 
        # Answer RRs (2 bytes) + Authority RRs (2 bytes) + Additional RRs (2 bytes)
        header = struct.pack('>HHHHHH', 0, 0, 1, 0, 0, 0)
        
        # Encode the service type as a DNS name
        name_parts = service_type.split('.')
        name_bytes = b''
        for part in name_parts:
            if part:  # Skip empty parts
                name_bytes += struct.pack('B', len(part)) + part.encode('ascii')
        name_bytes += b'\x00'  # Null terminator
        
        # Query type: PTR (12), Class: IN (1)
        query = name_bytes + struct.pack('>HH', 12, 1)
        
        return header + query
    
    def _parse_mdns_response(self, data: bytes, source_addr: str) -> List[AdbService]:
        """
        Parse mDNS response packet
        
        Args:
            data: mDNS response packet
            source_addr: Source IP address
            
        Returns:
            List of discovered services
        """
        services = []
        
        try:
            # This is a simplified parser - a full implementation would need
            # to properly parse the DNS packet format
            # For now, we'll use a heuristic approach to extract service info
            
            # Look for ADB service patterns in the response
            data_str = data.decode('ascii', errors='ignore')
            
            for service_type in self.ADB_SERVICE_TYPES:
                service_name = service_type.replace('.local.', '')
                if service_name in data_str:
                    # Extract port from the response (this is simplified)
                    # In a real implementation, we'd parse the SRV record
                    port = 5555  # Default ADB port
                    
                    # Try to extract actual port from SRV record
                    srv_pos = data_str.find(service_name)
                    if srv_pos >= 0:
                        # Look for port number patterns
                        for test_port in [5555, 5556, 37000, 37001]:
                            if str(test_port) in data_str[srv_pos:srv_pos+100]:
                                port = test_port
                                break
                    
                    service = AdbService(
                        instance_name=f"device_{source_addr}",
                        service_type=service_type,
                        hostname=source_addr,
                        ip_address=source_addr,
                        port=port,
                        txt_records={},
                        discovered_time=time.time()
                    )
                    services.append(service)
                    
        except Exception as e:
            logger.debug(f"Failed to parse mDNS response from {source_addr}: {e}")
        
        return services
    
    def _discovery_loop(self) -> None:
        """Main discovery loop"""
        logger.info("Starting mDNS discovery loop")
        
        try:
            # Create multicast socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # Join multicast group
            mreq = struct.pack('4sl', socket.inet_aton(self.MDNS_ADDR), socket.INADDR_ANY)
            self.socket.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)
            
            # Bind to multicast address
            self.socket.bind(('', self.MDNS_PORT))
            self.socket.settimeout(1.0)
            
            last_query_time = 0
            query_interval = 5.0  # Query every 5 seconds
            
            while self.running:
                current_time = time.time()
                
                # Send queries periodically
                if current_time - last_query_time >= query_interval:
                    for service_type in self.ADB_SERVICE_TYPES:
                        try:
                            query_packet = self._create_query_packet(service_type)
                            self.socket.sendto(query_packet, (self.MDNS_ADDR, self.MDNS_PORT))
                        except Exception as e:
                            logger.debug(f"Failed to send query for {service_type}: {e}")
                    
                    last_query_time = current_time
                
                # Listen for responses
                try:
                    data, addr = self.socket.recvfrom(4096)
                    source_ip = addr[0]
                    
                    # Skip our own packets
                    if source_ip == socket.gethostbyname(socket.gethostname()):
                        continue
                    
                    services = self._parse_mdns_response(data, source_ip)
                    
                    for service in services:
                        service_key = f"{service.ip_address}:{service.port}:{service.service_type}"
                        
                        with self._lock:
                            # Update or add service
                            if service_key not in self.services:
                                self.services[service_key] = service
                                logger.info(f"Discovered ADB service: {service.ip_address}:{service.port} ({service.service_type})")
                                
                                # Notify callbacks
                                for callback in self.callbacks:
                                    try:
                                        callback(service)
                                    except Exception as e:
                                        logger.error(f"Service callback error: {e}")
                            else:
                                # Update discovery time
                                self.services[service_key].discovered_time = service.discovered_time
                
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.running:
                        logger.error(f"Error in discovery loop: {e}")
                    break
        
        except Exception as e:
            logger.error(f"Failed to start mDNS discovery: {e}")
        finally:
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None
        
        logger.info("mDNS discovery loop stopped")
    
    def start_discovery(self) -> bool:
        """
        Start mDNS service discovery
        
        Returns:
            True if discovery started successfully
        """
        if self.running:
            return True
        
        self.running = True
        self._discovery_thread = threading.Thread(target=self._discovery_loop, daemon=True)
        self._discovery_thread.start()
        
        # Give it a moment to start
        time.sleep(0.1)
        return self._discovery_thread.is_alive()
    
    def stop_discovery(self) -> None:
        """Stop mDNS service discovery"""
        if not self.running:
            return
        
        self.running = False
        
        if self._discovery_thread and self._discovery_thread.is_alive():
            self._discovery_thread.join(timeout=2.0)
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        logger.info("mDNS discovery stopped")
    
    def get_discovered_services(self, service_type: Optional[str] = None) -> List[AdbService]:
        """
        Get list of discovered services
        
        Args:
            service_type: Filter by service type (optional)
            
        Returns:
            List of discovered ADB services
        """
        with self._lock:
            services = list(self.services.values())
        
        if service_type:
            services = [s for s in services if service_type in s.service_type]
        
        # Remove old services (older than 30 seconds)
        current_time = time.time()
        services = [s for s in services if current_time - s.discovered_time < 30]
        
        return services
    
    def get_pairing_services(self) -> List[AdbService]:
        """Get list of discovered pairing services"""
        return [s for s in self.get_discovered_services() if s.is_pairing_service]
    
    def get_connect_services(self) -> List[AdbService]:
        """Get list of discovered connect services"""
        return [s for s in self.get_discovered_services() if s.is_connect_service]
    
    def clear_services(self) -> None:
        """Clear all discovered services"""
        with self._lock:
            self.services.clear()


# Global mDNS client instance
_mdns_client: Optional[SimpleMdnsClient] = None


def get_mdns_client() -> SimpleMdnsClient:
    """
    Get the global mDNS client instance
    
    Returns:
        SimpleMdnsClient instance
    """
    global _mdns_client
    if _mdns_client is None:
        _mdns_client = SimpleMdnsClient()
    return _mdns_client


def discover_adb_services(timeout: float = 10.0) -> List[AdbService]:
    """
    Discover ADB services on the network
    
    Args:
        timeout: Discovery timeout in seconds
        
    Returns:
        List of discovered ADB services
    """
    client = get_mdns_client()
    
    if not client.running:
        if not client.start_discovery():
            logger.error("Failed to start mDNS discovery")
            return []
    
    # Wait for discovery
    time.sleep(timeout)
    
    return client.get_discovered_services()


def find_pairing_services(timeout: float = 5.0) -> List[AdbService]:
    """
    Find ADB pairing services on the network
    
    Args:
        timeout: Discovery timeout in seconds
        
    Returns:
        List of discovered pairing services
    """
    client = get_mdns_client()
    
    if not client.running:
        if not client.start_discovery():
            logger.error("Failed to start mDNS discovery")
            return []
    
    # Wait for discovery
    time.sleep(timeout)
    
    return client.get_pairing_services()
