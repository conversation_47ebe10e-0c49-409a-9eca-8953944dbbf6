"""
ADB Port Forwarding Implementation

This module implements ADB port forwarding functionality, including:
- Forward port forwarding (phone -> host)
- Reverse port forwarding (host -> phone)
- TCP socket management and data relay
- Compatible with official ADB port forwarding protocol

Based on the ADB source code implementation.
"""

import socket
import threading
import time
import select
import struct
from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from enum import Enum
import logging

from typing import Union
from .const import A_OPEN, A_OKAY, A_CLSE, A_WRTE
from .adb_socket import AdbSocket, AdbSocketManager

# Type alias for ADB connections
AdbConnection = Union['AdbTcpConnection', 'AdbUsbConnection']

logger = logging.getLogger(__name__)


class ForwardType(Enum):
    """Port forwarding types"""
    FORWARD = "forward"      # Phone -> Host
    REVERSE = "reverse"      # Host -> Phone


@dataclass
class ForwardSpec:
    """Port forwarding specification"""
    local_spec: str          # Local socket specification (e.g., "tcp:8080")
    remote_spec: str         # Remote socket specification (e.g., "tcp:8080")
    forward_type: ForwardType
    no_rebind: bool = False  # Don't rebind if already exists


class ForwardResult(NamedTuple):
    """Result of port forwarding operation"""
    success: bool
    local_port: int = 0      # Resolved local port (for tcp:0)
    error_message: str = ""


class PortForwardError(Exception):
    """Exception raised during port forwarding operations"""
    pass


class SocketRelay:
    """Relays data between two sockets"""
    
    def __init__(self, sock1: socket.socket, sock2: socket.socket, name: str = "relay"):
        self.sock1 = sock1
        self.sock2 = sock2
        self.name = name
        self.running = False
        self.threads: List[threading.Thread] = []
        self.bytes_transferred = 0
        self._lock = threading.Lock()
    
    def start(self) -> None:
        """Start the relay threads"""
        if self.running:
            return
        
        self.running = True
        
        # Create relay threads for both directions
        thread1 = threading.Thread(
            target=self._relay_data,
            args=(self.sock1, self.sock2, f"{self.name}-1to2"),
            daemon=True
        )
        thread2 = threading.Thread(
            target=self._relay_data,
            args=(self.sock2, self.sock1, f"{self.name}-2to1"),
            daemon=True
        )
        
        self.threads = [thread1, thread2]
        thread1.start()
        thread2.start()
        
        logger.debug(f"Started socket relay: {self.name}")
    
    def stop(self) -> None:
        """Stop the relay"""
        if not self.running:
            return
        
        self.running = False
        
        # Close sockets to interrupt blocking operations
        try:
            self.sock1.close()
        except:
            pass
        
        try:
            self.sock2.close()
        except:
            pass
        
        # Wait for threads to finish
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=1.0)
        
        logger.debug(f"Stopped socket relay: {self.name} ({self.bytes_transferred} bytes transferred)")
    
    def _relay_data(self, source: socket.socket, dest: socket.socket, direction: str) -> None:
        """Relay data from source to destination socket"""
        try:
            while self.running:
                try:
                    # Use select to check if data is available
                    ready, _, _ = select.select([source], [], [], 1.0)
                    if not ready:
                        continue
                    
                    data = source.recv(4096)
                    if not data:
                        logger.debug(f"Socket relay {direction}: source closed")
                        break
                    
                    dest.sendall(data)
                    
                    with self._lock:
                        self.bytes_transferred += len(data)
                    
                    logger.debug(f"Socket relay {direction}: transferred {len(data)} bytes")
                
                except socket.timeout:
                    continue
                except (socket.error, OSError) as e:
                    if self.running:
                        logger.debug(f"Socket relay {direction} error: {e}")
                    break
        
        except Exception as e:
            logger.error(f"Socket relay {direction} exception: {e}")
        finally:
            self.stop()


class AdbPortForwarder:
    """ADB port forwarding manager"""
    
    def __init__(self, connection: AdbConnection):
        """
        Initialize port forwarder

        Args:
            connection: ADB connection to use
        """
        self.connection = connection
        self.socket_manager = AdbSocketManager(connection)
        self.active_forwards: Dict[str, 'ActiveForward'] = {}
        self._lock = threading.RLock()
    
    def parse_socket_spec(self, spec: str) -> Tuple[str, str, int]:
        """
        Parse socket specification
        
        Args:
            spec: Socket specification (e.g., "tcp:8080", "local:socket_name")
            
        Returns:
            Tuple of (type, host, port) or (type, path, 0) for local sockets
        """
        if ':' not in spec:
            raise PortForwardError(f"Invalid socket specification: {spec}")
        
        parts = spec.split(':', 1)
        socket_type = parts[0]
        
        if socket_type == "tcp":
            try:
                port = int(parts[1])
                return ("tcp", "localhost", port)
            except ValueError:
                raise PortForwardError(f"Invalid TCP port: {parts[1]}")
        
        elif socket_type in ["local", "localabstract", "localreserved", "localfilesystem"]:
            return (socket_type, parts[1], 0)
        
        else:
            raise PortForwardError(f"Unsupported socket type: {socket_type}")
    
    def create_local_socket(self, spec: str) -> Tuple[socket.socket, int]:
        """
        Create a local socket based on specification
        
        Args:
            spec: Socket specification
            
        Returns:
            Tuple of (socket, resolved_port)
        """
        socket_type, host, port = self.parse_socket_spec(spec)
        
        if socket_type == "tcp":
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            try:
                sock.bind((host, port))
                sock.listen(5)
                
                # Get the actual port if port was 0
                actual_port = sock.getsockname()[1]
                logger.debug(f"Created TCP socket on {host}:{actual_port}")
                
                return sock, actual_port
            
            except socket.error as e:
                sock.close()
                raise PortForwardError(f"Failed to bind TCP socket {host}:{port}: {e}")
        
        else:
            raise PortForwardError(f"Local socket type {socket_type} not yet implemented")
    
    def forward_port(self, local_spec: str, remote_spec: str, no_rebind: bool = False) -> ForwardResult:
        """
        Set up port forwarding from phone to host
        
        Args:
            local_spec: Local socket specification (e.g., "tcp:8080")
            remote_spec: Remote socket specification (e.g., "tcp:8080")
            no_rebind: Don't rebind if already exists
            
        Returns:
            ForwardResult with success status and resolved port
        """
        logger.info(f"Setting up forward: {local_spec} -> {remote_spec}")
        
        with self._lock:
            forward_key = f"forward:{local_spec}"
            
            # Check if already exists
            if forward_key in self.active_forwards:
                if no_rebind:
                    return ForwardResult(False, 0, f"Forward already exists: {local_spec}")
                else:
                    # Remove existing forward
                    self.remove_forward(local_spec)
            
            try:
                # Create local listening socket
                local_socket, resolved_port = self.create_local_socket(local_spec)
                
                # Create active forward
                active_forward = ActiveForward(
                    ForwardSpec(local_spec, remote_spec, ForwardType.FORWARD, no_rebind),
                    local_socket,
                    resolved_port,
                    self.socket_manager
                )
                
                # Start the forward
                active_forward.start()
                
                # Store in active forwards
                self.active_forwards[forward_key] = active_forward
                
                logger.info(f"Forward established: {local_spec} -> {remote_spec} (port {resolved_port})")
                return ForwardResult(True, resolved_port, "")
            
            except Exception as e:
                logger.error(f"Failed to set up forward: {e}")
                return ForwardResult(False, 0, str(e))
    
    def reverse_port(self, local_spec: str, remote_spec: str, no_rebind: bool = False) -> ForwardResult:
        """
        Set up reverse port forwarding from host to phone
        
        Args:
            local_spec: Local socket specification on phone
            remote_spec: Remote socket specification on host
            no_rebind: Don't rebind if already exists
            
        Returns:
            ForwardResult with success status
        """
        logger.info(f"Setting up reverse: {local_spec} <- {remote_spec}")
        
        with self._lock:
            reverse_key = f"reverse:{local_spec}"
            
            # Check if already exists
            if reverse_key in self.active_forwards:
                if no_rebind:
                    return ForwardResult(False, 0, f"Reverse forward already exists: {local_spec}")
                else:
                    # Remove existing reverse forward
                    self.remove_reverse(local_spec)
            
            try:
                # Send reverse forward command to device
                command = f"reverse:forward:{local_spec};{remote_spec}"
                if no_rebind:
                    command = f"reverse:forward:norebind:{local_spec};{remote_spec}"
                
                # Execute the reverse forward command
                response = self._execute_forward_command(command)
                
                if response.startswith("OKAY"):
                    # Create active reverse forward entry
                    active_forward = ActiveReverseForward(
                        ForwardSpec(local_spec, remote_spec, ForwardType.REVERSE, no_rebind),
                        self.connection
                    )
                    
                    self.active_forwards[reverse_key] = active_forward
                    
                    logger.info(f"Reverse forward established: {local_spec} <- {remote_spec}")
                    return ForwardResult(True, 0, "")
                else:
                    return ForwardResult(False, 0, f"Device rejected reverse forward: {response}")
            
            except Exception as e:
                logger.error(f"Failed to set up reverse forward: {e}")
                return ForwardResult(False, 0, str(e))
    
    def remove_forward(self, local_spec: str) -> bool:
        """
        Remove a port forward
        
        Args:
            local_spec: Local socket specification to remove
            
        Returns:
            True if removed successfully
        """
        with self._lock:
            forward_key = f"forward:{local_spec}"
            
            if forward_key in self.active_forwards:
                active_forward = self.active_forwards.pop(forward_key)
                active_forward.stop()
                logger.info(f"Removed forward: {local_spec}")
                return True
            
            return False
    
    def remove_reverse(self, local_spec: str) -> bool:
        """
        Remove a reverse port forward
        
        Args:
            local_spec: Local socket specification to remove
            
        Returns:
            True if removed successfully
        """
        with self._lock:
            reverse_key = f"reverse:{local_spec}"
            
            if reverse_key in self.active_forwards:
                try:
                    # Send kill reverse command to device
                    command = f"reverse:killforward:{local_spec}"
                    response = self._execute_forward_command(command)
                    
                    active_forward = self.active_forwards.pop(reverse_key)
                    active_forward.stop()
                    
                    logger.info(f"Removed reverse forward: {local_spec}")
                    return True
                
                except Exception as e:
                    logger.error(f"Failed to remove reverse forward: {e}")
                    return False
            
            return False
    
    def list_forwards(self) -> List[Dict[str, str]]:
        """
        List active port forwards
        
        Returns:
            List of forward information dictionaries
        """
        with self._lock:
            forwards = []
            
            for key, active_forward in self.active_forwards.items():
                forward_info = {
                    'type': active_forward.spec.forward_type.value,
                    'local': active_forward.spec.local_spec,
                    'remote': active_forward.spec.remote_spec,
                }
                
                if hasattr(active_forward, 'resolved_port') and active_forward.resolved_port:
                    forward_info['resolved_port'] = active_forward.resolved_port
                
                forwards.append(forward_info)
            
            return forwards
    
    def remove_all_forwards(self) -> int:
        """
        Remove all active forwards
        
        Returns:
            Number of forwards removed
        """
        with self._lock:
            count = len(self.active_forwards)
            
            for active_forward in self.active_forwards.values():
                active_forward.stop()
            
            self.active_forwards.clear()
            
            logger.info(f"Removed all forwards ({count} total)")
            return count
    
    def _execute_forward_command(self, command: str) -> str:
        """
        Execute a forward command on the device
        
        Args:
            command: Forward command to execute
            
        Returns:
            Response from device
        """
        # This would need to be implemented based on the specific ADB connection type
        # For now, we'll simulate the response
        logger.debug(f"Executing forward command: {command}")
        
        # In a real implementation, this would send the command via the ADB protocol
        # and wait for the response
        return "OKAY"


class ActiveForward:
    """Active port forward (phone -> host)"""

    def __init__(self, spec: ForwardSpec, local_socket: socket.socket,
                 resolved_port: int, socket_manager: AdbSocketManager):
        self.spec = spec
        self.local_socket = local_socket
        self.resolved_port = resolved_port
        self.socket_manager = socket_manager
        self.running = False
        self.accept_thread: Optional[threading.Thread] = None
        self.active_relays: List[SocketRelay] = []
        self._lock = threading.Lock()

    def start(self) -> None:
        """Start accepting connections"""
        if self.running:
            return

        self.running = True
        self.accept_thread = threading.Thread(
            target=self._accept_connections,
            daemon=True
        )
        self.accept_thread.start()

        logger.debug(f"Started forward acceptor for {self.spec.local_spec}")

    def stop(self) -> None:
        """Stop the forward"""
        if not self.running:
            return

        self.running = False

        # Close listening socket
        try:
            self.local_socket.close()
        except:
            pass

        # Stop all active relays
        with self._lock:
            for relay in self.active_relays:
                relay.stop()
            self.active_relays.clear()

        # Wait for accept thread
        if self.accept_thread and self.accept_thread.is_alive():
            self.accept_thread.join(timeout=2.0)

        logger.debug(f"Stopped forward for {self.spec.local_spec}")

    def _accept_connections(self) -> None:
        """Accept incoming connections and create relays"""
        try:
            while self.running:
                try:
                    # Accept connection with timeout
                    self.local_socket.settimeout(1.0)
                    client_socket, client_addr = self.local_socket.accept()

                    logger.debug(f"Accepted connection from {client_addr}")

                    # Create connection to remote service
                    remote_socket = self._connect_to_remote()
                    if remote_socket:
                        # Create relay between client and remote
                        relay = SocketRelay(
                            client_socket,
                            remote_socket,
                            f"forward-{client_addr[0]}:{client_addr[1]}"
                        )

                        with self._lock:
                            self.active_relays.append(relay)

                        relay.start()
                    else:
                        logger.error("Failed to connect to remote service")
                        client_socket.close()

                except socket.timeout:
                    continue
                except (socket.error, OSError) as e:
                    if self.running:
                        logger.error(f"Accept error: {e}")
                    break

        except Exception as e:
            logger.error(f"Accept thread exception: {e}")

    def _connect_to_remote(self) -> Optional[AdbSocket]:
        """
        Connect to the remote service on the device

        Returns:
            Connected ADB socket or None if failed
        """
        try:
            # Parse remote specification
            socket_type, host, port = self._parse_remote_spec()

            if socket_type == "tcp":
                # Create ADB socket connection to device service
                logger.debug(f"Connecting to remote {self.spec.remote_spec}")

                # Create ADB socket for the remote service
                adb_socket = self.socket_manager.create_socket(self.spec.remote_spec)

                # Connect to the service
                if adb_socket.connect(timeout=10.0):
                    return adb_socket
                else:
                    logger.error(f"Failed to connect ADB socket to {self.spec.remote_spec}")
                    adb_socket.close()
                    return None

            else:
                logger.error(f"Unsupported remote socket type: {socket_type}")
                return None

        except Exception as e:
            logger.error(f"Failed to connect to remote: {e}")
            return None

    def _parse_remote_spec(self) -> Tuple[str, str, int]:
        """Parse remote socket specification"""
        if ':' not in self.spec.remote_spec:
            raise PortForwardError(f"Invalid remote specification: {self.spec.remote_spec}")

        parts = self.spec.remote_spec.split(':', 1)
        socket_type = parts[0]

        if socket_type == "tcp":
            try:
                port = int(parts[1])
                return ("tcp", "localhost", port)
            except ValueError:
                raise PortForwardError(f"Invalid remote TCP port: {parts[1]}")

        else:
            return (socket_type, parts[1], 0)


class ActiveReverseForward:
    """Active reverse port forward (host -> phone)"""

    def __init__(self, spec: ForwardSpec, connection: AdbConnection):
        self.spec = spec
        self.connection = connection
        self.running = False

    def start(self) -> None:
        """Start the reverse forward (already handled by device)"""
        self.running = True
        logger.debug(f"Started reverse forward for {self.spec.local_spec}")

    def stop(self) -> None:
        """Stop the reverse forward"""
        self.running = False
        logger.debug(f"Stopped reverse forward for {self.spec.local_spec}")


# High-level convenience functions

def create_port_forwarder(connection: AdbConnection) -> AdbPortForwarder:
    """
    Create a port forwarder for the given connection

    Args:
        connection: ADB connection

    Returns:
        AdbPortForwarder instance
    """
    return AdbPortForwarder(connection)


def forward_port(connection: AdbConnection, local_port: int, remote_port: int) -> ForwardResult:
    """
    Forward a TCP port from phone to host

    Args:
        connection: ADB connection
        local_port: Local port on host (0 for auto-assign)
        remote_port: Remote port on phone

    Returns:
        ForwardResult with success status and resolved port
    """
    forwarder = AdbPortForwarder(connection)
    return forwarder.forward_port(f"tcp:{local_port}", f"tcp:{remote_port}")


def reverse_port(connection: AdbConnection, local_port: int, remote_port: int) -> ForwardResult:
    """
    Reverse forward a TCP port from host to phone

    Args:
        connection: ADB connection
        local_port: Local port on phone
        remote_port: Remote port on host

    Returns:
        ForwardResult with success status
    """
    forwarder = AdbPortForwarder(connection)
    return forwarder.reverse_port(f"tcp:{local_port}", f"tcp:{remote_port}")
