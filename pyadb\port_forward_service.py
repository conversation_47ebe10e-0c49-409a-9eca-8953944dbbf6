"""
ADB Port Forwarding Service

This module provides a high-level service interface for ADB port forwarding operations.
It integrates with the main PyADB service architecture.
"""

import logging
from typing import List, Dict, Optional

from typing import List, Dict, Optional, Union

from .port_forward import Adb<PERSON>ort<PERSON>orwarder, ForwardResult, ForwardType

logger = logging.getLogger(__name__)


class AdbPortForwardService:
    """
    High-level ADB port forwarding service
    
    This service provides convenient methods for setting up and managing
    port forwards between the host and Android device.
    """
    
    def __init__(self, connection):
        """
        Initialize port forwarding service

        Args:
            connection: ADB connection object (TCP or USB)
        """
        self.connection = connection
        self.forwarder = AdbPortForwarder(connection)
    
    def forward_tcp_port(self, local_port: int, remote_port: int, no_rebind: bool = False) -> ForwardResult:
        """
        Forward a TCP port from device to host
        
        This creates a listening socket on the host that forwards connections
        to a service running on the Android device.
        
        Args:
            local_port: Local port on host (0 for auto-assign)
            remote_port: Remote port on device
            no_rebind: Don't rebind if port is already forwarded
            
        Returns:
            ForwardResult with success status and resolved port
            
        Example:
            # Forward device port 8080 to host port 8080
            result = service.forward_tcp_port(8080, 8080)
            if result.success:
                print(f"Port forwarding active on port {result.local_port}")
        """
        logger.info(f"Setting up TCP forward: host:{local_port} -> device:{remote_port}")
        
        return self.forwarder.forward_port(
            f"tcp:{local_port}",
            f"tcp:{remote_port}",
            no_rebind=no_rebind
        )
    
    def reverse_tcp_port(self, device_port: int, host_port: int, no_rebind: bool = False) -> ForwardResult:
        """
        Reverse forward a TCP port from host to device
        
        This creates a listening socket on the device that forwards connections
        to a service running on the host.
        
        Args:
            device_port: Port on device to listen on
            host_port: Port on host to connect to
            no_rebind: Don't rebind if port is already forwarded
            
        Returns:
            ForwardResult with success status
            
        Example:
            # Forward host port 3000 to device port 3000
            result = service.reverse_tcp_port(3000, 3000)
            if result.success:
                print("Reverse port forwarding established")
        """
        logger.info(f"Setting up TCP reverse: device:{device_port} -> host:{host_port}")
        
        return self.forwarder.reverse_port(
            f"tcp:{device_port}",
            f"tcp:{host_port}",
            no_rebind=no_rebind
        )
    
    def forward_port_range(self, local_start: int, remote_start: int, count: int) -> List[ForwardResult]:
        """
        Forward a range of TCP ports
        
        Args:
            local_start: Starting local port
            remote_start: Starting remote port
            count: Number of ports to forward
            
        Returns:
            List of ForwardResult for each port
        """
        results = []
        
        for i in range(count):
            local_port = local_start + i
            remote_port = remote_start + i
            
            result = self.forward_tcp_port(local_port, remote_port)
            results.append(result)
            
            if not result.success:
                logger.warning(f"Failed to forward port {local_port}: {result.error_message}")
        
        return results
    
    def remove_forward(self, local_port: int) -> bool:
        """
        Remove a port forward
        
        Args:
            local_port: Local port to stop forwarding
            
        Returns:
            True if removed successfully
        """
        return self.forwarder.remove_forward(f"tcp:{local_port}")
    
    def remove_reverse(self, device_port: int) -> bool:
        """
        Remove a reverse port forward
        
        Args:
            device_port: Device port to stop forwarding
            
        Returns:
            True if removed successfully
        """
        return self.forwarder.remove_reverse(f"tcp:{device_port}")
    
    def list_forwards(self) -> List[Dict[str, str]]:
        """
        List all active port forwards
        
        Returns:
            List of forward information dictionaries
        """
        return self.forwarder.list_forwards()
    
    def remove_all_forwards(self) -> int:
        """
        Remove all active port forwards
        
        Returns:
            Number of forwards removed
        """
        return self.forwarder.remove_all_forwards()
    
    def get_forward_stats(self) -> Dict[str, int]:
        """
        Get port forwarding statistics
        
        Returns:
            Dictionary with forwarding statistics
        """
        forwards = self.list_forwards()
        
        stats = {
            'total_forwards': len(forwards),
            'forward_count': len([f for f in forwards if f['type'] == 'forward']),
            'reverse_count': len([f for f in forwards if f['type'] == 'reverse']),
        }
        
        return stats
    
    def test_forward(self, local_port: int, timeout: float = 5.0) -> bool:
        """
        Test if a port forward is working
        
        Args:
            local_port: Local port to test
            timeout: Connection timeout
            
        Returns:
            True if port is accessible
        """
        import socket
        
        try:
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(timeout)
            
            result = test_socket.connect_ex(('localhost', local_port))
            test_socket.close()
            
            return result == 0
        
        except Exception as e:
            logger.debug(f"Forward test failed for port {local_port}: {e}")
            return False
    
    def wait_for_forward(self, local_port: int, timeout: float = 30.0) -> bool:
        """
        Wait for a port forward to become active
        
        Args:
            local_port: Local port to wait for
            timeout: Maximum time to wait
            
        Returns:
            True if port becomes active within timeout
        """
        import time
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.test_forward(local_port, timeout=1.0):
                return True
            
            time.sleep(0.5)
        
        return False
    
    def create_tunnel(self, local_port: int, remote_host: str, remote_port: int) -> ForwardResult:
        """
        Create a tunnel through the device to a remote host
        
        This is useful for accessing services that are only reachable from the device.
        
        Args:
            local_port: Local port on host
            remote_host: Remote host to connect to from device
            remote_port: Remote port to connect to
            
        Returns:
            ForwardResult with success status
        """
        # For tunneling, we need to use a different approach
        # This would typically involve setting up a proxy service on the device
        logger.info(f"Creating tunnel: host:{local_port} -> device -> {remote_host}:{remote_port}")
        
        # This is a placeholder - full implementation would require
        # a proxy service running on the device
        return ForwardResult(False, 0, "Tunneling not yet implemented")
    
    def cleanup(self) -> None:
        """Clean up all port forwards"""
        count = self.remove_all_forwards()
        logger.info(f"Cleaned up {count} port forwards")


# Convenience functions for direct use

def create_port_forward_service(connection):
    """
    Create a port forwarding service
    
    Args:
        connection: ADB connection
        
    Returns:
        AdbPortForwardService instance
    """
    return AdbPortForwardService(connection)


def forward_port(connection, local_port: int, remote_port: int):
    """
    Quick port forward setup
    
    Args:
        connection: ADB connection
        local_port: Local port on host
        remote_port: Remote port on device
        
    Returns:
        ForwardResult with success status
    """
    service = AdbPortForwardService(connection)
    return service.forward_tcp_port(local_port, remote_port)


def reverse_port(connection, device_port: int, host_port: int):
    """
    Quick reverse port forward setup
    
    Args:
        connection: ADB connection
        device_port: Port on device
        host_port: Port on host
        
    Returns:
        ForwardResult with success status
    """
    service = AdbPortForwardService(connection)
    return service.reverse_tcp_port(device_port, host_port)
