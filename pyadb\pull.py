"""
ADB File Pull Service Implementation

This module implements the ADB file sync protocol for pulling files
from Android devices. It handles the sync protocol, file transfer,
and progress reporting.
"""

import os
import struct
import time
from typing import Optional, Callable, BinaryIO
from pathlib import Path
import logging

from .const import A_OPEN, A_OKAY, A_WRTE, A_CLSE
from .core import create_open_packet, create_ready_packet, create_close_packet

logger = logging.getLogger(__name__)

# File sync protocol constants (from file_sync_protocol.h)
ID_LSTAT_V1 = 0x54415453  # 'STAT'
ID_STAT_V2 = 0x32415453   # 'STA2'
ID_LSTAT_V2 = 0x3254534C  # 'LST2'
ID_LIST_V1 = 0x5453494C   # 'LIST'
ID_LIST_V2 = 0x3253494C   # 'LIS2'
ID_DENT_V1 = 0x544E4544   # 'DENT'
ID_DENT_V2 = 0x32544E44   # 'DNT2'
ID_SEND_V1 = 0x444E4553   # 'SEND'
ID_SEND_V2 = 0x32444E53   # 'SND2'
ID_RECV_V1 = 0x56434552   # 'RECV'
ID_RECV_V2 = 0x32564352   # 'RCV2'
ID_DONE = 0x454E4F44      # 'DONE'
ID_DATA = 0x41544144      # 'DATA'
ID_OKAY = 0x59414B4F      # 'OKAY'
ID_FAIL = 0x4C494146      # 'FAIL'
ID_QUIT = 0x54495551      # 'QUIT'

# Sync data maximum size
SYNC_DATA_MAX = 64 * 1024


class SyncRequest:
    """File sync protocol request structure"""

    def __init__(self, sync_id: int, path: str):
        self.id = sync_id
        self.path_length = len(path)
        self.path = path

    def to_bytes(self) -> bytes:
        """Convert to wire format"""
        header = struct.pack('<II', self.id, self.path_length)
        return header + self.path.encode('utf-8')


class SyncResponse:
    """File sync protocol response structure"""

    def __init__(self, sync_id: int, data: bytes = b''):
        self.id = sync_id
        self.data = data

    @classmethod
    def from_bytes(cls, data: bytes) -> 'SyncResponse':
        """Parse from wire format"""
        if len(data) < 4:
            raise ValueError("Insufficient data for sync response")

        sync_id = struct.unpack('<I', data[:4])[0]
        return cls(sync_id, data[4:])


class SyncStatV2:
    """File sync stat v2 response structure"""

    def __init__(self, data: bytes):
        if len(data) < 72:  # Size of sync_stat_v2 struct
            raise ValueError("Insufficient data for stat v2 response")

        fields = struct.unpack('<IIQQQIIIQQQQ', data[:72])
        self.id = fields[0]
        self.error = fields[1]
        self.dev = fields[2]
        self.ino = fields[3]
        self.mode = fields[4]
        self.nlink = fields[5]
        self.uid = fields[6]
        self.gid = fields[7]
        self.size = fields[8]
        self.atime = fields[9]
        self.mtime = fields[10]
        self.ctime = fields[11]


class SyncDataPacket:
    """File sync data packet structure"""

    def __init__(self, data: bytes):
        self.id = ID_DATA
        self.size = len(data)
        self.data = data

    @classmethod
    def from_bytes(cls, data: bytes) -> 'SyncDataPacket':
        """Parse from wire format"""
        if len(data) < 8:
            raise ValueError("Insufficient data for sync data packet")

        sync_id, size = struct.unpack('<II', data[:8])
        if sync_id != ID_DATA:
            raise ValueError(f"Expected DATA packet, got {sync_id:08x}")

        payload = data[8:8+size]
        if len(payload) != size:
            raise ValueError(f"Incomplete data packet: expected {size}, got {len(payload)}")

        packet = cls(payload)
        return packet


class AdbPullService:
    """
    ADB file pull service for downloading files from Android devices.

    Implements the ADB file sync protocol for efficient file transfer
    with support for progress reporting and error handling.
    """

    def __init__(self, connection, features: list = None):
        """
        Initialize pull service.

        Args:
            connection: ADB connection object (TCP or USB)
            features: List of supported features
        """
        self.connection = connection
        self.features = features or []
        self.supports_stat_v2 = 'stat_v2' in self.features
        self.supports_sendrecv_v2 = 'sendrecv_v2' in self.features
        self._sync_connection = None

        logger.debug(f"Pull service initialized (stat_v2: {self.supports_stat_v2}, sendrecv_v2: {self.supports_sendrecv_v2})")

    def _establish_sync_connection(self) -> bool:
        """Establish sync service connection"""
        if self._sync_connection:
            return True

        # Open sync service
        local_id = 1  # Simple local ID for sync
        open_packet = create_open_packet(local_id, "sync:")

        if not self.connection.send_packet(open_packet):
            logger.error("Failed to send sync open packet")
            return False

        # Wait for OKAY response
        response = self.connection.receive_packet()
        if not response or response.message.command != A_OKAY:
            logger.error(f"Unexpected response to sync open: {response}")
            return False

        self._sync_connection = {
            'local_id': local_id,
            'remote_id': response.message.arg0
        }

        logger.debug("Sync connection established")
        return True

    def _close_sync_connection(self) -> None:
        """Close sync service connection"""
        if not self._sync_connection:
            return

        try:
            # Send QUIT command
            quit_request = struct.pack('<I', ID_QUIT)
            write_packet = create_write_packet(
                self._sync_connection['local_id'],
                self._sync_connection['remote_id'],
                quit_request
            )
            self.connection.send_packet(write_packet)

            # Send close packet
            close_packet = create_close_packet(
                self._sync_connection['local_id'],
                self._sync_connection['remote_id']
            )
            self.connection.send_packet(close_packet)

        except Exception as e:
            logger.debug(f"Error closing sync connection: {e}")
        finally:
            self._sync_connection = None
            logger.debug("Sync connection closed")

    def _send_sync_request(self, request: SyncRequest) -> bool:
        """Send sync protocol request"""
        if not self._sync_connection:
            return False

        try:
            data = request.to_bytes()
            write_packet = create_write_packet(
                self._sync_connection['local_id'],
                self._sync_connection['remote_id'],
                data
            )
            return self.connection.send_packet(write_packet)
        except Exception as e:
            logger.error(f"Error sending sync request: {e}")
            return False

    def _receive_sync_data(self) -> Optional[bytes]:
        """Receive sync protocol data"""
        if not self._sync_connection:
            return None

        try:
            packet = self.connection.receive_packet()
            if not packet or packet.message.command != A_WRTE:
                logger.error(f"Unexpected packet in sync receive: {packet}")
                return None

            # Send acknowledgment
            ack_packet = create_ready_packet(
                self._sync_connection['local_id'],
                self._sync_connection['remote_id']
            )
            self.connection.send_packet(ack_packet)

            return packet.payload
        except Exception as e:
            logger.error(f"Error receiving sync data: {e}")
            return None

    def stat_file(self, remote_path: str) -> Optional[dict]:
        """
        Get file statistics from remote device.

        Args:
            remote_path: Path to file on device

        Returns:
            Dictionary with file stats or None if failed
        """
        if not self._establish_sync_connection():
            return None

        try:
            # Send stat request
            if self.supports_stat_v2:
                request = SyncRequest(ID_LSTAT_V2, remote_path)
            else:
                request = SyncRequest(ID_LSTAT_V1, remote_path)

            if not self._send_sync_request(request):
                return None

            # Receive response
            response_data = self._receive_sync_data()
            if not response_data:
                return None

            if self.supports_stat_v2:
                stat_info = SyncStatV2(response_data)
                if stat_info.error != 0:
                    logger.error(f"Stat error for {remote_path}: {stat_info.error}")
                    return None

                return {
                    'mode': stat_info.mode,
                    'size': stat_info.size,
                    'mtime': stat_info.mtime,
                    'atime': stat_info.atime,
                    'ctime': stat_info.ctime,
                    'uid': stat_info.uid,
                    'gid': stat_info.gid,
                    'dev': stat_info.dev,
                    'ino': stat_info.ino,
                    'nlink': stat_info.nlink
                }
            else:
                # Parse v1 stat response
                if len(response_data) < 16:
                    logger.error("Invalid stat v1 response")
                    return None

                fields = struct.unpack('<IIII', response_data[:16])
                return {
                    'mode': fields[1],
                    'size': fields[2],
                    'mtime': fields[3],
                    'atime': 0,
                    'ctime': 0,
                    'uid': 0,
                    'gid': 0,
                    'dev': 0,
                    'ino': 0,
                    'nlink': 0
                }

        except Exception as e:
            logger.error(f"Error getting file stats: {e}")
            return None

    def pull_file(self, remote_path: str, local_path: str,
                  progress_callback: Optional[Callable[[int, int], None]] = None) -> bool:
        """
        Pull a file from the remote device.

        Args:
            remote_path: Path to file on device
            local_path: Local path to save file
            progress_callback: Optional callback for progress updates (bytes_received, total_bytes)

        Returns:
            True if successful, False otherwise
        """
        if not self._establish_sync_connection():
            return False

        try:
            # First, get file stats to know the size
            stat_info = self.stat_file(remote_path)
            if not stat_info:
                logger.error(f"Cannot stat remote file: {remote_path}")
                return False

            file_size = stat_info['size']
            logger.info(f"Pulling {remote_path} ({file_size} bytes) to {local_path}")

            # Send receive request
            if self.supports_sendrecv_v2:
                request = SyncRequest(ID_RECV_V2, remote_path)
                # TODO: Add compression support
            else:
                request = SyncRequest(ID_RECV_V1, remote_path)

            if not self._send_sync_request(request):
                return False

            # Create local file
            local_file_path = Path(local_path)
            local_file_path.parent.mkdir(parents=True, exist_ok=True)

            bytes_received = 0

            with open(local_file_path, 'wb') as local_file:
                while bytes_received < file_size:
                    # Receive data packet
                    response_data = self._receive_sync_data()
                    if not response_data:
                        logger.error("Failed to receive data packet")
                        return False

                    # Parse sync response
                    if len(response_data) < 4:
                        logger.error("Invalid sync response")
                        return False

                    response_id = struct.unpack('<I', response_data[:4])[0]

                    if response_id == ID_DATA:
                        # Data packet
                        if len(response_data) < 8:
                            logger.error("Invalid data packet")
                            return False

                        data_size = struct.unpack('<I', response_data[4:8])[0]
                        data = response_data[8:8+data_size]

                        if len(data) != data_size:
                            logger.error(f"Incomplete data packet: expected {data_size}, got {len(data)}")
                            return False

                        local_file.write(data)
                        bytes_received += len(data)

                        if progress_callback:
                            progress_callback(bytes_received, file_size)

                    elif response_id == ID_DONE:
                        # Transfer complete
                        break

                    elif response_id == ID_FAIL:
                        # Transfer failed
                        error_msg = response_data[8:].decode('utf-8', errors='ignore')
                        logger.error(f"Pull failed: {error_msg}")
                        return False

                    else:
                        logger.error(f"Unexpected sync response: {response_id:08x}")
                        return False

            logger.info(f"Successfully pulled {bytes_received} bytes")
            return True

        except Exception as e:
            logger.error(f"Error pulling file: {e}")
            return False

        finally:
            self._close_sync_connection()

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self._close_sync_connection()