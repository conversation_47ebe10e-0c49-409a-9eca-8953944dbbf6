"""
ADB File Push Service Implementation

This module implements the ADB file sync protocol for pushing files
to Android devices. It handles the sync protocol, file transfer,
and progress reporting.
"""

import os
import struct
import time
from typing import Optional, Callable, BinaryIO
from pathlib import Path
import logging

from .const import A_OPEN, A_OKAY, A_WRTE, A_CLSE
from .core import create_open_packet, create_ready_packet, create_close_packet, create_write_packet

logger = logging.getLogger(__name__)

# Import constants from pull.py to avoid duplication
from .pull import (
    ID_SEND_V1, ID_SEND_V2, ID_DATA, ID_DONE, ID_OKAY, ID_FAIL, ID_QUIT,
    SYNC_DATA_MAX, SyncRequest
)


class AdbPushService:
    """
    ADB file push service for uploading files to Android devices.

    Implements the ADB file sync protocol for efficient file transfer
    with support for progress reporting and error handling.
    """

    def __init__(self, connection, features: list = None):
        """
        Initialize push service.

        Args:
            connection: ADB connection object (TCP or USB)
            features: List of supported features
        """
        self.connection = connection
        self.features = features or []
        self.supports_sendrecv_v2 = 'sendrecv_v2' in self.features
        self._sync_connection = None

        logger.debug(f"Push service initialized (sendrecv_v2: {self.supports_sendrecv_v2})")

    def _establish_sync_connection(self) -> bool:
        """Establish sync service connection"""
        if self._sync_connection:
            return True

        # Open sync service
        local_id = 2  # Different local ID from pull service
        open_packet = create_open_packet(local_id, "sync:")

        if not self.connection.send_packet(open_packet):
            logger.error("Failed to send sync open packet")
            return False

        # Wait for OKAY response
        response = self.connection.receive_packet()
        if not response or response.message.command != A_OKAY:
            logger.error(f"Unexpected response to sync open: {response}")
            return False

        self._sync_connection = {
            'local_id': local_id,
            'remote_id': response.message.arg0
        }

        logger.debug("Sync connection established")
        return True

    def _close_sync_connection(self) -> None:
        """Close sync service connection"""
        if not self._sync_connection:
            return

        try:
            # Send QUIT command
            quit_request = struct.pack('<I', ID_QUIT)
            write_packet = create_write_packet(
                self._sync_connection['local_id'],
                self._sync_connection['remote_id'],
                quit_request
            )
            self.connection.send_packet(write_packet)

            # Send close packet
            close_packet = create_close_packet(
                self._sync_connection['local_id'],
                self._sync_connection['remote_id']
            )
            self.connection.send_packet(close_packet)

        except Exception as e:
            logger.debug(f"Error closing sync connection: {e}")
        finally:
            self._sync_connection = None
            logger.debug("Sync connection closed")

    def _send_sync_request(self, request: SyncRequest) -> bool:
        """Send sync protocol request"""
        if not self._sync_connection:
            return False

        try:
            data = request.to_bytes()
            write_packet = create_write_packet(
                self._sync_connection['local_id'],
                self._sync_connection['remote_id'],
                data
            )
            return self.connection.send_packet(write_packet)
        except Exception as e:
            logger.error(f"Error sending sync request: {e}")
            return False

    def _send_sync_data(self, data: bytes) -> bool:
        """Send sync protocol data"""
        if not self._sync_connection:
            return False

        try:
            write_packet = create_write_packet(
                self._sync_connection['local_id'],
                self._sync_connection['remote_id'],
                data
            )
            return self.connection.send_packet(write_packet)
        except Exception as e:
            logger.error(f"Error sending sync data: {e}")
            return False

    def _receive_sync_response(self) -> Optional[bytes]:
        """Receive sync protocol response"""
        if not self._sync_connection:
            return None

        try:
            packet = self.connection.receive_packet()
            if not packet or packet.message.command != A_WRTE:
                logger.error(f"Unexpected packet in sync receive: {packet}")
                return None

            # Send acknowledgment
            ack_packet = create_ready_packet(
                self._sync_connection['local_id'],
                self._sync_connection['remote_id']
            )
            self.connection.send_packet(ack_packet)

            return packet.payload
        except Exception as e:
            logger.error(f"Error receiving sync response: {e}")
            return None

    def push_file(self, local_path: str, remote_path: str,
                  mode: int = 0o644,
                  progress_callback: Optional[Callable[[int, int], None]] = None) -> bool:
        """
        Push a file to the remote device.

        Args:
            local_path: Local file path
            remote_path: Remote path to save file
            mode: File permissions (default: 0o644)
            progress_callback: Optional callback for progress updates (bytes_sent, total_bytes)

        Returns:
            True if successful, False otherwise
        """
        if not self._establish_sync_connection():
            return False

        try:
            local_file_path = Path(local_path)
            if not local_file_path.exists():
                logger.error(f"Local file does not exist: {local_path}")
                return False

            file_size = local_file_path.stat().st_size
            logger.info(f"Pushing {local_path} ({file_size} bytes) to {remote_path}")

            # Send send request
            if self.supports_sendrecv_v2:
                # Send v2 protocol: first send path, then send mode/flags
                request = SyncRequest(ID_SEND_V2, remote_path)
                if not self._send_sync_request(request):
                    return False

                # Send mode and flags
                send_v2_data = struct.pack('<III', ID_SEND_V2, mode, 0)  # flags = 0 for now
                if not self._send_sync_data(send_v2_data):
                    return False
            else:
                # Send v1 protocol: path,mode as string
                path_mode = f"{remote_path},{mode}"
                request = SyncRequest(ID_SEND_V1, path_mode)
                if not self._send_sync_request(request):
                    return False

            bytes_sent = 0

            # Send file data in chunks
            with open(local_file_path, 'rb') as local_file:
                while bytes_sent < file_size:
                    # Read chunk
                    chunk_size = min(SYNC_DATA_MAX, file_size - bytes_sent)
                    chunk_data = local_file.read(chunk_size)

                    if not chunk_data:
                        break

                    # Create data packet
                    data_packet = struct.pack('<II', ID_DATA, len(chunk_data)) + chunk_data

                    if not self._send_sync_data(data_packet):
                        logger.error("Failed to send data packet")
                        return False

                    bytes_sent += len(chunk_data)

                    if progress_callback:
                        progress_callback(bytes_sent, file_size)

            # Send DONE packet
            done_packet = struct.pack('<II', ID_DONE, int(time.time()))
            if not self._send_sync_data(done_packet):
                logger.error("Failed to send DONE packet")
                return False

            # Wait for response
            response_data = self._receive_sync_response()
            if not response_data:
                logger.error("No response to push operation")
                return False

            if len(response_data) < 4:
                logger.error("Invalid push response")
                return False

            response_id = struct.unpack('<I', response_data[:4])[0]

            if response_id == ID_OKAY:
                logger.info(f"Successfully pushed {bytes_sent} bytes")
                return True
            elif response_id == ID_FAIL:
                error_msg = response_data[8:].decode('utf-8', errors='ignore') if len(response_data) > 8 else "Unknown error"
                logger.error(f"Push failed: {error_msg}")
                return False
            else:
                logger.error(f"Unexpected push response: {response_id:08x}")
                return False

        except Exception as e:
            logger.error(f"Error pushing file: {e}")
            return False

        finally:
            self._close_sync_connection()

    def push_data(self, data: bytes, remote_path: str,
                  mode: int = 0o644) -> bool:
        """
        Push raw data to a file on the remote device.

        Args:
            data: Data to push
            remote_path: Remote path to save file
            mode: File permissions (default: 0o644)

        Returns:
            True if successful, False otherwise
        """
        if not self._establish_sync_connection():
            return False

        try:
            file_size = len(data)
            logger.info(f"Pushing {file_size} bytes of data to {remote_path}")

            # Send send request
            if self.supports_sendrecv_v2:
                request = SyncRequest(ID_SEND_V2, remote_path)
                if not self._send_sync_request(request):
                    return False

                send_v2_data = struct.pack('<III', ID_SEND_V2, mode, 0)
                if not self._send_sync_data(send_v2_data):
                    return False
            else:
                path_mode = f"{remote_path},{mode}"
                request = SyncRequest(ID_SEND_V1, path_mode)
                if not self._send_sync_request(request):
                    return False

            # Send data in chunks
            bytes_sent = 0
            while bytes_sent < file_size:
                chunk_size = min(SYNC_DATA_MAX, file_size - bytes_sent)
                chunk_data = data[bytes_sent:bytes_sent + chunk_size]

                data_packet = struct.pack('<II', ID_DATA, len(chunk_data)) + chunk_data

                if not self._send_sync_data(data_packet):
                    logger.error("Failed to send data packet")
                    return False

                bytes_sent += len(chunk_data)

            # Send DONE packet
            done_packet = struct.pack('<II', ID_DONE, int(time.time()))
            if not self._send_sync_data(done_packet):
                return False

            # Wait for response
            response_data = self._receive_sync_response()
            if not response_data:
                return False

            response_id = struct.unpack('<I', response_data[:4])[0]
            return response_id == ID_OKAY

        except Exception as e:
            logger.error(f"Error pushing data: {e}")
            return False

        finally:
            self._close_sync_connection()

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self._close_sync_connection()