"""
ADB Remount Service Implementation

This module implements the ADB remount functionality for remounting
system partitions as read-write. It handles the remount protocol
and partition management.
"""

import logging
from typing import Optional, List

from .const import A_OPEN, A_OKAY, A_WRTE, A_CLSE, FEATURE_REMOUNT_SHELL
from .core import create_open_packet, create_ready_packet, create_close_packet

logger = logging.getLogger(__name__)


class AdbRemountService:
    """
    ADB remount service for remounting system partitions as read-write.

    Implements the ADB remount protocol which allows modification of
    system files on rooted devices or emulators.
    """

    def __init__(self, connection, features: list = None):
        """
        Initialize remount service.

        Args:
            connection: ADB connection object (TCP or USB)
            features: List of supported features
        """
        self.connection = connection
        self.features = features or []
        self.supports_remount_shell = FEATURE_REMOUNT_SHELL in self.features
        logger.debug(f"Remount service initialized (remount_shell: {self.supports_remount_shell})")

    def remount_system(self) -> bool:
        """
        Remount system partition as read-write.

        This sends the 'remount:' command to adbd, which will attempt to
        remount system partitions with write access.

        Returns:
            True if remount command was sent successfully, False otherwise
        """
        try:
            logger.info("Requesting system remount...")

            if self.supports_remount_shell:
                # Use shell-based remount (newer devices)
                return self._remount_via_shell()
            else:
                # Use service-based remount (older devices)
                return self._remount_via_service()

        except Exception as e:
            logger.error(f"Error remounting system: {e}")
            return False

    def _remount_via_service(self) -> bool:
        """Remount using the remount: service"""
        try:
            # Open remount service connection
            local_id = 12  # Use unique local ID
            open_packet = create_open_packet(local_id, "remount:")

            if not self.connection.send_packet(open_packet):
                logger.error("Failed to send remount open packet")
                return False

            # Wait for OKAY response
            response = self.connection.receive_packet()
            if not response or response.message.command != A_OKAY:
                logger.error(f"Unexpected response to remount open: {response}")
                return False

            remote_id = response.message.arg0

            # Read response message
            response_message = ""
            while True:
                packet = self.connection.receive_packet()
                if not packet:
                    break

                if packet.message.command == A_WRTE:
                    # Received data from remount service
                    data = packet.payload.decode('utf-8', errors='ignore')
                    response_message += data

                    # Send acknowledgment
                    ack_packet = create_ready_packet(local_id, remote_id)
                    self.connection.send_packet(ack_packet)

                elif packet.message.command == A_CLSE:
                    # Connection closed
                    break

            logger.info(f"Remount service response: {response_message.strip()}")

            # Check if remount was successful
            success_indicators = [
                "remount succeeded",
                "remount of system succeeded",
                "/system remounted",
                "remount successful"
            ]

            failure_indicators = [
                "remount failed",
                "permission denied",
                "not permitted",
                "read-only file system"
            ]

            response_lower = response_message.lower()

            for indicator in success_indicators:
                if indicator in response_lower:
                    logger.info("System remount successful")
                    return True

            for indicator in failure_indicators:
                if indicator in response_lower:
                    logger.warning("System remount failed")
                    return False

            # If no clear indicator, assume success if we got a response
            if response_message.strip():
                logger.info("Remount command completed")
                return True
            else:
                logger.warning("No response from remount service")
                return False

        except Exception as e:
            logger.error(f"Error in service-based remount: {e}")
            return False

    def _remount_via_shell(self) -> bool:
        """Remount using shell command (newer devices)"""
        try:
            from .shell import AdbShellService

            shell = AdbShellService(self.connection, self.features)

            # Execute remount command via shell
            result = shell.execute_command('remount')

            if result.exit_code == 0:
                output = result.stdout.decode('utf-8', errors='ignore')
                logger.info(f"Shell remount output: {output.strip()}")

                # Check for success indicators in output
                if any(indicator in output.lower() for indicator in [
                    "remount succeeded", "remounted", "successful"
                ]):
                    logger.info("Shell-based remount successful")
                    return True
                else:
                    logger.info("Shell-based remount completed")
                    return True
            else:
                error_output = result.stderr.decode('utf-8', errors='ignore')
                logger.error(f"Shell remount failed: {error_output.strip()}")
                return False

        except Exception as e:
            logger.error(f"Error in shell-based remount: {e}")
            return False

    def check_mount_status(self) -> Optional[List[dict]]:
        """
        Check current mount status of system partitions.

        Returns:
            List of mount information dictionaries, or None if failed
        """
        try:
            from .shell import AdbShellService

            shell = AdbShellService(self.connection)
            result = shell.execute_command('mount')

            if result.exit_code != 0:
                logger.error("Failed to get mount information")
                return None

            mount_info = []
            lines = result.stdout.decode('utf-8', errors='ignore').strip().split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Parse mount line format: device on mountpoint type filesystem (options)
                # or: device mountpoint filesystem options
                parts = line.split()
                if len(parts) >= 3:
                    if 'on' in parts and 'type' in parts:
                        # Format: device on mountpoint type filesystem (options)
                        try:
                            on_idx = parts.index('on')
                            type_idx = parts.index('type')
                            device = ' '.join(parts[:on_idx])
                            mountpoint = ' '.join(parts[on_idx+1:type_idx])
                            filesystem = parts[type_idx+1] if type_idx+1 < len(parts) else 'unknown'
                            options = ' '.join(parts[type_idx+2:]) if type_idx+2 < len(parts) else ''
                        except (ValueError, IndexError):
                            continue
                    else:
                        # Format: device mountpoint filesystem options
                        device = parts[0]
                        mountpoint = parts[1]
                        filesystem = parts[2] if len(parts) > 2 else 'unknown'
                        options = ' '.join(parts[3:]) if len(parts) > 3 else ''

                    # Check if it's a system-related mount
                    if any(path in mountpoint for path in ['/system', '/vendor', '/product', '/odm']):
                        is_readonly = 'ro' in options or 'read-only' in options.lower()

                        mount_info.append({
                            'device': device,
                            'mountpoint': mountpoint,
                            'filesystem': filesystem,
                            'options': options,
                            'readonly': is_readonly
                        })

            return mount_info

        except Exception as e:
            logger.error(f"Error checking mount status: {e}")
            return None

    def is_system_writable(self) -> Optional[bool]:
        """
        Check if system partition is writable.

        Returns:
            True if writable, False if read-only, None if unable to determine
        """
        try:
            from .shell import AdbShellService

            shell = AdbShellService(self.connection)

            # Try to create a test file in /system
            test_file = "/system/.pyadb_write_test"
            result = shell.execute_command(f'touch {test_file}')

            if result.exit_code == 0:
                # Successfully created file, clean it up
                shell.execute_command(f'rm {test_file}')
                logger.debug("System partition is writable")
                return True
            else:
                logger.debug("System partition is read-only")
                return False

        except Exception as e:
            logger.error(f"Error checking system writability: {e}")
            return None