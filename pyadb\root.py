"""
ADB Root Service Implementation

This module implements the ADB root functionality for gaining root access
on Android devices. It handles the root protocol and device restart process.
"""

import time
import logging
from typing import Optional

from .const import A_OPEN, A_OKAY, A_WRTE, A_CLSE
from .core import create_open_packet, create_ready_packet, create_close_packet

logger = logging.getLogger(__name__)


class AdbRootService:
    """
    ADB root service for gaining root access on Android devices.

    Implements the ADB root protocol which restarts adbd with root permissions
    on debuggable builds. This requires the device to support root access.
    """

    def __init__(self, connection):
        """
        Initialize root service.

        Args:
            connection: ADB connection object (TCP or USB)
        """
        self.connection = connection
        logger.debug("Root service initialized")

    def enable_root(self) -> bool:
        """
        Enable root access on the device.

        This sends the 'root:' command to adbd, which will restart adbd
        with root permissions if the device supports it.

        Returns:
            True if root command was sent successfully, False otherwise
        """
        try:
            logger.info("Requesting root access...")

            # Open root service connection
            local_id = 10  # Use unique local ID
            open_packet = create_open_packet(local_id, "root:")

            if not self.connection.send_packet(open_packet):
                logger.error("Failed to send root open packet")
                return False

            # Wait for OKAY response
            response = self.connection.receive_packet()
            if not response or response.message.command != A_OKAY:
                logger.error(f"Unexpected response to root open: {response}")
                return False

            remote_id = response.message.arg0

            # Read response message
            response_message = ""
            while True:
                packet = self.connection.receive_packet()
                if not packet:
                    break

                if packet.message.command == A_WRTE:
                    # Received data from root service
                    data = packet.payload.decode('utf-8', errors='ignore')
                    response_message += data

                    # Send acknowledgment
                    ack_packet = create_ready_packet(local_id, remote_id)
                    self.connection.send_packet(ack_packet)

                elif packet.message.command == A_CLSE:
                    # Connection closed
                    break

            logger.info(f"Root service response: {response_message.strip()}")

            # Check if root was successful
            if "restarting adbd as root" in response_message.lower():
                logger.info("Root access granted - adbd will restart")
                return True
            elif "already running as root" in response_message.lower():
                logger.info("Already running as root")
                return True
            elif "cannot run as root" in response_message.lower():
                logger.warning("Root access denied - device does not support root")
                return False
            else:
                logger.warning(f"Unexpected root response: {response_message}")
                return False

        except Exception as e:
            logger.error(f"Error enabling root: {e}")
            return False

    def disable_root(self) -> bool:
        """
        Disable root access on the device (unroot).

        This sends the 'unroot:' command to adbd, which will restart adbd
        without root permissions.

        Returns:
            True if unroot command was sent successfully, False otherwise
        """
        try:
            logger.info("Requesting unroot...")

            # Open unroot service connection
            local_id = 11  # Use unique local ID
            open_packet = create_open_packet(local_id, "unroot:")

            if not self.connection.send_packet(open_packet):
                logger.error("Failed to send unroot open packet")
                return False

            # Wait for OKAY response
            response = self.connection.receive_packet()
            if not response or response.message.command != A_OKAY:
                logger.error(f"Unexpected response to unroot open: {response}")
                return False

            remote_id = response.message.arg0

            # Read response message
            response_message = ""
            while True:
                packet = self.connection.receive_packet()
                if not packet:
                    break

                if packet.message.command == A_WRTE:
                    data = packet.payload.decode('utf-8', errors='ignore')
                    response_message += data

                    ack_packet = create_ready_packet(local_id, remote_id)
                    self.connection.send_packet(ack_packet)

                elif packet.message.command == A_CLSE:
                    break

            logger.info(f"Unroot service response: {response_message.strip()}")

            # Check if unroot was successful
            if "restarting adbd as non root" in response_message.lower():
                logger.info("Root access disabled - adbd will restart")
                return True
            elif "not running as root" in response_message.lower():
                logger.info("Already running without root")
                return True
            else:
                logger.warning(f"Unexpected unroot response: {response_message}")
                return False

        except Exception as e:
            logger.error(f"Error disabling root: {e}")
            return False

    def check_root_status(self) -> Optional[bool]:
        """
        Check if the device is currently running with root access.

        This uses the shell service to check the current user ID.

        Returns:
            True if running as root, False if not, None if unable to determine
        """
        try:
            from .shell import AdbShellService

            shell = AdbShellService(self.connection)
            result = shell.execute_command('id -u')

            if result.exit_code == 0:
                uid = result.stdout.decode().strip()
                is_root = uid == '0'
                logger.debug(f"Current UID: {uid}, is_root: {is_root}")
                return is_root
            else:
                logger.error("Failed to check root status")
                return None

        except Exception as e:
            logger.error(f"Error checking root status: {e}")
            return None

    def wait_for_device_restart(self, timeout: float = 30.0) -> bool:
        """
        Wait for device to restart after root/unroot command.

        Args:
            timeout: Maximum time to wait in seconds

        Returns:
            True if device came back online, False if timeout
        """
        logger.info(f"Waiting for device restart (timeout: {timeout}s)...")

        start_time = time.time()

        # First, wait for connection to drop
        connection_dropped = False
        while time.time() - start_time < timeout / 2:
            if not self.connection.is_connected():
                connection_dropped = True
                logger.debug("Connection dropped - device restarting")
                break
            time.sleep(0.5)

        if not connection_dropped:
            logger.warning("Connection did not drop - device may not have restarted")

        # Then wait for connection to come back
        while time.time() - start_time < timeout:
            try:
                if self.connection.connect():
                    logger.info("Device reconnected after restart")
                    return True
            except Exception:
                pass
            time.sleep(1.0)

        logger.error("Timeout waiting for device restart")
        return False