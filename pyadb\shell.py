"""
ADB Shell Service Implementation

This module implements the ADB shell service for executing commands
on Android devices. It handles shell protocol, command execution,
and data streaming for interactive and non-interactive shells.
"""

import time
import threading
from typing import Optional, Callable, Union, List, Tuple, Any
from dataclasses import dataclass
from enum import IntEnum
import logging

from .const import (
    A_OPEN, A_OKAY, A_WRTE, A_CLSE,
    FEATURE_SHELL2, FEATURE_CMD
)
from .core import (
    AdbPacket, create_open_packet, create_write_packet,
    create_close_packet, create_ready_packet
)

logger = logging.getLogger(__name__)


class ShellProtocolId(IntEnum):
    """Shell protocol packet identifiers"""
    STDIN = 0
    STDOUT = 1
    STDERR = 2
    EXIT = 3


@dataclass
class ShellResult:
    """Result of a shell command execution"""
    stdout: bytes
    stderr: bytes
    exit_code: int
    execution_time: float


class ShellProtocolPacket:
    """
    Shell protocol packet for shell_v2 feature.

    The shell protocol wraps data with a header indicating the stream type
    (stdin, stdout, stderr) and data length.
    """

    def __init__(self, stream_id: ShellProtocolId, data: bytes):
        self.stream_id = stream_id
        self.data = data

    def to_bytes(self) -> bytes:
        """Convert to wire format: [id:1][length:4][data:length]"""
        import struct
        header = struct.pack('<BI', self.stream_id, len(self.data))
        return header + self.data

    @classmethod
    def from_bytes(cls, data: bytes) -> 'ShellProtocolPacket':
        """Parse from wire format"""
        import struct
        if len(data) < 5:
            raise ValueError("Insufficient data for shell protocol packet")

        stream_id, length = struct.unpack('<BI', data[:5])
        payload = data[5:5+length]

        if len(payload) != length:
            raise ValueError(f"Incomplete shell protocol packet: expected {length}, got {len(payload)}")

        return cls(ShellProtocolId(stream_id), payload)

    def __str__(self) -> str:
        return f"ShellProtocolPacket({self.stream_id.name}, {len(self.data)} bytes)"


class AdbShellService:
    """
    ADB Shell service for command execution.

    Provides both interactive and non-interactive shell capabilities
    with support for shell_v2 protocol when available.
    """

    def __init__(self, connection, features: List[str] = None):
        """
        Initialize shell service.

        Args:
            connection: ADB connection object (TCP or USB)
            features: List of supported features
        """
        self.connection = connection
        self.features = features or []
        self.supports_shell2 = FEATURE_SHELL2 in self.features
        self.supports_cmd = FEATURE_CMD in self.features
        self._next_local_id = 1
        self._active_shells = {}
        self._lock = threading.RLock()

        logger.debug(f"Shell service initialized (shell_v2: {self.supports_shell2}, cmd: {self.supports_cmd})")

    def execute_command(self, command: str, timeout: float = 30.0) -> ShellResult:
        """
        Execute a single command and return the result.

        Args:
            command: Command to execute
            timeout: Execution timeout in seconds

        Returns:
            ShellResult with stdout, stderr, exit code, and execution time
        """
        start_time = time.time()

        try:
            if self.supports_shell2:
                return self._execute_shell2_command(command, timeout)
            else:
                return self._execute_legacy_command(command, timeout)
        finally:
            execution_time = time.time() - start_time
            logger.debug(f"Command execution took {execution_time:.2f}s")

    def _execute_shell2_command(self, command: str, timeout: float) -> ShellResult:
        """Execute command using shell_v2 protocol"""
        local_id = self._get_next_local_id()

        # Open shell connection
        service_name = f"shell,v2,raw:{command}"
        open_packet = create_open_packet(local_id, service_name)

        if not self.connection.send_packet(open_packet):
            raise RuntimeError("Failed to send shell open packet")

        # Wait for OKAY response
        response = self.connection.receive_packet()
        if not response or response.message.command != A_OKAY:
            raise RuntimeError(f"Unexpected response to shell open: {response}")

        remote_id = response.message.arg0

        stdout_data = b''
        stderr_data = b''
        exit_code = 0

        try:
            # Read shell protocol packets
            while True:
                packet = self.connection.receive_packet()
                if not packet:
                    break

                if packet.message.command == A_WRTE:
                    # Parse shell protocol data
                    try:
                        shell_packet = ShellProtocolPacket.from_bytes(packet.payload)

                        if shell_packet.stream_id == ShellProtocolId.STDOUT:
                            stdout_data += shell_packet.data
                        elif shell_packet.stream_id == ShellProtocolId.STDERR:
                            stderr_data += shell_packet.data
                        elif shell_packet.stream_id == ShellProtocolId.EXIT:
                            if len(shell_packet.data) >= 1:
                                exit_code = shell_packet.data[0]
                            break

                        # Send OKAY to acknowledge
                        okay_packet = create_ready_packet(local_id, remote_id)
                        self.connection.send_packet(okay_packet)

                    except ValueError as e:
                        logger.error(f"Invalid shell protocol packet: {e}")
                        break

                elif packet.message.command == A_CLSE:
                    break

        finally:
            # Close the connection
            close_packet = create_close_packet(local_id, remote_id)
            self.connection.send_packet(close_packet)

        execution_time = time.time() - time.time()  # Will be updated by caller
        return ShellResult(stdout_data, stderr_data, exit_code, execution_time)

    def _execute_legacy_command(self, command: str, timeout: float) -> ShellResult:
        """Execute command using legacy shell protocol"""
        local_id = self._get_next_local_id()

        # Open shell connection
        service_name = f"shell:{command}"
        open_packet = create_open_packet(local_id, service_name)

        if not self.connection.send_packet(open_packet):
            raise RuntimeError("Failed to send shell open packet")

        # Wait for OKAY response
        response = self.connection.receive_packet()
        if not response or response.message.command != A_OKAY:
            raise RuntimeError(f"Unexpected response to shell open: {response}")

        remote_id = response.message.arg0
        output_data = b''

        try:
            # Read output data
            while True:
                packet = self.connection.receive_packet()
                if not packet:
                    break

                if packet.message.command == A_WRTE:
                    output_data += packet.payload

                    # Send OKAY to acknowledge
                    okay_packet = create_ready_packet(local_id, remote_id)
                    self.connection.send_packet(okay_packet)

                elif packet.message.command == A_CLSE:
                    break

        finally:
            # Close the connection
            close_packet = create_close_packet(local_id, remote_id)
            self.connection.send_packet(close_packet)

        # Legacy shell doesn't separate stdout/stderr or provide exit codes
        execution_time = time.time() - time.time()  # Will be updated by caller
        return ShellResult(output_data, b'', 0, execution_time)

    def _get_next_local_id(self) -> int:
        """Get next available local ID for connections"""
        with self._lock:
            local_id = self._next_local_id
            self._next_local_id += 1
            return local_id

    def execute_command_streaming(self, command: str,
                                stdout_callback: Optional[Callable[[bytes], None]] = None,
                                stderr_callback: Optional[Callable[[bytes], None]] = None,
                                timeout: float = 30.0) -> int:
        """
        Execute command with streaming output callbacks.

        Args:
            command: Command to execute
            stdout_callback: Callback for stdout data
            stderr_callback: Callback for stderr data
            timeout: Execution timeout in seconds

        Returns:
            Exit code of the command
        """
        if not self.supports_shell2:
            # For legacy shell, we can't separate stdout/stderr
            result = self._execute_legacy_command(command, timeout)
            if stdout_callback:
                stdout_callback(result.stdout)
            return result.exit_code

        local_id = self._get_next_local_id()

        # Open shell connection
        service_name = f"shell,v2,raw:{command}"
        open_packet = create_open_packet(local_id, service_name)

        if not self.connection.send_packet(open_packet):
            raise RuntimeError("Failed to send shell open packet")

        # Wait for OKAY response
        response = self.connection.receive_packet()
        if not response or response.message.command != A_OKAY:
            raise RuntimeError(f"Unexpected response to shell open: {response}")

        remote_id = response.message.arg0
        exit_code = 0

        try:
            # Read shell protocol packets with streaming
            while True:
                packet = self.connection.receive_packet()
                if not packet:
                    break

                if packet.message.command == A_WRTE:
                    try:
                        shell_packet = ShellProtocolPacket.from_bytes(packet.payload)

                        if shell_packet.stream_id == ShellProtocolId.STDOUT:
                            if stdout_callback:
                                stdout_callback(shell_packet.data)
                        elif shell_packet.stream_id == ShellProtocolId.STDERR:
                            if stderr_callback:
                                stderr_callback(shell_packet.data)
                        elif shell_packet.stream_id == ShellProtocolId.EXIT:
                            if len(shell_packet.data) >= 1:
                                exit_code = shell_packet.data[0]
                            break

                        # Send OKAY to acknowledge
                        okay_packet = create_ready_packet(local_id, remote_id)
                        self.connection.send_packet(okay_packet)

                    except ValueError as e:
                        logger.error(f"Invalid shell protocol packet: {e}")
                        break

                elif packet.message.command == A_CLSE:
                    break

        finally:
            # Close the connection
            close_packet = create_close_packet(local_id, remote_id)
            self.connection.send_packet(close_packet)

        return exit_code

    def get_properties(self) -> dict:
        """Get Android system properties"""
        result = self.execute_command("getprop")
        properties = {}

        if result.exit_code == 0:
            lines = result.stdout.decode('utf-8', errors='ignore').strip().split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('[') and ']: [' in line:
                    # Parse format: [key]: [value]
                    try:
                        key_end = line.index(']: [')
                        key = line[1:key_end]
                        value = line[key_end + 4:-1]  # Remove ']: [' and final ']'
                        properties[key] = value
                    except (ValueError, IndexError):
                        continue

        return properties

    def get_device_info(self) -> dict:
        """Get basic device information"""
        info = {}

        # Get key properties
        properties = self.get_properties()

        info['model'] = properties.get('ro.product.model', 'Unknown')
        info['brand'] = properties.get('ro.product.brand', 'Unknown')
        info['device'] = properties.get('ro.product.device', 'Unknown')
        info['android_version'] = properties.get('ro.build.version.release', 'Unknown')
        info['api_level'] = properties.get('ro.build.version.sdk', 'Unknown')
        info['build_id'] = properties.get('ro.build.id', 'Unknown')
        info['serial'] = properties.get('ro.serialno', 'Unknown')

        return info

    def list_packages(self, system_apps: bool = False) -> List[str]:
        """
        List installed packages.

        Args:
            system_apps: Include system applications

        Returns:
            List of package names
        """
        cmd = "pm list packages"
        if not system_apps:
            cmd += " -3"  # Third-party apps only

        result = self.execute_command(cmd)
        packages = []

        if result.exit_code == 0:
            lines = result.stdout.decode('utf-8', errors='ignore').strip().split('\n')
            for line in lines:
                if line.startswith('package:'):
                    package_name = line[8:].strip()  # Remove 'package:' prefix
                    packages.append(package_name)

        return packages

    def is_root_available(self) -> bool:
        """Check if root access is available"""
        result = self.execute_command("su -c 'id'", timeout=5.0)
        return result.exit_code == 0 and b'uid=0' in result.stdout