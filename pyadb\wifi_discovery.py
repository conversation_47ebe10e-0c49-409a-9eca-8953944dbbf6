"""
ADB WiFi Discovery and Connection API

This module provides high-level APIs for discovering, pairing, and connecting
to ADB devices over WiFi networks.
"""

import time
from typing import List, Optional, Dict, Tuple
from dataclasses import dataclass
import logging

from .mdns_discovery import AdbService, discover_adb_services, find_pairing_services, get_mdns_client
from .pairing import pair_device, discover_and_pair, PairingResult
from .conn_wifi import AdbTcpConnection
from .device_registry import get_device_registry, DeviceInfo, register_device
from .auth import get_key_manager

logger = logging.getLogger(__name__)


@dataclass
class DiscoveredDevice:
    """Information about a discovered ADB device"""
    name: str                    # Device name/identifier
    ip_address: str             # Device IP address
    port: int                   # ADB port
    is_paired: bool             # Whether device is already paired
    is_secure: bool             # Whether device supports secure connection
    service_type: str           # mDNS service type
    can_pair: bool              # Whether device is available for pairing
    device_guid: Optional[str] = None  # Device GUID if known


class AdbWifiManager:
    """High-level manager for ADB WiFi operations"""
    
    def __init__(self):
        self.registry = get_device_registry()
        self.mdns_client = get_mdns_client()
        self._discovery_active = False
    
    def start_discovery(self) -> bool:
        """
        Start continuous device discovery
        
        Returns:
            True if discovery started successfully
        """
        if self._discovery_active:
            return True
        
        success = self.mdns_client.start_discovery()
        if success:
            self._discovery_active = True
            logger.info("WiFi device discovery started")
        else:
            logger.error("Failed to start WiFi device discovery")
        
        return success
    
    def stop_discovery(self) -> None:
        """Stop device discovery"""
        if self._discovery_active:
            self.mdns_client.stop_discovery()
            self._discovery_active = False
            logger.info("WiFi device discovery stopped")
    
    def discover_devices(self, timeout: float = 10.0, include_paired: bool = True) -> List[DiscoveredDevice]:
        """
        Discover ADB devices on the network
        
        Args:
            timeout: Discovery timeout in seconds
            include_paired: Whether to include already paired devices
            
        Returns:
            List of discovered devices
        """
        logger.info(f"Discovering ADB devices (timeout: {timeout}s)")
        
        # Start discovery if not already running
        was_running = self._discovery_active
        if not was_running:
            if not self.start_discovery():
                return []
        
        # Wait for discovery
        time.sleep(timeout)
        
        # Get discovered services
        services = self.mdns_client.get_discovered_services()
        
        # Stop discovery if we started it
        if not was_running:
            self.stop_discovery()
        
        # Convert services to discovered devices
        devices = []
        for service in services:
            # Check if device is already paired
            device_info = self.registry.get_device_by_address(service.ip_address, service.port)
            is_paired = device_info is not None
            
            if not include_paired and is_paired:
                continue
            
            device = DiscoveredDevice(
                name=service.instance_name,
                ip_address=service.ip_address,
                port=service.port,
                is_paired=is_paired,
                is_secure=service.is_secure,
                service_type=service.service_type,
                can_pair=service.is_pairing_service,
                device_guid=device_info.guid if device_info else None
            )
            devices.append(device)
        
        logger.info(f"Discovered {len(devices)} ADB devices")
        return devices
    
    def find_pairing_devices(self, timeout: float = 5.0) -> List[DiscoveredDevice]:
        """
        Find devices available for pairing
        
        Args:
            timeout: Discovery timeout in seconds
            
        Returns:
            List of devices available for pairing
        """
        logger.info("Looking for devices available for pairing")
        
        services = find_pairing_services(timeout)
        
        devices = []
        for service in services:
            device = DiscoveredDevice(
                name=service.instance_name,
                ip_address=service.ip_address,
                port=service.port,
                is_paired=False,
                is_secure=service.is_secure,
                service_type=service.service_type,
                can_pair=True
            )
            devices.append(device)
        
        logger.info(f"Found {len(devices)} devices available for pairing")
        return devices
    
    def pair_with_device(self, ip_address: str, password: str, port: int = 37000, 
                        device_name: Optional[str] = None) -> PairingResult:
        """
        Pair with a specific device
        
        Args:
            ip_address: Device IP address
            password: Pairing password (6-digit code)
            port: Pairing port (default: 37000)
            device_name: Device name (optional, for identification)
            
        Returns:
            PairingResult with success status and device GUID
        """
        logger.info(f"Pairing with device at {ip_address}:{port}")
        
        result = pair_device(ip_address, password, port)
        
        if result.success:
            logger.info(f"Successfully paired with device {result.device_guid}")
            
            # Register the device if not already registered
            if not self.registry.is_device_known(result.device_guid):
                register_device(
                    guid=result.device_guid,
                    name=device_name or f"Device-{ip_address}",
                    ip_address=ip_address,
                    port=5555,  # Default ADB port
                    auth_key=""  # Will be populated during connection
                )
        else:
            logger.error(f"Failed to pair with device: {result.error_message}")
        
        return result
    
    def auto_pair_device(self, password: str, device_name: Optional[str] = None) -> PairingResult:
        """
        Automatically discover and pair with a device
        
        Args:
            password: Pairing password
            device_name: Specific device name to pair with (optional)
            
        Returns:
            PairingResult with success status and device GUID
        """
        logger.info("Auto-pairing with discovered device")
        return discover_and_pair(password, device_name)
    
    def connect_to_device(self, device_guid: str, use_tls: bool = True) -> Optional[AdbTcpConnection]:
        """
        Connect to a paired device
        
        Args:
            device_guid: Device GUID
            use_tls: Whether to use TLS encryption
            
        Returns:
            AdbTcpConnection if successful, None otherwise
        """
        device_info = self.registry.get_device(device_guid)
        if not device_info:
            logger.error(f"Device {device_guid} not found in registry")
            return None
        
        logger.info(f"Connecting to device {device_guid} at {device_info.ip_address}:{device_info.port}")
        
        connection = AdbTcpConnection(
            host=device_info.ip_address,
            port=device_info.port,
            use_tls=use_tls,
            device_guid=device_guid
        )
        
        if connection.connect():
            # Update device connection info
            self.registry.update_device_connection(
                device_guid, 
                device_info.ip_address, 
                device_info.port
            )
            logger.info(f"Successfully connected to device {device_guid}")
            return connection
        else:
            logger.error(f"Failed to connect to device {device_guid}")
            return None
    
    def connect_to_address(self, ip_address: str, port: int = 5555, 
                          use_tls: bool = False) -> Optional[AdbTcpConnection]:
        """
        Connect to a device by IP address
        
        Args:
            ip_address: Device IP address
            port: Device port (default: 5555)
            use_tls: Whether to use TLS encryption
            
        Returns:
            AdbTcpConnection if successful, None otherwise
        """
        logger.info(f"Connecting to device at {ip_address}:{port}")
        
        # Check if device is known
        device_info = self.registry.get_device_by_address(ip_address, port)
        device_guid = device_info.guid if device_info else None
        
        connection = AdbTcpConnection(
            host=ip_address,
            port=port,
            use_tls=use_tls,
            device_guid=device_guid
        )
        
        if connection.connect():
            logger.info(f"Successfully connected to {ip_address}:{port}")
            return connection
        else:
            logger.error(f"Failed to connect to {ip_address}:{port}")
            return None
    
    def list_paired_devices(self) -> List[DeviceInfo]:
        """
        List all paired devices
        
        Returns:
            List of paired device information
        """
        return self.registry.list_devices()
    
    def remove_device(self, device_guid: str) -> bool:
        """
        Remove a device from the registry
        
        Args:
            device_guid: Device GUID to remove
            
        Returns:
            True if device was removed
        """
        return self.registry.remove_device(device_guid)
    
    def get_device_info(self, device_guid: str) -> Optional[DeviceInfo]:
        """
        Get information about a paired device
        
        Args:
            device_guid: Device GUID
            
        Returns:
            Device information or None if not found
        """
        return self.registry.get_device(device_guid)


# Global WiFi manager instance
_wifi_manager: Optional[AdbWifiManager] = None


def get_wifi_manager() -> AdbWifiManager:
    """
    Get the global WiFi manager instance
    
    Returns:
        AdbWifiManager instance
    """
    global _wifi_manager
    if _wifi_manager is None:
        _wifi_manager = AdbWifiManager()
    return _wifi_manager


# Convenience functions
def discover_wifi_devices(timeout: float = 10.0) -> List[DiscoveredDevice]:
    """Discover ADB devices on WiFi network"""
    manager = get_wifi_manager()
    return manager.discover_devices(timeout)


def pair_wifi_device(ip_address: str, password: str) -> PairingResult:
    """Pair with a WiFi ADB device"""
    manager = get_wifi_manager()
    return manager.pair_with_device(ip_address, password)


def connect_wifi_device(device_guid: str) -> Optional[AdbTcpConnection]:
    """Connect to a paired WiFi device"""
    manager = get_wifi_manager()
    return manager.connect_to_device(device_guid)


def connect_wifi_address(ip_address: str, port: int = 5555) -> Optional[AdbTcpConnection]:
    """Connect to a device by IP address"""
    manager = get_wifi_manager()
    return manager.connect_to_address(ip_address, port)
