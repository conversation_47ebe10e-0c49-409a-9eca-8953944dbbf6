[tool:pytest]
# PyADB Test Configuration

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# Markers
markers =
    integration: Integration tests that test multiple components together
    network: Tests that require network access
    slow: Tests that take a long time to run
    real_device: Tests that require real ADB devices (USB or WiFi)

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# addopts = -n auto  # Uncomment to enable parallel testing with pytest-xdist

# Coverage options (if pytest-cov is installed)
# addopts = --cov=pyadb --cov-report=html --cov-report=term-missing

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filterwarnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:cryptography.*
