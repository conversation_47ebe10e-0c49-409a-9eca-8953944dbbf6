#!/usr/bin/env python3
"""
PyADB Test Runner

This script provides a convenient way to run PyADB tests with different
configurations and options.

Usage:
    python run_tests.py [options]

Examples:
    python run_tests.py                    # Run all tests
    python run_tests.py --unit             # Run only unit tests
    python run_tests.py --integration      # Run only integration tests
    python run_tests.py --network          # Run network tests
    python run_tests.py --real-device      # Run real device tests
    python run_tests.py --coverage         # Run with coverage report
    python run_tests.py --verbose          # Verbose output
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and return the result"""
    if description:
        print(f"\n🔄 {description}")
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=False)
    
    if result.returncode == 0:
        print(f"✅ {description or 'Command'} completed successfully")
    else:
        print(f"❌ {description or 'Command'} failed with exit code {result.returncode}")
    
    return result.returncode


def main():
    parser = argparse.ArgumentParser(
        description="PyADB Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # Test selection options
    parser.add_argument(
        "--unit", action="store_true",
        help="Run only unit tests (exclude integration, network, slow tests)"
    )
    parser.add_argument(
        "--integration", action="store_true",
        help="Run only integration tests"
    )
    parser.add_argument(
        "--network", action="store_true",
        help="Run network tests (requires network access)"
    )
    parser.add_argument(
        "--real-device", action="store_true",
        help="Run real device tests (requires actual ADB devices)"
    )
    parser.add_argument(
        "--slow", action="store_true",
        help="Include slow tests"
    )
    
    # Output options
    parser.add_argument(
        "--coverage", action="store_true",
        help="Run with coverage report"
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--quiet", "-q", action="store_true",
        help="Quiet output"
    )
    parser.add_argument(
        "--no-color", action="store_true",
        help="Disable colored output"
    )
    
    # Test execution options
    parser.add_argument(
        "--parallel", "-n", type=int, metavar="N",
        help="Run tests in parallel with N workers (requires pytest-xdist)"
    )
    parser.add_argument(
        "--timeout", type=int, default=300, metavar="SECONDS",
        help="Test timeout in seconds (default: 300)"
    )
    parser.add_argument(
        "--maxfail", type=int, metavar="N",
        help="Stop after N test failures"
    )
    
    # Specific test selection
    parser.add_argument(
        "--test", "-t", metavar="TEST_PATTERN",
        help="Run specific test(s) matching pattern"
    )
    parser.add_argument(
        "--module", "-m", metavar="MODULE",
        help="Run tests from specific module"
    )
    
    # Other options
    parser.add_argument(
        "--install-deps", action="store_true",
        help="Install test dependencies before running tests"
    )
    parser.add_argument(
        "--dry-run", action="store_true",
        help="Show what would be run without executing"
    )
    
    args = parser.parse_args()
    
    # Check if pytest is available
    try:
        import pytest
    except ImportError:
        print("❌ pytest is not installed. Install it with: pip install pytest")
        return 1
    
    # Install dependencies if requested
    if args.install_deps:
        deps = [
            "pytest>=6.0",
            "pytest-cov",
            "pytest-xdist",
            "pytest-timeout",
            "cryptography"
        ]
        
        for dep in deps:
            result = run_command([sys.executable, "-m", "pip", "install", dep], 
                               f"Installing {dep}")
            if result != 0:
                print(f"⚠️  Failed to install {dep}, continuing anyway...")
    
    # Build pytest command
    cmd = [sys.executable, "-m", "pytest"]
    
    # Test selection
    if args.unit:
        cmd.extend(["-m", "not integration and not network and not slow and not real_device"])
    elif args.integration:
        cmd.extend(["-m", "integration"])
    elif args.network:
        cmd.extend(["-m", "network"])
    elif args.real_device:
        cmd.extend(["-m", "real_device"])
    elif not args.slow:
        # By default, exclude slow tests unless explicitly requested
        cmd.extend(["-m", "not slow and not real_device"])
    
    # Coverage
    if args.coverage:
        cmd.extend([
            "--cov=pyadb",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-report=xml"
        ])
    
    # Output options
    if args.verbose:
        cmd.append("-vv")
    elif args.quiet:
        cmd.append("-q")
    
    if args.no_color:
        cmd.append("--color=no")
    
    # Execution options
    if args.parallel:
        cmd.extend(["-n", str(args.parallel)])
    
    if args.timeout:
        cmd.extend(["--timeout", str(args.timeout)])
    
    if args.maxfail:
        cmd.extend(["--maxfail", str(args.maxfail)])
    
    # Specific test selection
    if args.test:
        cmd.extend(["-k", args.test])
    
    if args.module:
        cmd.append(f"tests/test_{args.module}.py")
    else:
        cmd.append("tests/")
    
    # Show command if dry run
    if args.dry_run:
        print("Would run:")
        print(" ".join(cmd))
        return 0
    
    # Run tests
    print("🧪 Running PyADB Tests")
    print("=" * 50)
    
    exit_code = run_command(cmd, "Running tests")
    
    if exit_code == 0:
        print("\n🎉 All tests passed!")
        
        if args.coverage:
            print("\n📊 Coverage report generated:")
            print("  - HTML: htmlcov/index.html")
            print("  - XML: coverage.xml")
    else:
        print(f"\n💥 Tests failed with exit code {exit_code}")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
