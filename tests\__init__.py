"""
PyADB Test Suite

Comprehensive test suite for PyADB - Pure Python ADB Implementation.

Test Structure:
- unit/: Unit tests for individual components
- integration/: Integration tests for component interactions  
- real_device/emulator/: Integration tests using real emulator device
- fixtures/: Test fixtures and utilities
- conftest.py: Pytest configuration and shared fixtures
"""

import logging
import sys
from pathlib import Path

# Add pyadb to path for testing
test_dir = Path(__file__).parent
project_root = test_dir.parent
sys.path.insert(0, str(project_root))

# Configure test logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Test constants
EMULATOR_HOST = 'localhost'
EMULATOR_PORT = 5554
EMULATOR_DEVICE = 'emulator-5554'

__version__ = '1.0.0'
