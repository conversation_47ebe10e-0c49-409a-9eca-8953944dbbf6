"""
PyTest Configuration and Shared Fixtures

This module provides shared fixtures and configuration for all PyADB tests.
"""

import pytest
import logging
import socket
import time
from typing import Generator, Optional
from pathlib import Path

# Import PyADB components
from pyadb import (
    AdbTcpConnection, AdbShellService, AdbPullService, AdbPushService,
    AdbRootService, AdbRemountService, AdbPortForwardService
)
from pyadb.core import AdbPacket, AdbMessage
from pyadb.const import A_CNXN, A_OKAY, A_AUTH

# Test constants
EMULATOR_HOST = 'localhost'
EMULATOR_PORT = 5554
EMULATOR_DEVICE = 'emulator-5554'
TEST_TIMEOUT = 30.0

logger = logging.getLogger(__name__)


def pytest_configure(config):
    """Configure pytest with custom markers and settings."""
    config.addinivalue_line(
        "markers", "real_device: mark test as requiring real device connection"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "network: mark test as requiring network access"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def is_emulator_available() -> bool:
    """Check if emulator is available and responding."""
    try:
        with socket.create_connection((EMULATOR_HOST, EMULATOR_PORT), timeout=5):
            return True
    except (socket.error, OSError):
        return False


@pytest.fixture(scope="session")
def emulator_available():
    """Session-scoped fixture to check emulator availability."""
    available = is_emulator_available()
    if not available:
        pytest.skip(f"Emulator not available at {EMULATOR_HOST}:{EMULATOR_PORT}")
    return available


@pytest.fixture
def adb_connection(emulator_available) -> Generator[AdbTcpConnection, None, None]:
    """Provide a connected ADB TCP connection to emulator."""
    logger.info(f"Connecting to emulator at {EMULATOR_HOST}:{EMULATOR_PORT}")
    
    connection = AdbTcpConnection(
        host=EMULATOR_HOST,
        port=EMULATOR_PORT,
        timeout=TEST_TIMEOUT
    )
    
    try:
        connection.connect()
        logger.info("Successfully connected to emulator")
        yield connection
    finally:
        try:
            connection.disconnect()
            logger.info("Disconnected from emulator")
        except Exception as e:
            logger.warning(f"Error during disconnect: {e}")


@pytest.fixture
def shell_service(adb_connection) -> AdbShellService:
    """Provide an ADB shell service connected to emulator."""
    return AdbShellService(adb_connection)


@pytest.fixture
def pull_service(adb_connection) -> AdbPullService:
    """Provide an ADB pull service connected to emulator."""
    return AdbPullService(adb_connection)


@pytest.fixture
def push_service(adb_connection) -> AdbPushService:
    """Provide an ADB push service connected to emulator."""
    return AdbPushService(adb_connection)


@pytest.fixture
def root_service(adb_connection) -> AdbRootService:
    """Provide an ADB root service connected to emulator."""
    return AdbRootService(adb_connection)


@pytest.fixture
def remount_service(adb_connection) -> AdbRemountService:
    """Provide an ADB remount service connected to emulator."""
    return AdbRemountService(adb_connection)


@pytest.fixture
def port_forward_service(adb_connection) -> AdbPortForwardService:
    """Provide an ADB port forward service connected to emulator."""
    return AdbPortForwardService(adb_connection)


@pytest.fixture
def temp_test_dir(tmp_path) -> Path:
    """Provide a temporary directory for test files."""
    test_dir = tmp_path / "pyadb_test"
    test_dir.mkdir(exist_ok=True)
    return test_dir


@pytest.fixture
def sample_test_file(temp_test_dir) -> Path:
    """Create a sample test file for file operations."""
    test_file = temp_test_dir / "test_file.txt"
    test_content = "This is a test file for PyADB testing.\nLine 2\nLine 3\n"
    test_file.write_text(test_content)
    return test_file


@pytest.fixture
def packet_logger():
    """Fixture to enable detailed packet-level logging."""
    # Set up packet-level logging
    packet_logger = logging.getLogger('pyadb.core')
    packet_logger.setLevel(logging.DEBUG)
    
    # Create detailed formatter for packet logging
    formatter = logging.Formatter(
        '%(asctime)s [PACKET] %(name)s: %(message)s',
        datefmt='%H:%M:%S.%f'
    )
    
    # Add handler if not already present
    if not packet_logger.handlers:
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        packet_logger.addHandler(handler)
    
    return packet_logger


class PacketCapture:
    """Helper class to capture and analyze ADB packets during tests."""
    
    def __init__(self):
        self.sent_packets = []
        self.received_packets = []
        self.enabled = False
    
    def enable(self):
        """Enable packet capture."""
        self.enabled = True
        self.sent_packets.clear()
        self.received_packets.clear()
    
    def disable(self):
        """Disable packet capture."""
        self.enabled = False
    
    def capture_sent(self, packet: AdbPacket):
        """Capture a sent packet."""
        if self.enabled:
            self.sent_packets.append(packet)
    
    def capture_received(self, packet: AdbPacket):
        """Capture a received packet."""
        if self.enabled:
            self.received_packets.append(packet)
    
    def get_packets_by_command(self, command: int, sent: bool = True):
        """Get packets by command type."""
        packets = self.sent_packets if sent else self.received_packets
        return [p for p in packets if p.message.command == command]


@pytest.fixture
def packet_capture():
    """Provide packet capture functionality for detailed protocol analysis."""
    return PacketCapture()


def log_packet_details(packet: AdbPacket, direction: str = ""):
    """Log detailed packet information for debugging."""
    msg = packet.message
    logger.debug(f"{direction} Packet Details:")
    logger.debug(f"  Command: 0x{msg.command:08x} ({msg.command})")
    logger.debug(f"  Arg0: 0x{msg.arg0:08x} ({msg.arg0})")
    logger.debug(f"  Arg1: 0x{msg.arg1:08x} ({msg.arg1})")
    logger.debug(f"  Data Length: {msg.data_length}")
    logger.debug(f"  Data Check: 0x{msg.data_check:08x}")
    logger.debug(f"  Magic: 0x{msg.magic:08x}")
    if packet.payload:
        logger.debug(f"  Payload: {packet.payload[:50]}{'...' if len(packet.payload) > 50 else ''}")


@pytest.fixture
def debug_logger():
    """Provide enhanced debug logging for tests."""
    def _log_packet(packet: AdbPacket, direction: str = ""):
        log_packet_details(packet, direction)
    
    return _log_packet
