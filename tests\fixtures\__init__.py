"""
Test Fixtures and Utilities

This package contains test fixtures, mock objects, and utility functions
used across the PyADB test suite.

Modules:
- mock_devices.py: Mock ADB device implementations
- packet_fixtures.py: Pre-built ADB packet fixtures
- test_data.py: Test data and constants
- helpers.py: Test helper functions
"""

import logging

# Configure fixture logging
logging.getLogger('pyadb.fixtures').setLevel(logging.DEBUG)
