"""
Integration Tests for PyADB

This package contains integration tests that test multiple PyADB components
working together, but may use mocked external dependencies.

Test Modules:
- test_connection_flow.py: Complete connection establishment flow
- test_shell_integration.py: Shell service with connection integration
- test_file_operations.py: File pull/push integration tests
- test_service_interactions.py: Multiple service interaction tests
"""

import logging

# Configure integration test logging
logging.getLogger('pyadb').setLevel(logging.DEBUG)
