"""
Real Device Tests for PyADB

This package contains tests that require actual ADB devices (emulator or physical).
These tests verify PyADB works correctly with real ADB implementations.

Test Structure:
- emulator/: Tests using Android emulator (emulator-5554)
- physical/: Tests using physical devices (if available)
"""

import logging

# Configure real device test logging
logging.getLogger('pyadb').setLevel(logging.DEBUG)
