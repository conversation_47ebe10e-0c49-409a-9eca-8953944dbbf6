"""
Emulator Integration Tests

Tests that use the Android emulator (emulator-5554) to verify PyADB
functionality against a real ADB implementation.

All tests in this package require:
- Android emulator running and accessible at localhost:5554
- Emulator configured for ADB debugging

Test Modules:
- test_01_connection.py: Basic connection establishment
- test_02_shell.py: Shell command execution
- test_03_file_operations.py: File pull/push operations
- test_04_system_services.py: Root, remount, port forwarding
- test_05_protocol_compliance.py: Protocol compliance verification
"""

import logging

# Configure emulator test logging
logging.getLogger('pyadb').setLevel(logging.DEBUG)
