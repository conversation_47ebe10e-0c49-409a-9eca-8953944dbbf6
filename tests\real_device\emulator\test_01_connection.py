"""
Integration tests for ADB emulator connection

Tests basic connection establishment, authentication, and device communication
using the Android emulator at localhost:5554.
"""

import pytest
import time
import socket
import logging

from pyadb import AdbTcpConnection, AdbShellService
from pyadb.core import AdbPacket, AdbMessage, create_connect_packet
from pyadb.const import A_CNXN, A_OKAY, A_AUTH, A_VERSION, MAX_PAYLOAD

logger = logging.getLogger(__name__)


@pytest.mark.real_device
@pytest.mark.integration
class TestEmulatorConnection:
    """Test basic emulator connection functionality."""

    def test_emulator_availability(self, emulator_available, packet_logger):
        """Test that emulator is available and responding."""
        packet_logger.debug("Testing emulator availability")
        
        # This test passes if the emulator_available fixture doesn't skip
        assert emulator_available is True
        
        # Additional connectivity test
        try:
            with socket.create_connection(('localhost', 5555), timeout=5) as sock:
                # Just verify we can connect
                pass
        except (socket.error, OSError) as e:
            pytest.fail(f"Cannot connect to emulator: {e}")
        
        packet_logger.debug("Emulator is available and responding")

    def test_tcp_connection_establishment(self, packet_logger):
        """Test TCP connection establishment to emulator."""
        packet_logger.debug("Testing TCP connection establishment")
        
        connection = AdbTcpConnection('localhost', 5555, timeout=10.0)
        
        try:
            result = connection.connect()
            assert result is True, "Failed to establish TCP connection"
            assert connection.is_connected() is True
            
            # Verify connection details
            peer_addr = connection.get_peer_address()
            assert peer_addr is not None
            assert peer_addr[0] in ['127.0.0.1', '::1']  # localhost IPv4 or IPv6
            assert peer_addr[1] == 5555
            
            packet_logger.debug(f"Connected to emulator at {peer_addr}")
            
        finally:
            connection.disconnect()
            assert connection.is_connected() is False
        
        packet_logger.debug("TCP connection establishment successful")

    def test_adb_protocol_handshake(self, packet_logger):
        """Test ADB protocol handshake with emulator."""
        packet_logger.debug("Testing ADB protocol handshake")
        
        connection = AdbTcpConnection('localhost', 5555, timeout=10.0)
        
        try:
            # Establish TCP connection
            assert connection.connect() is True
            
            # Send CNXN packet
            connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, "host::")
            packet_logger.debug(f"Sending CNXN packet: {connect_packet.message}")
            
            send_result = connection.send_packet(connect_packet)
            assert send_result is True, "Failed to send CNXN packet"
            
            # Receive response
            response = connection.receive_packet()
            assert response is not None, "No response to CNXN packet"
            
            packet_logger.debug(f"Received response: {response.message}")
            
            # Response should be CNXN or AUTH
            assert response.message.command in [A_CNXN, A_AUTH], f"Unexpected response command: {response.message.command:08x}"
            
            if response.message.command == A_CNXN:
                # Connection established without authentication
                assert response.message.arg0 >= A_VERSION, "Version too low"
                assert response.message.arg1 > 0, "Max payload is zero"
                packet_logger.debug("Connection established without authentication")
                
            elif response.message.command == A_AUTH:
                # Authentication required
                packet_logger.debug("Authentication required (this is expected for secure devices)")
                # We'll handle auth in a separate test
            
        finally:
            connection.disconnect()
        
        packet_logger.debug("ADB protocol handshake completed")

    def test_connection_context_manager(self, packet_logger):
        """Test connection using context manager."""
        packet_logger.debug("Testing connection context manager")
        
        with AdbTcpConnection('localhost', 5555, timeout=10.0) as connection:
            assert connection.is_connected() is True
            
            # Test basic packet exchange
            connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, "host::")
            send_result = connection.send_packet(connect_packet)
            assert send_result is True
            
            response = connection.receive_packet()
            assert response is not None
            
            packet_logger.debug("Context manager connection successful")
        
        # Connection should be closed after context exit
        # Note: We can't easily test this without accessing private members
        
        packet_logger.debug("Connection context manager works correctly")

    def test_connection_timeout(self, packet_logger):
        """Test connection timeout handling."""
        packet_logger.debug("Testing connection timeout")
        
        # Test with very short timeout to unreachable address
        connection = AdbTcpConnection('***************', 5554, timeout=0.1)
        
        start_time = time.time()
        result = connection.connect()
        elapsed_time = time.time() - start_time
        
        assert result is False, "Connection should have failed"
        assert elapsed_time < 2.0, "Timeout took too long"
        assert connection.is_connected() is False
        
        packet_logger.debug(f"Connection timeout handled correctly in {elapsed_time:.2f}s")

    def test_connection_error_handling(self, packet_logger):
        """Test connection error handling."""
        packet_logger.debug("Testing connection error handling")
        
        # Test connection to wrong port
        connection = AdbTcpConnection('localhost', 9999, timeout=2.0)  # Wrong port
        
        result = connection.connect()
        assert result is False, "Connection should have failed to wrong port"
        assert connection.is_connected() is False
        
        # Test multiple connection attempts
        result2 = connection.connect()
        assert result2 is False, "Second connection attempt should also fail"
        
        packet_logger.debug("Connection error handling works correctly")

    def test_packet_send_receive_basic(self, adb_connection, packet_logger):
        """Test basic packet send/receive functionality."""
        packet_logger.debug("Testing basic packet send/receive")
        
        # Send CNXN packet
        connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, "host::test")
        
        packet_logger.debug(f"Sending packet: {connect_packet}")
        send_result = adb_connection.send_packet(connect_packet)
        assert send_result is True
        
        # Receive response
        response = adb_connection.receive_packet()
        assert response is not None
        
        packet_logger.debug(f"Received packet: {response}")
        
        # Verify response structure
        assert isinstance(response, AdbPacket)
        assert isinstance(response.message, AdbMessage)
        assert response.message.command in [A_CNXN, A_AUTH]
        
        # Verify magic field
        expected_magic = response.message.command ^ 0xffffffff
        assert response.message.magic == expected_magic
        
        packet_logger.debug("Basic packet send/receive successful")

    def test_packet_integrity(self, adb_connection, packet_logger):
        """Test packet integrity and checksums."""
        packet_logger.debug("Testing packet integrity")
        
        # Create packet with payload
        test_payload = b"test payload for integrity check"
        connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, "host::integrity_test")
        
        # Verify packet structure before sending
        packet_bytes = connect_packet.to_bytes()
        assert len(packet_bytes) >= 24  # Minimum header size
        
        # Send and receive
        send_result = adb_connection.send_packet(connect_packet)
        assert send_result is True
        
        response = adb_connection.receive_packet()
        assert response is not None
        
        # Verify response integrity
        response_bytes = response.to_bytes()
        reconstructed = AdbPacket.from_bytes(response_bytes)
        
        assert reconstructed.message.command == response.message.command
        assert reconstructed.message.arg0 == response.message.arg0
        assert reconstructed.message.arg1 == response.message.arg1
        assert reconstructed.payload == response.payload
        
        packet_logger.debug("Packet integrity verification successful")

    def test_connection_stability(self, adb_connection, packet_logger):
        """Test connection stability with multiple operations."""
        packet_logger.debug("Testing connection stability")
        
        # Perform multiple packet exchanges
        for i in range(5):
            connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, f"host::stability_test_{i}")
            
            send_result = adb_connection.send_packet(connect_packet)
            assert send_result is True, f"Failed to send packet {i}"
            
            response = adb_connection.receive_packet()
            assert response is not None, f"No response for packet {i}"
            
            # Small delay between operations
            time.sleep(0.1)
        
        # Verify connection is still active
        assert adb_connection.is_connected() is True
        
        packet_logger.debug("Connection stability test passed")

    def test_large_packet_handling(self, adb_connection, packet_logger):
        """Test handling of large packets."""
        packet_logger.debug("Testing large packet handling")
        
        # Create packet with large payload (but within limits)
        large_payload = b"x" * 1000  # 1KB payload
        large_identity = f"host::large_test_{large_payload[:10].decode()}"
        
        # Note: We can't easily test with MAX_PAYLOAD size in CNXN packet
        # as the identity string has practical limits
        connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, large_identity)
        
        send_result = adb_connection.send_packet(connect_packet)
        assert send_result is True
        
        response = adb_connection.receive_packet()
        assert response is not None
        
        packet_logger.debug("Large packet handling successful")

    def test_concurrent_connections(self, emulator_available, packet_logger):
        """Test multiple concurrent connections to emulator."""
        packet_logger.debug("Testing concurrent connections")
        
        connections = []
        try:
            # Create multiple connections
            for i in range(3):
                conn = AdbTcpConnection('localhost', 5555, timeout=10.0)
                result = conn.connect()
                assert result is True, f"Failed to establish connection {i}"
                connections.append(conn)
            
            # Test that all connections work
            for i, conn in enumerate(connections):
                connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, f"host::concurrent_{i}")
                send_result = conn.send_packet(connect_packet)
                assert send_result is True, f"Failed to send on connection {i}"
                
                response = conn.receive_packet()
                assert response is not None, f"No response on connection {i}"
            
            packet_logger.debug("All concurrent connections working")
            
        finally:
            # Clean up connections
            for conn in connections:
                try:
                    conn.disconnect()
                except:
                    pass
        
        packet_logger.debug("Concurrent connections test passed")

    def test_connection_recovery(self, packet_logger):
        """Test connection recovery after network issues."""
        packet_logger.debug("Testing connection recovery")
        
        connection = AdbTcpConnection('localhost', 5555, timeout=10.0)
        
        try:
            # Establish initial connection
            result = connection.connect()
            assert result is True
            
            # Simulate connection loss by disconnecting
            connection.disconnect()
            assert connection.is_connected() is False
            
            # Attempt to reconnect
            result = connection.connect()
            assert result is True, "Failed to reconnect"
            assert connection.is_connected() is True
            
            # Verify connection works after recovery
            connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, "host::recovery_test")
            send_result = connection.send_packet(connect_packet)
            assert send_result is True
            
            response = connection.receive_packet()
            assert response is not None
            
        finally:
            connection.disconnect()
        
        packet_logger.debug("Connection recovery test passed")
