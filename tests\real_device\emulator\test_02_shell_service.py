"""
Integration tests for ADB Shell Service

Tests shell command execution, streaming, device information retrieval,
and error handling using the Android emulator at localhost:5555.
"""

import pytest
import time
import threading
import logging
from io import String<PERSON>
from typing import List, Dict, Any

from pyadb import AdbTcpConnection, AdbShellService
from pyadb.shell import ShellResult, ShellProtocolPacket, ShellProtocolId
from pyadb.const import FEATURE_SHELL2, FEATURE_CMD

logger = logging.getLogger(__name__)


@pytest.mark.real_device
@pytest.mark.integration
class TestShellService:
    """Test ADB Shell Service functionality."""

    def test_shell_service_initialization(self, adb_connection, packet_logger):
        """Test shell service initialization and feature detection."""
        packet_logger.debug("Testing shell service initialization")
        
        shell = AdbShellService(adb_connection)
        
        # Verify basic initialization
        assert shell.connection == adb_connection
        assert isinstance(shell.features, list)
        
        # Check feature support detection
        packet_logger.debug(f"Shell features: {shell.features}")
        packet_logger.debug(f"Shell v2 support: {shell.supports_shell2}")
        packet_logger.debug(f"CMD support: {shell.supports_cmd}")
        
        # Features should be boolean
        assert isinstance(shell.supports_shell2, bool)
        assert isinstance(shell.supports_cmd, bool)
        
        packet_logger.debug("Shell service initialization successful")

    def test_basic_command_execution(self, shell_service, packet_logger):
        """Test basic shell command execution."""
        packet_logger.debug("Testing basic command execution")
        
        # Test simple echo command
        result = shell_service.execute_command('echo "Hello PyADB"')
        
        packet_logger.debug(f"Command result: exit_code={result.exit_code}")
        packet_logger.debug(f"Stdout: {result.stdout}")
        packet_logger.debug(f"Stderr: {result.stderr}")
        
        assert isinstance(result, ShellResult)
        assert result.exit_code == 0
        assert b"Hello PyADB" in result.stdout
        assert len(result.stderr) == 0
        
        packet_logger.debug("Basic command execution successful")

    def test_command_with_exit_code(self, shell_service, packet_logger):
        """Test command execution with non-zero exit codes."""
        packet_logger.debug("Testing command with exit codes")
        
        # Test command that should fail
        result = shell_service.execute_command('exit 42')
        
        packet_logger.debug(f"Exit command result: exit_code={result.exit_code}")
        
        assert isinstance(result, ShellResult)
        assert result.exit_code == 42
        
        # Test another exit code
        result = shell_service.execute_command('sh -c "exit 5"')
        assert result.exit_code == 5
        
        packet_logger.debug("Command exit code handling successful")

    def test_command_with_stderr(self, shell_service, packet_logger):
        """Test command execution with stderr output."""
        packet_logger.debug("Testing command with stderr")
        
        # Command that writes to stderr
        result = shell_service.execute_command('sh -c "echo error message >&2; echo stdout message"')
        
        packet_logger.debug(f"Stderr command result: exit_code={result.exit_code}")
        packet_logger.debug(f"Stdout: {result.stdout}")
        packet_logger.debug(f"Stderr: {result.stderr}")
        
        assert isinstance(result, ShellResult)
        assert result.exit_code == 0
        assert b"stdout message" in result.stdout
        assert b"error message" in result.stderr
        
        packet_logger.debug("Command stderr handling successful")

    def test_long_running_command(self, shell_service, packet_logger):
        """Test execution of long-running commands."""
        packet_logger.debug("Testing long-running command")
        
        # Command that takes some time
        start_time = time.time()
        result = shell_service.execute_command('sleep 2; echo "done sleeping"')
        elapsed_time = time.time() - start_time
        
        packet_logger.debug(f"Long command result: exit_code={result.exit_code}, elapsed={elapsed_time:.2f}s")
        
        assert isinstance(result, ShellResult)
        assert result.exit_code == 0
        assert b"done sleeping" in result.stdout
        assert elapsed_time >= 2.0  # Should take at least 2 seconds
        assert elapsed_time < 5.0   # But not too long
        
        packet_logger.debug("Long-running command successful")

    def test_command_with_large_output(self, shell_service, packet_logger):
        """Test command execution with large output."""
        packet_logger.debug("Testing command with large output")
        
        # Generate large output (about 10KB)
        result = shell_service.execute_command('yes "This is a test line for large output" | head -n 200')
        
        packet_logger.debug(f"Large output result: exit_code={result.exit_code}, stdout_size={len(result.stdout)}")
        
        assert isinstance(result, ShellResult)
        assert result.exit_code == 0
        assert len(result.stdout) > 5000  # Should be substantial output
        assert b"This is a test line" in result.stdout
        
        packet_logger.debug("Large output command successful")

    def test_command_timeout(self, shell_service, packet_logger):
        """Test command execution timeout handling."""
        packet_logger.debug("Testing command timeout")
        
        # This test depends on the shell service supporting timeouts
        # For now, we'll test a reasonable command that should complete quickly
        start_time = time.time()
        result = shell_service.execute_command('echo "timeout test"', timeout=10.0)
        elapsed_time = time.time() - start_time
        
        packet_logger.debug(f"Timeout test result: exit_code={result.exit_code}, elapsed={elapsed_time:.2f}s")
        
        assert isinstance(result, ShellResult)
        assert result.exit_code == 0
        assert elapsed_time < 5.0  # Should complete quickly
        
        packet_logger.debug("Command timeout handling successful")

    def test_device_info_retrieval(self, shell_service, packet_logger):
        """Test device information retrieval."""
        packet_logger.debug("Testing device info retrieval")
        
        device_info = shell_service.get_device_info()
        
        packet_logger.debug(f"Device info: {device_info}")
        
        assert isinstance(device_info, dict)
        
        # Check for expected keys (these may vary by device)
        expected_keys = ['model', 'manufacturer', 'android_version', 'api_level', 'build_id']
        found_keys = []
        
        for key in expected_keys:
            if key in device_info:
                found_keys.append(key)
                packet_logger.debug(f"  {key}: {device_info[key]}")
        
        # Should have at least some device information
        assert len(device_info) > 0
        assert len(found_keys) > 0
        
        packet_logger.debug("Device info retrieval successful")

    def test_multiple_concurrent_commands(self, shell_service, packet_logger):
        """Test multiple concurrent command executions."""
        packet_logger.debug("Testing multiple concurrent commands")
        
        results = []
        threads = []
        
        def execute_command(cmd_id):
            result = shell_service.execute_command(f'echo "Command {cmd_id} output"')
            results.append((cmd_id, result))
        
        # Start multiple threads
        for i in range(3):
            thread = threading.Thread(target=execute_command, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=10.0)
        
        packet_logger.debug(f"Concurrent commands completed: {len(results)} results")
        
        # Verify all commands completed successfully
        assert len(results) == 3
        
        for cmd_id, result in results:
            assert isinstance(result, ShellResult)
            assert result.exit_code == 0
            assert f"Command {cmd_id} output".encode() in result.stdout
            packet_logger.debug(f"  Command {cmd_id}: success")
        
        packet_logger.debug("Multiple concurrent commands successful")

    def test_special_characters_in_commands(self, shell_service, packet_logger):
        """Test commands with special characters."""
        packet_logger.debug("Testing special characters in commands")
        
        # Test various special characters
        test_cases = [
            ('echo "Hello & World"', b"Hello & World"),
            ('echo "Test | Pipe"', b"Test | Pipe"),
            ('echo "Quote \\"test\\""', b'Quote "test"'),
            ('echo "Newline\\nTest"', b"Newline\nTest"),
        ]
        
        for cmd, expected in test_cases:
            result = shell_service.execute_command(cmd)
            packet_logger.debug(f"Special char test: {cmd} -> exit_code={result.exit_code}")
            
            assert isinstance(result, ShellResult)
            assert result.exit_code == 0
            assert expected in result.stdout
        
        packet_logger.debug("Special characters handling successful")

    def test_empty_and_whitespace_commands(self, shell_service, packet_logger):
        """Test empty and whitespace-only commands."""
        packet_logger.debug("Testing empty and whitespace commands")
        
        # Test empty command
        result = shell_service.execute_command('')
        packet_logger.debug(f"Empty command result: exit_code={result.exit_code}")
        
        # Empty command behavior may vary, but should not crash
        assert isinstance(result, ShellResult)
        
        # Test whitespace-only command
        result = shell_service.execute_command('   ')
        packet_logger.debug(f"Whitespace command result: exit_code={result.exit_code}")
        
        assert isinstance(result, ShellResult)
        
        packet_logger.debug("Empty and whitespace commands handled")

    def test_command_with_environment_variables(self, shell_service, packet_logger):
        """Test commands using environment variables."""
        packet_logger.debug("Testing environment variables")
        
        # Test accessing environment variables
        result = shell_service.execute_command('echo "PATH: $PATH"')
        
        packet_logger.debug(f"Environment test result: exit_code={result.exit_code}")
        packet_logger.debug(f"PATH output: {result.stdout[:100]}...")  # First 100 chars
        
        assert isinstance(result, ShellResult)
        assert result.exit_code == 0
        assert b"PATH:" in result.stdout
        
        # Test setting and using custom environment variable
        result = shell_service.execute_command('TEST_VAR="hello world"; echo "TEST_VAR: $TEST_VAR"')
        
        assert isinstance(result, ShellResult)
        assert result.exit_code == 0
        assert b"TEST_VAR: hello world" in result.stdout
        
        packet_logger.debug("Environment variables handling successful")

    def test_streaming_command_execution(self, shell_service, packet_logger):
        """Test streaming command execution with callbacks."""
        packet_logger.debug("Testing streaming command execution")

        stdout_data = []
        stderr_data = []

        def stdout_callback(data):
            stdout_data.append(data)
            packet_logger.debug(f"Stdout callback: {len(data)} bytes")

        def stderr_callback(data):
            stderr_data.append(data)
            packet_logger.debug(f"Stderr callback: {len(data)} bytes")

        # Execute command with streaming
        exit_code = shell_service.execute_command_streaming(
            'echo "Line 1"; echo "Line 2"; echo "Error line" >&2',
            stdout_callback=stdout_callback,
            stderr_callback=stderr_callback
        )

        packet_logger.debug(f"Streaming command exit_code: {exit_code}")

        # Verify streaming worked
        assert exit_code == 0
        assert len(stdout_data) > 0
        assert len(stderr_data) > 0

        # Combine all stdout data
        all_stdout = b''.join(stdout_data)
        all_stderr = b''.join(stderr_data)

        assert b"Line 1" in all_stdout
        assert b"Line 2" in all_stdout
        assert b"Error line" in all_stderr

        packet_logger.debug("Streaming command execution successful")

    def test_interactive_shell_session(self, shell_service, packet_logger):
        """Test interactive shell session functionality."""
        packet_logger.debug("Testing interactive shell session")

        # This test may need to be adapted based on the actual implementation
        # For now, test basic shell interaction

        # Test multiple commands in sequence
        commands = [
            'echo "Command 1"',
            'pwd',
            'echo "Command 2"'
        ]

        results = []
        for cmd in commands:
            result = shell_service.execute_command(cmd)
            results.append(result)
            packet_logger.debug(f"Interactive command '{cmd}': exit_code={result.exit_code}")

        # Verify all commands executed successfully
        for i, result in enumerate(results):
            assert isinstance(result, ShellResult)
            assert result.exit_code == 0
            packet_logger.debug(f"  Command {i+1}: success")

        packet_logger.debug("Interactive shell session successful")

    def test_shell_protocol_packets(self, shell_service, packet_logger, packet_capture):
        """Test shell protocol packet handling."""
        packet_logger.debug("Testing shell protocol packets")

        # Enable packet capture
        packet_capture.enable()

        try:
            # Execute a command that should generate protocol packets
            result = shell_service.execute_command('echo "Protocol test"')

            packet_logger.debug(f"Protocol test result: exit_code={result.exit_code}")

            assert isinstance(result, ShellResult)
            assert result.exit_code == 0
            assert b"Protocol test" in result.stdout

            # Check captured packets
            sent_packets = packet_capture.sent_packets
            received_packets = packet_capture.received_packets

            packet_logger.debug(f"Captured packets: {len(sent_packets)} sent, {len(received_packets)} received")

            # Should have some packet activity
            assert len(sent_packets) > 0 or len(received_packets) > 0

        finally:
            packet_capture.disable()

        packet_logger.debug("Shell protocol packets test successful")

    def test_command_error_handling(self, shell_service, packet_logger):
        """Test various error conditions and edge cases."""
        packet_logger.debug("Testing command error handling")

        # Test non-existent command
        result = shell_service.execute_command('nonexistent_command_12345')
        packet_logger.debug(f"Non-existent command result: exit_code={result.exit_code}")

        assert isinstance(result, ShellResult)
        assert result.exit_code != 0  # Should fail

        # Test command with syntax error
        result = shell_service.execute_command('if [ incomplete')
        packet_logger.debug(f"Syntax error command result: exit_code={result.exit_code}")

        assert isinstance(result, ShellResult)
        assert result.exit_code != 0  # Should fail

        # Test permission denied (try to access restricted file)
        result = shell_service.execute_command('cat /proc/version')  # This should work
        packet_logger.debug(f"Proc version result: exit_code={result.exit_code}")

        assert isinstance(result, ShellResult)
        # This should succeed on most systems

        packet_logger.debug("Command error handling successful")

    def test_shell_service_cleanup(self, adb_connection, packet_logger):
        """Test shell service cleanup and resource management."""
        packet_logger.debug("Testing shell service cleanup")

        # Create multiple shell services
        shells = []
        for i in range(3):
            shell = AdbShellService(adb_connection)
            shells.append(shell)

            # Execute a command with each
            result = shell.execute_command(f'echo "Shell {i} test"')
            assert result.exit_code == 0
            packet_logger.debug(f"Shell {i}: command executed successfully")

        # Test that all shells can be used concurrently
        for i, shell in enumerate(shells):
            result = shell.execute_command(f'echo "Concurrent test {i}"')
            assert result.exit_code == 0

        packet_logger.debug("Shell service cleanup successful")

    def test_shell_feature_detection(self, shell_service, packet_logger):
        """Test shell feature detection and capability checking."""
        packet_logger.debug("Testing shell feature detection")

        # Check feature flags
        features = shell_service.features
        supports_shell2 = shell_service.supports_shell2
        supports_cmd = shell_service.supports_cmd

        packet_logger.debug(f"Detected features: {features}")
        packet_logger.debug(f"Shell v2 support: {supports_shell2}")
        packet_logger.debug(f"CMD support: {supports_cmd}")

        # Features should be consistent
        if FEATURE_SHELL2 in features:
            assert supports_shell2 is True
        else:
            assert supports_shell2 is False

        if FEATURE_CMD in features:
            assert supports_cmd is True
        else:
            assert supports_cmd is False

        # Test feature-dependent behavior
        if supports_shell2:
            packet_logger.debug("Testing shell v2 specific functionality")
            # Shell v2 should support better error reporting
            result = shell_service.execute_command('echo "Shell v2 test"')
            assert isinstance(result, ShellResult)
            assert result.exit_code == 0

        packet_logger.debug("Shell feature detection successful")
