"""
Integration tests for ADB File Operations (Pull and Push Services)

Tests file transfer functionality including pull/push operations,
progress callbacks, and error handling using the Android emulator.
"""

import pytest
import os
import time
import tempfile
import hashlib
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional

from pyadb import AdbTcpConnection, AdbPullService, AdbPushService, AdbShellService
from pyadb.pull import SyncRequest, SyncResponse
from pyadb.push import SYNC_DATA_MAX

logger = logging.getLogger(__name__)


@pytest.mark.real_device
@pytest.mark.integration
class TestFileOperations:
    """Test ADB file pull and push operations."""

    def test_pull_service_initialization(self, adb_connection, packet_logger):
        """Test pull service initialization."""
        packet_logger.debug("Testing pull service initialization")
        
        pull_service = AdbPullService(adb_connection)
        
        # Verify basic initialization
        assert pull_service.connection == adb_connection
        assert isinstance(pull_service.features, list)
        
        # Check feature support detection
        packet_logger.debug(f"Pull service features: {pull_service.features}")
        packet_logger.debug(f"Stat v2 support: {pull_service.supports_stat_v2}")
        packet_logger.debug(f"Sendrecv v2 support: {pull_service.supports_sendrecv_v2}")
        
        assert isinstance(pull_service.supports_stat_v2, bool)
        assert isinstance(pull_service.supports_sendrecv_v2, bool)
        
        packet_logger.debug("Pull service initialization successful")

    def test_push_service_initialization(self, adb_connection, packet_logger):
        """Test push service initialization."""
        packet_logger.debug("Testing push service initialization")
        
        push_service = AdbPushService(adb_connection)
        
        # Verify basic initialization
        assert push_service.connection == adb_connection
        assert isinstance(push_service.features, list)
        
        # Check feature support detection
        packet_logger.debug(f"Push service features: {push_service.features}")
        packet_logger.debug(f"Sendrecv v2 support: {push_service.supports_sendrecv_v2}")
        
        assert isinstance(push_service.supports_sendrecv_v2, bool)
        
        packet_logger.debug("Push service initialization successful")

    def test_file_stat_operation(self, pull_service, packet_logger):
        """Test file stat operation."""
        packet_logger.debug("Testing file stat operation")
        
        # Test stat on a known file
        stat_info = pull_service.stat_file('/system/build.prop')
        
        packet_logger.debug(f"Stat result: {stat_info}")
        
        if stat_info:
            assert isinstance(stat_info, dict)
            assert 'size' in stat_info
            assert 'mode' in stat_info
            assert 'mtime' in stat_info
            
            # Size should be reasonable for build.prop
            assert stat_info['size'] > 0
            assert stat_info['size'] < 100000  # Should be less than 100KB
            
            packet_logger.debug(f"build.prop size: {stat_info['size']} bytes")
        else:
            packet_logger.warning("Could not stat /system/build.prop - may not exist on this device")
        
        # Test stat on non-existent file
        stat_info = pull_service.stat_file('/nonexistent/file/path')
        assert stat_info is None
        
        packet_logger.debug("File stat operation successful")

    def test_small_file_pull(self, pull_service, temp_test_dir, packet_logger):
        """Test pulling a small file."""
        packet_logger.debug("Testing small file pull")
        
        # Create a small test file on device first
        shell = AdbShellService(pull_service.connection)
        test_content = "Hello PyADB File Transfer Test\nLine 2\nLine 3\n"
        remote_path = "/data/local/tmp/test_small_file.txt"
        
        # Create test file on device
        result = shell.execute_command(f'echo "{test_content}" > {remote_path}')
        assert result.exit_code == 0
        
        try:
            # Pull the file
            local_path = temp_test_dir / "pulled_small_file.txt"
            
            progress_calls = []
            def progress_callback(bytes_transferred, total_bytes):
                progress_calls.append((bytes_transferred, total_bytes))
                packet_logger.debug(f"Progress: {bytes_transferred}/{total_bytes} bytes")
            
            success = pull_service.pull_file(
                remote_path, 
                str(local_path),
                progress_callback=progress_callback
            )
            
            packet_logger.debug(f"Pull result: {success}")
            packet_logger.debug(f"Progress calls: {len(progress_calls)}")
            
            assert success is True
            assert local_path.exists()
            
            # Verify file content
            pulled_content = local_path.read_text()
            assert test_content.strip() in pulled_content
            
            # Verify progress callbacks were called
            assert len(progress_calls) > 0
            
            # Last progress call should show completion
            final_transferred, final_total = progress_calls[-1]
            assert final_transferred == final_total
            assert final_total > 0
            
        finally:
            # Clean up remote file
            shell.execute_command(f'rm -f {remote_path}')
        
        packet_logger.debug("Small file pull successful")

    def test_large_file_pull(self, pull_service, temp_test_dir, packet_logger):
        """Test pulling a larger file with progress tracking."""
        packet_logger.debug("Testing large file pull")
        
        shell = AdbShellService(pull_service.connection)
        remote_path = "/data/local/tmp/test_large_file.txt"
        
        # Create a larger test file (about 50KB)
        result = shell.execute_command(f'yes "This is a test line for large file transfer testing." | head -n 1000 > {remote_path}')
        assert result.exit_code == 0
        
        try:
            # Verify file was created and get its size
            stat_result = shell.execute_command(f'stat -c %s {remote_path}')
            assert stat_result.exit_code == 0
            file_size = int(stat_result.stdout.decode().strip())
            packet_logger.debug(f"Created large file: {file_size} bytes")
            
            # Pull the file
            local_path = temp_test_dir / "pulled_large_file.txt"
            
            progress_calls = []
            def progress_callback(bytes_transferred, total_bytes):
                progress_calls.append((bytes_transferred, total_bytes))
                if len(progress_calls) % 10 == 0:  # Log every 10th call
                    percent = (bytes_transferred / total_bytes) * 100
                    packet_logger.debug(f"Progress: {bytes_transferred}/{total_bytes} bytes ({percent:.1f}%)")
            
            start_time = time.time()
            success = pull_service.pull_file(
                remote_path,
                str(local_path),
                progress_callback=progress_callback
            )
            elapsed_time = time.time() - start_time
            
            packet_logger.debug(f"Large pull result: {success}, elapsed: {elapsed_time:.2f}s")
            packet_logger.debug(f"Progress calls: {len(progress_calls)}")
            
            assert success is True
            assert local_path.exists()
            
            # Verify file size
            pulled_size = local_path.stat().st_size
            assert pulled_size == file_size
            
            # Verify progress tracking
            assert len(progress_calls) > 0
            
            # Check progress consistency
            for bytes_transferred, total_bytes in progress_calls:
                assert bytes_transferred <= total_bytes
                assert total_bytes == file_size
            
            # Final progress should show completion
            final_transferred, final_total = progress_calls[-1]
            assert final_transferred == final_total == file_size
            
        finally:
            # Clean up remote file
            shell.execute_command(f'rm -f {remote_path}')
        
        packet_logger.debug("Large file pull successful")

    def test_small_file_push(self, push_service, temp_test_dir, packet_logger):
        """Test pushing a small file."""
        packet_logger.debug("Testing small file push")
        
        # Create a small test file locally
        test_content = "Hello PyADB Push Test\nThis is line 2\nAnd line 3\n"
        local_path = temp_test_dir / "test_push_small.txt"
        local_path.write_text(test_content)
        
        remote_path = "/data/local/tmp/pushed_small_file.txt"
        shell = AdbShellService(push_service.connection)
        
        try:
            # Push the file
            progress_calls = []
            def progress_callback(bytes_transferred, total_bytes):
                progress_calls.append((bytes_transferred, total_bytes))
                packet_logger.debug(f"Push progress: {bytes_transferred}/{total_bytes} bytes")
            
            success = push_service.push_file(
                str(local_path),
                remote_path,
                progress_callback=progress_callback
            )
            
            packet_logger.debug(f"Push result: {success}")
            packet_logger.debug(f"Progress calls: {len(progress_calls)}")
            
            assert success is True
            
            # Verify file was pushed correctly
            result = shell.execute_command(f'cat {remote_path}')
            assert result.exit_code == 0
            
            pushed_content = result.stdout.decode()
            assert test_content.strip() in pushed_content
            
            # Verify progress callbacks
            assert len(progress_calls) > 0
            
            # Check final progress
            final_transferred, final_total = progress_calls[-1]
            assert final_transferred == final_total
            assert final_total > 0
            
        finally:
            # Clean up remote file
            shell.execute_command(f'rm -f {remote_path}')
        
        packet_logger.debug("Small file push successful")

    def test_large_file_push(self, push_service, temp_test_dir, packet_logger):
        """Test pushing a larger file with progress tracking."""
        packet_logger.debug("Testing large file push")
        
        # Create a larger test file locally (about 100KB)
        test_line = "This is a test line for large file push testing with some extra content.\n"
        test_content = test_line * 1500  # About 100KB
        
        local_path = temp_test_dir / "test_push_large.txt"
        local_path.write_text(test_content)
        
        file_size = local_path.stat().st_size
        packet_logger.debug(f"Created large local file: {file_size} bytes")
        
        remote_path = "/data/local/tmp/pushed_large_file.txt"
        shell = AdbShellService(push_service.connection)
        
        try:
            # Push the file
            progress_calls = []
            def progress_callback(bytes_transferred, total_bytes):
                progress_calls.append((bytes_transferred, total_bytes))
                if len(progress_calls) % 20 == 0:  # Log every 20th call
                    percent = (bytes_transferred / total_bytes) * 100
                    packet_logger.debug(f"Push progress: {bytes_transferred}/{total_bytes} bytes ({percent:.1f}%)")
            
            start_time = time.time()
            success = push_service.push_file(
                str(local_path),
                remote_path,
                progress_callback=progress_callback
            )
            elapsed_time = time.time() - start_time
            
            packet_logger.debug(f"Large push result: {success}, elapsed: {elapsed_time:.2f}s")
            packet_logger.debug(f"Progress calls: {len(progress_calls)}")
            
            assert success is True
            
            # Verify file size on device
            result = shell.execute_command(f'stat -c %s {remote_path}')
            assert result.exit_code == 0
            
            remote_size = int(result.stdout.decode().strip())
            assert remote_size == file_size
            
            # Verify progress tracking
            assert len(progress_calls) > 0
            
            # Check progress consistency
            for bytes_transferred, total_bytes in progress_calls:
                assert bytes_transferred <= total_bytes
                assert total_bytes == file_size
            
            # Final progress should show completion
            final_transferred, final_total = progress_calls[-1]
            assert final_transferred == final_total == file_size
            
        finally:
            # Clean up remote file
            shell.execute_command(f'rm -f {remote_path}')
        
        packet_logger.debug("Large file push successful")

    def test_binary_file_transfer(self, pull_service, push_service, temp_test_dir, packet_logger):
        """Test transfer of binary files."""
        packet_logger.debug("Testing binary file transfer")

        # Create a binary test file with various byte values
        binary_data = bytes(range(256)) * 100  # 25.6KB of binary data
        local_path = temp_test_dir / "test_binary.bin"
        local_path.write_bytes(binary_data)

        original_hash = hashlib.md5(binary_data).hexdigest()
        packet_logger.debug(f"Original binary file hash: {original_hash}")

        remote_path = "/data/local/tmp/test_binary.bin"
        shell = AdbShellService(push_service.connection)

        try:
            # Push binary file
            success = push_service.push_file(str(local_path), remote_path)
            assert success is True

            # Pull it back to a different location
            pulled_path = temp_test_dir / "pulled_binary.bin"
            success = pull_service.pull_file(remote_path, str(pulled_path))
            assert success is True

            # Verify binary integrity
            pulled_data = pulled_path.read_bytes()
            pulled_hash = hashlib.md5(pulled_data).hexdigest()

            packet_logger.debug(f"Pulled binary file hash: {pulled_hash}")

            assert len(pulled_data) == len(binary_data)
            assert pulled_hash == original_hash
            assert pulled_data == binary_data

        finally:
            # Clean up
            shell.execute_command(f'rm -f {remote_path}')

        packet_logger.debug("Binary file transfer successful")

    def test_file_permissions_and_modes(self, push_service, temp_test_dir, packet_logger):
        """Test file permissions and mode handling."""
        packet_logger.debug("Testing file permissions and modes")

        # Create test file
        test_content = "#!/bin/sh\necho 'Test script'\n"
        local_path = temp_test_dir / "test_script.sh"
        local_path.write_text(test_content)

        remote_path = "/data/local/tmp/test_script.sh"
        shell = AdbShellService(push_service.connection)

        try:
            # Push with executable permissions
            success = push_service.push_file(
                str(local_path),
                remote_path,
                mode=0o755  # rwxr-xr-x
            )
            assert success is True

            # Check file permissions
            result = shell.execute_command(f'ls -l {remote_path}')
            assert result.exit_code == 0

            permissions = result.stdout.decode().strip()
            packet_logger.debug(f"File permissions: {permissions}")

            # Should have execute permissions
            assert 'rwx' in permissions or 'x' in permissions

        finally:
            # Clean up
            shell.execute_command(f'rm -f {remote_path}')

        packet_logger.debug("File permissions test successful")

    def test_pull_error_handling(self, pull_service, temp_test_dir, packet_logger):
        """Test pull operation error handling."""
        packet_logger.debug("Testing pull error handling")

        # Test pulling non-existent file
        local_path = temp_test_dir / "nonexistent_pull.txt"
        success = pull_service.pull_file("/nonexistent/file/path", str(local_path))

        packet_logger.debug(f"Non-existent file pull result: {success}")
        assert success is False
        assert not local_path.exists()

        # Test pulling from restricted location (may vary by device)
        success = pull_service.pull_file("/root/restricted_file", str(local_path))
        packet_logger.debug(f"Restricted file pull result: {success}")
        # This should fail, but exact behavior may vary

        # Test pulling to invalid local path
        try:
            success = pull_service.pull_file("/system/build.prop", "/invalid/path/file.txt")
            # This might fail due to invalid local path
            packet_logger.debug(f"Invalid local path pull result: {success}")
        except Exception as e:
            packet_logger.debug(f"Expected exception for invalid path: {e}")

        packet_logger.debug("Pull error handling test successful")

    def test_push_error_handling(self, push_service, temp_test_dir, packet_logger):
        """Test push operation error handling."""
        packet_logger.debug("Testing push error handling")

        # Test pushing non-existent local file
        success = push_service.push_file("/nonexistent/local/file.txt", "/data/local/tmp/test.txt")

        packet_logger.debug(f"Non-existent local file push result: {success}")
        assert success is False

        # Test pushing to restricted remote location
        test_file = temp_test_dir / "test_restricted.txt"
        test_file.write_text("Test content")

        success = push_service.push_file(str(test_file), "/system/restricted_file.txt")
        packet_logger.debug(f"Restricted remote path push result: {success}")
        # This should fail due to permissions
        assert success is False

        # Test pushing empty file
        empty_file = temp_test_dir / "empty_file.txt"
        empty_file.write_text("")

        remote_path = "/data/local/tmp/empty_test.txt"
        shell = AdbShellService(push_service.connection)

        try:
            success = push_service.push_file(str(empty_file), remote_path)
            packet_logger.debug(f"Empty file push result: {success}")

            if success:
                # Verify empty file was pushed
                result = shell.execute_command(f'stat -c %s {remote_path}')
                if result.exit_code == 0:
                    size = int(result.stdout.decode().strip())
                    assert size == 0
        finally:
            shell.execute_command(f'rm -f {remote_path}')

        packet_logger.debug("Push error handling test successful")

    def test_concurrent_file_operations(self, pull_service, push_service, temp_test_dir, packet_logger):
        """Test concurrent file operations."""
        packet_logger.debug("Testing concurrent file operations")

        import threading

        shell = AdbShellService(pull_service.connection)
        results = []

        def push_file_task(file_id):
            try:
                # Create local file
                content = f"Concurrent test file {file_id}\n" * 100
                local_path = temp_test_dir / f"concurrent_push_{file_id}.txt"
                local_path.write_text(content)

                remote_path = f"/data/local/tmp/concurrent_push_{file_id}.txt"

                # Push file
                success = push_service.push_file(str(local_path), remote_path)
                results.append(('push', file_id, success))

                # Clean up
                if success:
                    shell.execute_command(f'rm -f {remote_path}')

            except Exception as e:
                results.append(('push', file_id, False, str(e)))

        def pull_file_task(file_id):
            try:
                # Create remote file first
                content = f"Concurrent pull test {file_id}"
                remote_path = f"/data/local/tmp/concurrent_pull_{file_id}.txt"

                result = shell.execute_command(f'echo "{content}" > {remote_path}')
                if result.exit_code != 0:
                    results.append(('pull', file_id, False, 'Failed to create remote file'))
                    return

                # Pull file
                local_path = temp_test_dir / f"concurrent_pull_{file_id}.txt"
                success = pull_service.pull_file(remote_path, str(local_path))
                results.append(('pull', file_id, success))

                # Clean up
                shell.execute_command(f'rm -f {remote_path}')

            except Exception as e:
                results.append(('pull', file_id, False, str(e)))

        # Start concurrent operations
        threads = []
        for i in range(3):
            push_thread = threading.Thread(target=push_file_task, args=(i,))
            pull_thread = threading.Thread(target=pull_file_task, args=(i,))

            threads.extend([push_thread, pull_thread])
            push_thread.start()
            pull_thread.start()

        # Wait for all operations to complete
        for thread in threads:
            thread.join(timeout=30.0)

        packet_logger.debug(f"Concurrent operations completed: {len(results)} results")

        # Verify results
        push_results = [r for r in results if r[0] == 'push']
        pull_results = [r for r in results if r[0] == 'pull']

        assert len(push_results) == 3
        assert len(pull_results) == 3

        # Most operations should succeed
        successful_pushes = sum(1 for r in push_results if r[2] is True)
        successful_pulls = sum(1 for r in pull_results if r[2] is True)

        packet_logger.debug(f"Successful operations: {successful_pushes}/3 pushes, {successful_pulls}/3 pulls")

        # At least some should succeed (allowing for potential race conditions)
        assert successful_pushes >= 1
        assert successful_pulls >= 1

        packet_logger.debug("Concurrent file operations test successful")

    def test_file_sync_protocol_details(self, pull_service, push_service, packet_logger, packet_capture):
        """Test file sync protocol packet details."""
        packet_logger.debug("Testing file sync protocol details")

        # Enable packet capture
        packet_capture.enable()

        try:
            # Test stat operation with packet capture
            stat_info = pull_service.stat_file('/system/build.prop')

            if stat_info:
                packet_logger.debug(f"Stat operation captured packets")

                # Check for sync-related packets
                sent_packets = packet_capture.sent_packets
                received_packets = packet_capture.received_packets

                packet_logger.debug(f"Sync packets: {len(sent_packets)} sent, {len(received_packets)} received")

                # Should have some packet activity for sync operations
                assert len(sent_packets) > 0 or len(received_packets) > 0

        finally:
            packet_capture.disable()

        packet_logger.debug("File sync protocol details test successful")
