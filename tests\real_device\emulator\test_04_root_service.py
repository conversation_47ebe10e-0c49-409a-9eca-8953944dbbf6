"""
Integration tests for ADB Root Service

Tests root access management including enable/disable root and status checking
using the Android emulator.
"""

import pytest
import time
import logging
from typing import Optional

from pyadb import AdbTcpConnection, AdbRootService, AdbShellService
from pyadb.core import AdbPacket, AdbMessage
from pyadb.const import A_OKAY, A_CLSE

logger = logging.getLogger(__name__)


@pytest.mark.real_device
@pytest.mark.integration
class TestRootService:
    """Test ADB Root Service functionality."""

    def test_root_service_initialization(self, adb_connection, packet_logger):
        """Test root service initialization."""
        packet_logger.debug("Testing root service initialization")
        
        root_service = AdbRootService(adb_connection)
        
        # Verify basic initialization
        assert root_service.connection == adb_connection
        
        packet_logger.debug("Root service initialization successful")

    def test_root_status_checking(self, root_service, packet_logger):
        """Test root status checking functionality."""
        packet_logger.debug("Testing root status checking")
        
        # Check initial root status
        initial_status = root_service.check_root_status()
        
        packet_logger.debug(f"Initial root status: {initial_status}")
        
        # Status should be a boolean or None
        assert initial_status is None or isinstance(initial_status, bool)
        
        if initial_status is not None:
            if initial_status:
                packet_logger.debug("Device is currently running as root")
            else:
                packet_logger.debug("Device is currently running as non-root")
        else:
            packet_logger.debug("Unable to determine root status")
        
        packet_logger.debug("Root status checking successful")

    def test_root_status_via_shell_commands(self, shell_service, packet_logger):
        """Test root status detection via shell commands."""
        packet_logger.debug("Testing root status via shell commands")
        
        # Check user ID
        result = shell_service.execute_command('id -u')
        packet_logger.debug(f"User ID command result: exit_code={result.exit_code}")
        
        if result.exit_code == 0:
            uid = result.stdout.decode().strip()
            packet_logger.debug(f"Current UID: {uid}")
            
            is_root = uid == '0'
            packet_logger.debug(f"Is root (UID=0): {is_root}")
            
            # Also check with 'whoami'
            result = shell_service.execute_command('whoami')
            if result.exit_code == 0:
                username = result.stdout.decode().strip()
                packet_logger.debug(f"Current username: {username}")
                
                # Root user should be 'root'
                is_root_user = username == 'root'
                packet_logger.debug(f"Is root user: {is_root_user}")
                
                # UID and username should be consistent
                if is_root and is_root_user:
                    packet_logger.debug("Consistent root status detected")
                elif not is_root and not is_root_user:
                    packet_logger.debug("Consistent non-root status detected")
        
        packet_logger.debug("Root status via shell commands successful")

    def test_root_enable_attempt(self, root_service, packet_logger):
        """Test root enable functionality."""
        packet_logger.debug("Testing root enable attempt")
        
        # Check initial status
        initial_status = root_service.check_root_status()
        packet_logger.debug(f"Initial root status: {initial_status}")
        
        # Attempt to enable root
        packet_logger.debug("Attempting to enable root access...")
        enable_result = root_service.enable_root()
        
        packet_logger.debug(f"Root enable result: {enable_result}")
        
        # Result should be boolean
        assert isinstance(enable_result, bool)
        
        if enable_result:
            packet_logger.debug("Root enable command sent successfully")
            
            # Give some time for adbd to restart
            time.sleep(2.0)
            
            # Note: After root enable, the connection might be lost
            # as adbd restarts. This is expected behavior.
            packet_logger.debug("Root enable may cause connection restart")
            
        else:
            packet_logger.debug("Root enable failed - this is expected on production builds")
        
        packet_logger.debug("Root enable attempt completed")

    def test_root_disable_attempt(self, root_service, packet_logger):
        """Test root disable (unroot) functionality."""
        packet_logger.debug("Testing root disable attempt")
        
        # Check initial status
        initial_status = root_service.check_root_status()
        packet_logger.debug(f"Initial root status: {initial_status}")
        
        # Attempt to disable root
        packet_logger.debug("Attempting to disable root access...")
        disable_result = root_service.disable_root()
        
        packet_logger.debug(f"Root disable result: {disable_result}")
        
        # Result should be boolean
        assert isinstance(disable_result, bool)
        
        if disable_result:
            packet_logger.debug("Root disable command sent successfully")
            
            # Give some time for adbd to restart
            time.sleep(2.0)
            
            packet_logger.debug("Root disable may cause connection restart")
            
        else:
            packet_logger.debug("Root disable failed")
        
        packet_logger.debug("Root disable attempt completed")

    def test_root_service_error_handling(self, adb_connection, packet_logger):
        """Test root service error handling."""
        packet_logger.debug("Testing root service error handling")
        
        root_service = AdbRootService(adb_connection)
        
        # Test multiple rapid root status checks
        for i in range(3):
            status = root_service.check_root_status()
            packet_logger.debug(f"Rapid status check {i+1}: {status}")
            time.sleep(0.1)
        
        # Test root operations with potential connection issues
        try:
            # These operations might fail due to various reasons
            # but should not crash the service
            enable_result = root_service.enable_root()
            packet_logger.debug(f"Error handling enable result: {enable_result}")
            
            time.sleep(1.0)
            
            disable_result = root_service.disable_root()
            packet_logger.debug(f"Error handling disable result: {disable_result}")
            
        except Exception as e:
            packet_logger.debug(f"Expected exception during error handling test: {e}")
        
        packet_logger.debug("Root service error handling successful")

    def test_root_permissions_verification(self, shell_service, packet_logger):
        """Test verification of root permissions through file system access."""
        packet_logger.debug("Testing root permissions verification")
        
        # Test access to root-only locations
        test_commands = [
            ('ls /data/data', 'Access to /data/data'),
            ('ls /system', 'Access to /system'),
            ('cat /proc/version', 'Access to /proc/version'),
        ]
        
        for cmd, description in test_commands:
            result = shell_service.execute_command(cmd)
            packet_logger.debug(f"{description}: exit_code={result.exit_code}")
            
            if result.exit_code == 0:
                output_preview = result.stdout.decode()[:100]
                packet_logger.debug(f"  Output preview: {output_preview}...")
            else:
                error_preview = result.stderr.decode()[:100] if result.stderr else "No error output"
                packet_logger.debug(f"  Error preview: {error_preview}...")
        
        # Test write access to system locations (if root)
        write_test_result = shell_service.execute_command('touch /data/local/tmp/root_test_file')
        packet_logger.debug(f"Write test result: exit_code={write_test_result.exit_code}")
        
        if write_test_result.exit_code == 0:
            # Clean up test file
            shell_service.execute_command('rm -f /data/local/tmp/root_test_file')
            packet_logger.debug("Write test successful - file created and removed")
        else:
            packet_logger.debug("Write test failed - may indicate permission restrictions")
        
        packet_logger.debug("Root permissions verification completed")

    def test_root_service_protocol_packets(self, root_service, packet_logger, packet_capture):
        """Test root service protocol packet handling."""
        packet_logger.debug("Testing root service protocol packets")
        
        # Enable packet capture
        packet_capture.enable()
        
        try:
            # Perform root status check with packet capture
            status = root_service.check_root_status()
            packet_logger.debug(f"Root status with packet capture: {status}")
            
            # Check captured packets
            sent_packets = packet_capture.sent_packets
            received_packets = packet_capture.received_packets
            
            packet_logger.debug(f"Root service packets: {len(sent_packets)} sent, {len(received_packets)} received")
            
            # Should have some packet activity
            assert len(sent_packets) > 0 or len(received_packets) > 0
            
            # Look for shell-related packets (used for status checking)
            shell_packets = []
            for packet in sent_packets + received_packets:
                if hasattr(packet, 'payload') and packet.payload:
                    payload_str = packet.payload.decode('utf-8', errors='ignore')
                    if 'shell' in payload_str or 'id' in payload_str:
                        shell_packets.append(packet)
            
            packet_logger.debug(f"Shell-related packets found: {len(shell_packets)}")
            
        finally:
            packet_capture.disable()
        
        packet_logger.debug("Root service protocol packets test successful")

    def test_root_service_with_connection_recovery(self, adb_connection, packet_logger):
        """Test root service behavior with connection recovery scenarios."""
        packet_logger.debug("Testing root service with connection recovery")
        
        root_service = AdbRootService(adb_connection)
        
        # Test initial status
        initial_status = root_service.check_root_status()
        packet_logger.debug(f"Initial status before recovery test: {initial_status}")
        
        # Simulate connection recovery by creating new service instances
        for i in range(3):
            new_root_service = AdbRootService(adb_connection)
            status = new_root_service.check_root_status()
            packet_logger.debug(f"Recovery test {i+1} status: {status}")
            
            # Status should be consistent or None
            if initial_status is not None and status is not None:
                # Allow for some variation due to potential state changes
                packet_logger.debug(f"Status consistency check: initial={initial_status}, current={status}")
        
        packet_logger.debug("Root service connection recovery test successful")

    def test_root_service_concurrent_operations(self, adb_connection, packet_logger):
        """Test concurrent root service operations."""
        packet_logger.debug("Testing concurrent root service operations")
        
        import threading
        
        results = []
        
        def check_root_status_task(task_id):
            try:
                root_service = AdbRootService(adb_connection)
                status = root_service.check_root_status()
                results.append((task_id, 'status', status))
                packet_logger.debug(f"Concurrent task {task_id}: status={status}")
            except Exception as e:
                results.append((task_id, 'error', str(e)))
                packet_logger.debug(f"Concurrent task {task_id}: error={e}")
        
        # Start multiple concurrent status checks
        threads = []
        for i in range(3):
            thread = threading.Thread(target=check_root_status_task, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=10.0)
        
        packet_logger.debug(f"Concurrent operations completed: {len(results)} results")
        
        # Verify results
        assert len(results) == 3
        
        # Check for consistency in results
        status_results = [r for r in results if r[1] == 'status']
        error_results = [r for r in results if r[1] == 'error']
        
        packet_logger.debug(f"Status results: {len(status_results)}, Error results: {len(error_results)}")
        
        # Most operations should succeed
        assert len(status_results) >= 1
        
        packet_logger.debug("Concurrent root service operations successful")
