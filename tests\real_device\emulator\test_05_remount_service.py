"""
Integration tests for ADB Remount Service

Tests system remount functionality including remount operations and status checking
using the Android emulator.
"""

import pytest
import time
import logging
from typing import Optional, Dict, Any

from pyadb import AdbTcpConnection, AdbRemountService, AdbShellService, AdbRootService
from pyadb.const import FEATURE_REMOUNT_SHELL

logger = logging.getLogger(__name__)


@pytest.mark.real_device
@pytest.mark.integration
class TestRemountService:
    """Test ADB Remount Service functionality."""

    def test_remount_service_initialization(self, adb_connection, packet_logger):
        """Test remount service initialization."""
        packet_logger.debug("Testing remount service initialization")
        
        remount_service = AdbRemountService(adb_connection)
        
        # Verify basic initialization
        assert remount_service.connection == adb_connection
        assert isinstance(remount_service.features, list)
        
        # Check feature support detection
        packet_logger.debug(f"Remount service features: {remount_service.features}")
        packet_logger.debug(f"Remount shell support: {remount_service.supports_remount_shell}")
        
        assert isinstance(remount_service.supports_remount_shell, bool)
        
        if FEATURE_REMOUNT_SHELL in remount_service.features:
            assert remount_service.supports_remount_shell is True
        else:
            assert remount_service.supports_remount_shell is False
        
        packet_logger.debug("Remount service initialization successful")

    def test_system_writable_status_check(self, remount_service, packet_logger):
        """Test checking if system is writable."""
        packet_logger.debug("Testing system writable status check")
        
        # Check initial system writable status
        is_writable = remount_service.is_system_writable()
        
        packet_logger.debug(f"System writable status: {is_writable}")
        
        # Status should be a boolean or None
        assert is_writable is None or isinstance(is_writable, bool)
        
        if is_writable is not None:
            if is_writable:
                packet_logger.debug("System is currently writable")
            else:
                packet_logger.debug("System is currently read-only")
        else:
            packet_logger.debug("Unable to determine system writable status")
        
        packet_logger.debug("System writable status check successful")

    def test_mount_status_via_shell(self, shell_service, packet_logger):
        """Test mount status detection via shell commands."""
        packet_logger.debug("Testing mount status via shell commands")
        
        # Check mount information
        result = shell_service.execute_command('mount | grep system')
        packet_logger.debug(f"Mount command result: exit_code={result.exit_code}")
        
        if result.exit_code == 0:
            mount_info = result.stdout.decode().strip()
            packet_logger.debug(f"System mount info: {mount_info}")
            
            # Look for read-only or read-write indicators
            if 'ro,' in mount_info or 'ro ' in mount_info:
                packet_logger.debug("System appears to be mounted read-only")
            elif 'rw,' in mount_info or 'rw ' in mount_info:
                packet_logger.debug("System appears to be mounted read-write")
            else:
                packet_logger.debug("Mount status unclear from output")
        else:
            packet_logger.debug("Could not get mount information")
        
        # Also check /proc/mounts
        result = shell_service.execute_command('cat /proc/mounts | grep system')
        if result.exit_code == 0:
            proc_mounts = result.stdout.decode().strip()
            packet_logger.debug(f"Proc mounts system info: {proc_mounts[:200]}...")  # First 200 chars
        
        packet_logger.debug("Mount status via shell commands successful")

    def test_remount_system_attempt(self, remount_service, packet_logger):
        """Test system remount functionality."""
        packet_logger.debug("Testing system remount attempt")
        
        # Check initial status
        initial_writable = remount_service.is_system_writable()
        packet_logger.debug(f"Initial system writable status: {initial_writable}")
        
        # Attempt to remount system
        packet_logger.debug("Attempting to remount system as read-write...")
        remount_result = remount_service.remount_system()
        
        packet_logger.debug(f"Remount result: {remount_result}")
        
        # Result should be boolean
        assert isinstance(remount_result, bool)
        
        if remount_result:
            packet_logger.debug("Remount command sent successfully")
            
            # Give some time for remount to take effect
            time.sleep(1.0)
            
            # Check status after remount
            post_remount_writable = remount_service.is_system_writable()
            packet_logger.debug(f"Post-remount writable status: {post_remount_writable}")
            
        else:
            packet_logger.debug("Remount failed - this may be expected on some devices")
        
        packet_logger.debug("System remount attempt completed")

    def test_remount_with_root_check(self, adb_connection, packet_logger):
        """Test remount functionality with root status checking."""
        packet_logger.debug("Testing remount with root check")
        
        # Check root status first
        root_service = AdbRootService(adb_connection)
        is_root = root_service.check_root_status()
        
        packet_logger.debug(f"Root status: {is_root}")
        
        # Create remount service
        remount_service = AdbRemountService(adb_connection)
        
        # Attempt remount
        remount_result = remount_service.remount_system()
        packet_logger.debug(f"Remount result with root status {is_root}: {remount_result}")
        
        if is_root is True:
            # If we have root, remount might succeed
            packet_logger.debug("Have root access - remount may succeed")
        elif is_root is False:
            # Without root, remount will likely fail
            packet_logger.debug("No root access - remount likely to fail")
        else:
            # Unknown root status
            packet_logger.debug("Unknown root status - remount result uncertain")
        
        packet_logger.debug("Remount with root check completed")

    def test_remount_error_handling(self, remount_service, packet_logger):
        """Test remount service error handling."""
        packet_logger.debug("Testing remount service error handling")
        
        # Test multiple rapid remount attempts
        results = []
        for i in range(3):
            try:
                result = remount_service.remount_system()
                results.append(result)
                packet_logger.debug(f"Rapid remount attempt {i+1}: {result}")
                time.sleep(0.5)
            except Exception as e:
                packet_logger.debug(f"Exception during rapid remount {i+1}: {e}")
                results.append(False)
        
        # Should have some results
        assert len(results) == 3
        
        # Test status checking error handling
        status_results = []
        for i in range(3):
            try:
                status = remount_service.is_system_writable()
                status_results.append(status)
                packet_logger.debug(f"Rapid status check {i+1}: {status}")
                time.sleep(0.1)
            except Exception as e:
                packet_logger.debug(f"Exception during rapid status check {i+1}: {e}")
                status_results.append(None)
        
        assert len(status_results) == 3
        
        packet_logger.debug("Remount service error handling successful")

    def test_file_system_write_test(self, shell_service, packet_logger):
        """Test actual file system write capabilities."""
        packet_logger.debug("Testing file system write capabilities")
        
        # Test write to /data/local/tmp (should usually work)
        test_file = "/data/local/tmp/remount_write_test.txt"
        test_content = "Remount write test content"
        
        write_result = shell_service.execute_command(f'echo "{test_content}" > {test_file}')
        packet_logger.debug(f"Write to /data/local/tmp result: exit_code={write_result.exit_code}")
        
        if write_result.exit_code == 0:
            # Verify file was written
            read_result = shell_service.execute_command(f'cat {test_file}')
            if read_result.exit_code == 0:
                read_content = read_result.stdout.decode().strip()
                packet_logger.debug(f"Read content: {read_content}")
                assert test_content in read_content
            
            # Clean up
            shell_service.execute_command(f'rm -f {test_file}')
            packet_logger.debug("Write test to /data/local/tmp successful")
        else:
            packet_logger.debug("Write test to /data/local/tmp failed")
        
        # Test write to system location (will likely fail without remount)
        system_test_file = "/system/remount_test.txt"
        system_write_result = shell_service.execute_command(f'echo "test" > {system_test_file}')
        packet_logger.debug(f"Write to /system result: exit_code={system_write_result.exit_code}")
        
        if system_write_result.exit_code == 0:
            packet_logger.debug("Write to /system succeeded - system is writable")
            # Clean up if successful
            shell_service.execute_command(f'rm -f {system_test_file}')
        else:
            packet_logger.debug("Write to /system failed - system is read-only or no permissions")
        
        packet_logger.debug("File system write test completed")

    def test_remount_service_protocol_packets(self, remount_service, packet_logger, packet_capture):
        """Test remount service protocol packet handling."""
        packet_logger.debug("Testing remount service protocol packets")
        
        # Enable packet capture
        packet_capture.enable()
        
        try:
            # Perform remount operation with packet capture
            result = remount_service.remount_system()
            packet_logger.debug(f"Remount with packet capture: {result}")
            
            # Check captured packets
            sent_packets = packet_capture.sent_packets
            received_packets = packet_capture.received_packets
            
            packet_logger.debug(f"Remount service packets: {len(sent_packets)} sent, {len(received_packets)} received")
            
            # Should have some packet activity
            assert len(sent_packets) > 0 or len(received_packets) > 0
            
            # Look for remount-related packets
            remount_packets = []
            for packet in sent_packets + received_packets:
                if hasattr(packet, 'payload') and packet.payload:
                    payload_str = packet.payload.decode('utf-8', errors='ignore')
                    if 'remount' in payload_str.lower():
                        remount_packets.append(packet)
            
            packet_logger.debug(f"Remount-related packets found: {len(remount_packets)}")
            
        finally:
            packet_capture.disable()
        
        packet_logger.debug("Remount service protocol packets test successful")

    def test_remount_feature_detection(self, remount_service, packet_logger):
        """Test remount feature detection and method selection."""
        packet_logger.debug("Testing remount feature detection")
        
        supports_remount_shell = remount_service.supports_remount_shell
        features = remount_service.features
        
        packet_logger.debug(f"Remount shell support: {supports_remount_shell}")
        packet_logger.debug(f"Available features: {features}")
        
        # Test feature consistency
        if FEATURE_REMOUNT_SHELL in features:
            assert supports_remount_shell is True
            packet_logger.debug("Feature detection consistent: remount_shell supported")
        else:
            assert supports_remount_shell is False
            packet_logger.debug("Feature detection consistent: remount_shell not supported")
        
        # Test that the service can handle both methods
        if supports_remount_shell:
            packet_logger.debug("Testing shell-based remount method")
        else:
            packet_logger.debug("Testing service-based remount method")
        
        # Attempt remount to test method selection
        result = remount_service.remount_system()
        packet_logger.debug(f"Method selection test result: {result}")
        
        packet_logger.debug("Remount feature detection successful")

    def test_concurrent_remount_operations(self, adb_connection, packet_logger):
        """Test concurrent remount operations."""
        packet_logger.debug("Testing concurrent remount operations")
        
        import threading
        
        results = []
        
        def remount_task(task_id):
            try:
                remount_service = AdbRemountService(adb_connection)
                
                # Check status
                status = remount_service.is_system_writable()
                results.append((task_id, 'status', status))
                
                # Attempt remount
                remount_result = remount_service.remount_system()
                results.append((task_id, 'remount', remount_result))
                
                packet_logger.debug(f"Concurrent task {task_id}: status={status}, remount={remount_result}")
                
            except Exception as e:
                results.append((task_id, 'error', str(e)))
                packet_logger.debug(f"Concurrent task {task_id}: error={e}")
        
        # Start multiple concurrent operations
        threads = []
        for i in range(2):  # Use fewer threads for remount operations
            thread = threading.Thread(target=remount_task, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=15.0)
        
        packet_logger.debug(f"Concurrent remount operations completed: {len(results)} results")
        
        # Verify results
        assert len(results) >= 2  # Should have at least some results
        
        # Separate different types of results
        status_results = [r for r in results if r[1] == 'status']
        remount_results = [r for r in results if r[1] == 'remount']
        error_results = [r for r in results if r[1] == 'error']
        
        packet_logger.debug(f"Results breakdown: {len(status_results)} status, {len(remount_results)} remount, {len(error_results)} errors")
        
        # Most operations should complete (success or failure, but not error)
        assert len(error_results) <= len(threads)  # No more errors than threads
        
        packet_logger.debug("Concurrent remount operations successful")
