"""
Integration tests for ADB Port Forward Service

Tests port forwarding functionality including TCP forwarding, reverse forwarding,
and cleanup using the Android emulator.
"""

import pytest
import time
import socket
import threading
import logging
from typing import List, Dict, Any, Optional

from pyadb import AdbTcpConnection, AdbPortForwardService, AdbShellService
from pyadb.port_forward import ForwardResult, ForwardType

logger = logging.getLogger(__name__)


@pytest.mark.real_device
@pytest.mark.integration
class TestPortForwardService:
    """Test ADB Port Forward Service functionality."""

    def test_port_forward_service_initialization(self, adb_connection, packet_logger):
        """Test port forward service initialization."""
        packet_logger.debug("Testing port forward service initialization")
        
        port_forward_service = AdbPortForwardService(adb_connection)
        
        # Verify basic initialization
        assert port_forward_service.connection == adb_connection
        assert hasattr(port_forward_service, 'forwarder')
        
        packet_logger.debug("Port forward service initialization successful")

    def test_list_forwards_empty(self, port_forward_service, packet_logger):
        """Test listing forwards when none are active."""
        packet_logger.debug("Testing list forwards (empty)")
        
        # Clean up any existing forwards first
        port_forward_service.remove_all_forwards()
        
        # List forwards
        forwards = port_forward_service.list_forwards()
        
        packet_logger.debug(f"Empty forwards list: {forwards}")
        
        assert isinstance(forwards, list)
        # Should be empty or contain only unrelated forwards
        
        packet_logger.debug("List forwards (empty) successful")

    def test_tcp_port_forward_basic(self, port_forward_service, packet_logger):
        """Test basic TCP port forwarding."""
        packet_logger.debug("Testing basic TCP port forwarding")
        
        # Use high port numbers to avoid conflicts
        local_port = 8080
        remote_port = 8080
        
        try:
            # Set up port forward
            result = port_forward_service.forward_tcp_port(local_port, remote_port)
            
            packet_logger.debug(f"Port forward result: {result}")
            
            assert isinstance(result, ForwardResult)
            
            if result.success:
                packet_logger.debug(f"Port forward successful: {local_port} -> {remote_port}")
                assert result.local_port == local_port
                
                # List forwards to verify
                forwards = port_forward_service.list_forwards()
                packet_logger.debug(f"Active forwards: {forwards}")
                
                # Should have at least one forward
                assert len(forwards) >= 1
                
                # Look for our forward
                our_forward = None
                for forward in forwards:
                    if str(local_port) in str(forward):
                        our_forward = forward
                        break
                
                if our_forward:
                    packet_logger.debug(f"Found our forward: {our_forward}")
                
            else:
                packet_logger.debug(f"Port forward failed: {result.error}")
                # This might be expected if port is already in use
        
        finally:
            # Clean up
            port_forward_service.remove_forward(local_port)
        
        packet_logger.debug("Basic TCP port forwarding test completed")

    def test_tcp_port_forward_with_connection_test(self, port_forward_service, packet_logger):
        """Test TCP port forwarding with actual connection test."""
        packet_logger.debug("Testing TCP port forwarding with connection test")
        
        local_port = 8081
        remote_port = 22  # SSH port (commonly available)
        
        try:
            # Set up port forward
            result = port_forward_service.forward_tcp_port(local_port, remote_port)
            
            packet_logger.debug(f"Port forward result: {result}")
            
            if result.success:
                # Give some time for forward to be established
                time.sleep(1.0)
                
                # Test connection to forwarded port
                try:
                    with socket.create_connection(('localhost', local_port), timeout=5) as sock:
                        packet_logger.debug(f"Successfully connected to forwarded port {local_port}")
                        # Just test connection, don't send data
                        
                except socket.error as e:
                    packet_logger.debug(f"Connection to forwarded port failed: {e}")
                    # This might be expected if the remote service isn't running
                
            else:
                packet_logger.debug(f"Port forward setup failed: {result.error}")
        
        finally:
            # Clean up
            port_forward_service.remove_forward(local_port)
        
        packet_logger.debug("TCP port forwarding with connection test completed")

    def test_reverse_tcp_port_forward(self, port_forward_service, packet_logger):
        """Test reverse TCP port forwarding."""
        packet_logger.debug("Testing reverse TCP port forwarding")
        
        device_port = 8082
        host_port = 8082
        
        try:
            # Set up reverse port forward
            result = port_forward_service.reverse_tcp_port(device_port, host_port)
            
            packet_logger.debug(f"Reverse port forward result: {result}")
            
            assert isinstance(result, ForwardResult)
            
            if result.success:
                packet_logger.debug(f"Reverse port forward successful: device:{device_port} -> host:{host_port}")
                
                # List forwards to verify
                forwards = port_forward_service.list_forwards()
                packet_logger.debug(f"Active forwards after reverse: {forwards}")
                
            else:
                packet_logger.debug(f"Reverse port forward failed: {result.error}")
        
        finally:
            # Clean up
            port_forward_service.remove_reverse(device_port)
        
        packet_logger.debug("Reverse TCP port forwarding test completed")

    def test_multiple_port_forwards(self, port_forward_service, packet_logger):
        """Test multiple simultaneous port forwards."""
        packet_logger.debug("Testing multiple port forwards")
        
        forwards_to_create = [
            (8083, 80),   # HTTP
            (8084, 443),  # HTTPS
            (8085, 22),   # SSH
        ]
        
        created_forwards = []
        
        try:
            # Create multiple forwards
            for local_port, remote_port in forwards_to_create:
                result = port_forward_service.forward_tcp_port(local_port, remote_port)
                packet_logger.debug(f"Forward {local_port}->{remote_port}: {result.success}")
                
                if result.success:
                    created_forwards.append(local_port)
            
            packet_logger.debug(f"Created {len(created_forwards)} forwards")
            
            # List all forwards
            forwards = port_forward_service.list_forwards()
            packet_logger.debug(f"Total active forwards: {len(forwards)}")
            
            # Should have at least the ones we created
            assert len(forwards) >= len(created_forwards)
            
        finally:
            # Clean up all created forwards
            for local_port in created_forwards:
                port_forward_service.remove_forward(local_port)
        
        packet_logger.debug("Multiple port forwards test completed")

    def test_port_forward_error_handling(self, port_forward_service, packet_logger):
        """Test port forward error handling."""
        packet_logger.debug("Testing port forward error handling")
        
        # Test forwarding to invalid port
        result = port_forward_service.forward_tcp_port(8086, 99999)  # Invalid remote port
        packet_logger.debug(f"Invalid remote port result: {result}")
        
        # Should fail gracefully
        assert isinstance(result, ForwardResult)
        if not result.success:
            packet_logger.debug(f"Expected failure for invalid port: {result.error}")
        
        # Test forwarding with port already in use
        local_port = 8087
        
        # Create first forward
        result1 = port_forward_service.forward_tcp_port(local_port, 80)
        
        try:
            if result1.success:
                # Try to create second forward with same local port
                result2 = port_forward_service.forward_tcp_port(local_port, 443)
                packet_logger.debug(f"Duplicate local port result: {result2}")
                
                # This should fail or handle gracefully
                if not result2.success:
                    packet_logger.debug(f"Expected failure for duplicate port: {result2.error}")
        
        finally:
            # Clean up
            port_forward_service.remove_forward(local_port)
        
        # Test removing non-existent forward
        remove_result = port_forward_service.remove_forward(9999)
        packet_logger.debug(f"Remove non-existent forward result: {remove_result}")
        
        # Should handle gracefully (return False or True, but not crash)
        assert isinstance(remove_result, bool)
        
        packet_logger.debug("Port forward error handling test completed")

    def test_port_forward_cleanup(self, port_forward_service, packet_logger):
        """Test port forward cleanup functionality."""
        packet_logger.debug("Testing port forward cleanup")
        
        # Create several forwards
        test_ports = [8088, 8089, 8090]
        created_forwards = []
        
        for local_port in test_ports:
            result = port_forward_service.forward_tcp_port(local_port, 80)
            if result.success:
                created_forwards.append(local_port)
        
        packet_logger.debug(f"Created {len(created_forwards)} test forwards")
        
        # List forwards before cleanup
        forwards_before = port_forward_service.list_forwards()
        packet_logger.debug(f"Forwards before cleanup: {len(forwards_before)}")
        
        # Clean up all forwards
        removed_count = port_forward_service.remove_all_forwards()
        packet_logger.debug(f"Removed {removed_count} forwards")
        
        # List forwards after cleanup
        forwards_after = port_forward_service.list_forwards()
        packet_logger.debug(f"Forwards after cleanup: {len(forwards_after)}")
        
        # Should have fewer forwards after cleanup
        assert len(forwards_after) <= len(forwards_before)
        
        packet_logger.debug("Port forward cleanup test completed")

    def test_port_forward_with_shell_verification(self, port_forward_service, shell_service, packet_logger):
        """Test port forwarding with shell command verification."""
        packet_logger.debug("Testing port forwarding with shell verification")
        
        local_port = 8091
        remote_port = 80
        
        try:
            # Set up port forward
            result = port_forward_service.forward_tcp_port(local_port, remote_port)
            
            if result.success:
                packet_logger.debug(f"Port forward established: {local_port} -> {remote_port}")
                
                # Use shell to check if port is listening on device
                netstat_result = shell_service.execute_command('netstat -ln | grep :80')
                packet_logger.debug(f"Netstat result: exit_code={netstat_result.exit_code}")
                
                if netstat_result.exit_code == 0:
                    netstat_output = netstat_result.stdout.decode()
                    packet_logger.debug(f"Netstat output: {netstat_output[:200]}...")
                    
                    # Look for port 80 listening
                    if ':80 ' in netstat_output:
                        packet_logger.debug("Port 80 is listening on device")
                    else:
                        packet_logger.debug("Port 80 not found in netstat output")
                else:
                    packet_logger.debug("Netstat command failed or not available")
                
                # Also try ss command (more modern)
                ss_result = shell_service.execute_command('ss -ln | grep :80')
                if ss_result.exit_code == 0:
                    ss_output = ss_result.stdout.decode()
                    packet_logger.debug(f"SS output: {ss_output[:200]}...")
            
            else:
                packet_logger.debug(f"Port forward failed: {result.error}")
        
        finally:
            # Clean up
            port_forward_service.remove_forward(local_port)
        
        packet_logger.debug("Port forwarding with shell verification completed")

    def test_port_forward_protocol_packets(self, port_forward_service, packet_logger, packet_capture):
        """Test port forward protocol packet handling."""
        packet_logger.debug("Testing port forward protocol packets")
        
        # Enable packet capture
        packet_capture.enable()
        
        try:
            # Perform port forward operation with packet capture
            result = port_forward_service.forward_tcp_port(8092, 80)
            packet_logger.debug(f"Port forward with packet capture: {result}")
            
            # List forwards to generate more packets
            forwards = port_forward_service.list_forwards()
            packet_logger.debug(f"Listed forwards: {len(forwards)}")
            
            # Check captured packets
            sent_packets = packet_capture.sent_packets
            received_packets = packet_capture.received_packets
            
            packet_logger.debug(f"Port forward packets: {len(sent_packets)} sent, {len(received_packets)} received")
            
            # Should have some packet activity
            assert len(sent_packets) > 0 or len(received_packets) > 0
            
            # Look for forward-related packets
            forward_packets = []
            for packet in sent_packets + received_packets:
                if hasattr(packet, 'payload') and packet.payload:
                    payload_str = packet.payload.decode('utf-8', errors='ignore')
                    if 'forward' in payload_str.lower() or 'tcp:' in payload_str:
                        forward_packets.append(packet)
            
            packet_logger.debug(f"Forward-related packets found: {len(forward_packets)}")
            
            # Clean up
            if result.success:
                port_forward_service.remove_forward(8092)
            
        finally:
            packet_capture.disable()
        
        packet_logger.debug("Port forward protocol packets test successful")

    def test_concurrent_port_forward_operations(self, adb_connection, packet_logger):
        """Test concurrent port forward operations."""
        packet_logger.debug("Testing concurrent port forward operations")
        
        import threading
        
        results = []
        
        def port_forward_task(task_id):
            try:
                service = AdbPortForwardService(adb_connection)
                local_port = 8100 + task_id
                remote_port = 80
                
                # Create forward
                result = service.forward_tcp_port(local_port, remote_port)
                results.append((task_id, 'forward', result.success))
                
                if result.success:
                    # List forwards
                    forwards = service.list_forwards()
                    results.append((task_id, 'list', len(forwards)))
                    
                    # Remove forward
                    remove_result = service.remove_forward(local_port)
                    results.append((task_id, 'remove', remove_result))
                
                packet_logger.debug(f"Concurrent task {task_id}: forward={result.success}")
                
            except Exception as e:
                results.append((task_id, 'error', str(e)))
                packet_logger.debug(f"Concurrent task {task_id}: error={e}")
        
        # Start multiple concurrent operations
        threads = []
        for i in range(3):
            thread = threading.Thread(target=port_forward_task, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=20.0)
        
        packet_logger.debug(f"Concurrent port forward operations completed: {len(results)} results")
        
        # Verify results
        assert len(results) >= 3  # Should have at least some results
        
        # Separate different types of results
        forward_results = [r for r in results if r[1] == 'forward']
        list_results = [r for r in results if r[1] == 'list']
        remove_results = [r for r in results if r[1] == 'remove']
        error_results = [r for r in results if r[1] == 'error']
        
        packet_logger.debug(f"Results: {len(forward_results)} forwards, {len(list_results)} lists, {len(remove_results)} removes, {len(error_results)} errors")
        
        # Most operations should complete without errors
        assert len(error_results) <= len(threads)
        
        packet_logger.debug("Concurrent port forward operations successful")
