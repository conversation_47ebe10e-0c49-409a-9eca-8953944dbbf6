"""
Integration tests for ADB Core Protocol

Tests core ADB protocol functionality including packet handling, authentication,
connection management, and low-level protocol operations using the Android emulator.
"""

import pytest
import time
import struct
import logging
from typing import List, Dict, Any, Optional

from pyadb import AdbTcpConnection
from pyadb.core import (
    AdbMessage, AdbPacket, calculate_checksum,
    create_message, create_packet, create_connect_packet,
    create_auth_packet, create_open_packet, create_ready_packet,
    create_write_packet, create_close_packet
)
from pyadb.const import (
    A_SYNC, A_CNXN, A_OPEN, A_OKAY, A_CLSE, A_WRTE, A_AUTH, A_STLS,
    A_VERSION, A_VERSION_MIN, A_VERSION_SKIP_CHECKSUM,
    MAX_PAYLOAD, MAX_PAYLOAD_V1, MESSAGE_SIZE,
    command_to_string, ConnectionState, TransportType
)
from pyadb.auth import get_key_manager, sign_auth_token, get_public_key

logger = logging.getLogger(__name__)


@pytest.mark.real_device
@pytest.mark.integration
class TestCoreProtocol:
    """Test ADB Core Protocol functionality."""

    def test_packet_creation_and_serialization(self, packet_logger):
        """Test ADB packet creation and serialization."""
        packet_logger.debug("Testing packet creation and serialization")
        
        # Test basic packet creation
        test_payload = b"Hello ADB Protocol"
        packet = create_packet(A_CNXN, A_VERSION, MAX_PAYLOAD, test_payload)
        
        packet_logger.debug(f"Created packet: {packet}")
        
        assert isinstance(packet, AdbPacket)
        assert isinstance(packet.message, AdbMessage)
        assert packet.message.command == A_CNXN
        assert packet.message.arg0 == A_VERSION
        assert packet.message.arg1 == MAX_PAYLOAD
        assert packet.payload == test_payload
        
        # Test packet serialization
        packet_bytes = packet.to_bytes()
        packet_logger.debug(f"Serialized packet: {len(packet_bytes)} bytes")
        
        assert len(packet_bytes) >= MESSAGE_SIZE
        assert len(packet_bytes) == MESSAGE_SIZE + len(test_payload)
        
        # Test packet deserialization
        reconstructed = AdbPacket.from_bytes(packet_bytes)
        
        assert reconstructed.message.command == packet.message.command
        assert reconstructed.message.arg0 == packet.message.arg0
        assert reconstructed.message.arg1 == packet.message.arg1
        assert reconstructed.payload == packet.payload
        
        packet_logger.debug("Packet creation and serialization successful")

    def test_checksum_calculation(self, packet_logger):
        """Test ADB checksum calculation."""
        packet_logger.debug("Testing checksum calculation")
        
        # Test with various payloads
        test_cases = [
            b"",  # Empty payload
            b"Hello",  # Short payload
            b"A" * 1000,  # Long payload
            bytes(range(256)),  # Binary payload
        ]
        
        for i, payload in enumerate(test_cases):
            checksum = calculate_checksum(payload)
            packet_logger.debug(f"Test case {i+1}: payload_len={len(payload)}, checksum=0x{checksum:08x}")
            
            assert isinstance(checksum, int)
            assert 0 <= checksum <= 0xffffffff
            
            # Checksum should be consistent
            checksum2 = calculate_checksum(payload)
            assert checksum == checksum2
        
        packet_logger.debug("Checksum calculation successful")

    def test_message_magic_field(self, packet_logger):
        """Test ADB message magic field calculation."""
        packet_logger.debug("Testing message magic field")
        
        commands = [A_SYNC, A_CNXN, A_OPEN, A_OKAY, A_CLSE, A_WRTE, A_AUTH]
        
        for command in commands:
            message = create_message(command, 0, 0, b"test")
            expected_magic = command ^ 0xffffffff
            
            packet_logger.debug(f"Command 0x{command:08x}: magic=0x{message.magic:08x}, expected=0x{expected_magic:08x}")
            
            assert message.magic == expected_magic
            assert message.command == command
        
        packet_logger.debug("Message magic field test successful")

    def test_protocol_version_handling(self, adb_connection, packet_logger):
        """Test ADB protocol version handling."""
        packet_logger.debug("Testing protocol version handling")
        
        # Test connection with different protocol versions
        versions_to_test = [A_VERSION_MIN, A_VERSION]
        
        for version in versions_to_test:
            packet_logger.debug(f"Testing protocol version: 0x{version:08x}")
            
            # Create connect packet with specific version
            connect_packet = create_connect_packet(version, MAX_PAYLOAD, f"host::version_test_{version}")
            
            # Send packet
            send_result = adb_connection.send_packet(connect_packet)
            assert send_result is True
            
            # Receive response
            response = adb_connection.receive_packet()
            assert response is not None
            
            packet_logger.debug(f"Version {version:08x} response: command=0x{response.message.command:08x}")
            
            # Response should be CNXN or AUTH
            assert response.message.command in [A_CNXN, A_AUTH]
            
            if response.message.command == A_CNXN:
                # Check version in response
                response_version = response.message.arg0
                packet_logger.debug(f"Device protocol version: 0x{response_version:08x}")
                assert response_version >= A_VERSION_MIN
        
        packet_logger.debug("Protocol version handling successful")

    def test_payload_size_limits(self, adb_connection, packet_logger):
        """Test ADB payload size limits."""
        packet_logger.debug("Testing payload size limits")
        
        # Test with various payload sizes
        test_sizes = [0, 1, 100, 1000, 4096]  # Don't test MAX_PAYLOAD as it might be too large for CNXN
        
        for size in test_sizes:
            packet_logger.debug(f"Testing payload size: {size} bytes")
            
            # Create payload of specified size
            payload = b"X" * size
            identity = f"host::size_test_{size}"
            
            # Create connect packet
            connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, identity)
            
            # Send packet
            send_result = adb_connection.send_packet(connect_packet)
            assert send_result is True
            
            # Receive response
            response = adb_connection.receive_packet()
            assert response is not None
            
            packet_logger.debug(f"Size {size} response: command=0x{response.message.command:08x}")
            
            # Should get valid response
            assert response.message.command in [A_CNXN, A_AUTH]
        
        packet_logger.debug("Payload size limits test successful")

    def test_packet_integrity_verification(self, adb_connection, packet_logger):
        """Test packet integrity verification."""
        packet_logger.debug("Testing packet integrity verification")
        
        # Send multiple packets and verify integrity
        for i in range(5):
            test_payload = f"integrity_test_{i}".encode()
            connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, f"host::integrity_{i}")
            
            # Verify packet before sending
            original_bytes = connect_packet.to_bytes()
            reconstructed = AdbPacket.from_bytes(original_bytes)
            
            assert reconstructed.message.command == connect_packet.message.command
            assert reconstructed.payload == connect_packet.payload
            
            # Send and receive
            send_result = adb_connection.send_packet(connect_packet)
            assert send_result is True
            
            response = adb_connection.receive_packet()
            assert response is not None
            
            # Verify response integrity
            response_bytes = response.to_bytes()
            response_reconstructed = AdbPacket.from_bytes(response_bytes)
            
            assert response_reconstructed.message.command == response.message.command
            assert response_reconstructed.message.magic == response.message.magic
            
            packet_logger.debug(f"Integrity test {i+1}: passed")
        
        packet_logger.debug("Packet integrity verification successful")

    def test_connection_state_management(self, packet_logger):
        """Test connection state management."""
        packet_logger.debug("Testing connection state management")
        
        # Test with fresh connection
        connection = AdbTcpConnection('localhost', 5555, timeout=10.0)
        
        try:
            # Initial state
            assert connection.is_connected() is False
            
            # Connect
            result = connection.connect()
            assert result is True
            assert connection.is_connected() is True
            
            # Test connection info
            peer_addr = connection.get_peer_address()
            assert peer_addr is not None
            assert peer_addr[0] in ['127.0.0.1', '::1']
            assert peer_addr[1] == 5555
            
            packet_logger.debug(f"Connection established to {peer_addr}")
            
            # Test packet exchange
            connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, "host::state_test")
            send_result = connection.send_packet(connect_packet)
            assert send_result is True
            
            response = connection.receive_packet()
            assert response is not None
            
            packet_logger.debug("Connection state management successful")
            
        finally:
            connection.disconnect()
            assert connection.is_connected() is False
        
        packet_logger.debug("Connection state management test completed")

    def test_error_handling_and_recovery(self, adb_connection, packet_logger):
        """Test error handling and recovery mechanisms."""
        packet_logger.debug("Testing error handling and recovery")
        
        # Test with malformed packet data (this should be handled gracefully)
        try:
            # Create a packet with unusual but valid parameters
            unusual_packet = create_packet(A_CNXN, 0, 0, b"")  # Minimal CNXN packet
            
            send_result = adb_connection.send_packet(unusual_packet)
            packet_logger.debug(f"Unusual packet send result: {send_result}")
            
            if send_result:
                response = adb_connection.receive_packet()
                if response:
                    packet_logger.debug(f"Unusual packet response: 0x{response.message.command:08x}")
                else:
                    packet_logger.debug("No response to unusual packet")
            
        except Exception as e:
            packet_logger.debug(f"Expected exception for unusual packet: {e}")
        
        # Test connection recovery after error
        try:
            # Send normal packet to verify connection still works
            normal_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, "host::recovery_test")
            send_result = adb_connection.send_packet(normal_packet)
            assert send_result is True
            
            response = adb_connection.receive_packet()
            assert response is not None
            
            packet_logger.debug("Connection recovery after error successful")
            
        except Exception as e:
            packet_logger.debug(f"Connection recovery failed: {e}")
        
        packet_logger.debug("Error handling and recovery test completed")

    def test_command_string_conversion(self, packet_logger):
        """Test command to string conversion utilities."""
        packet_logger.debug("Testing command string conversion")
        
        # Test known commands
        known_commands = {
            A_SYNC: "SYNC",
            A_CNXN: "CNXN", 
            A_OPEN: "OPEN",
            A_OKAY: "OKAY",
            A_CLSE: "CLSE",
            A_WRTE: "WRTE",
            A_AUTH: "AUTH",
        }
        
        for command, expected_str in known_commands.items():
            command_str = command_to_string(command)
            packet_logger.debug(f"Command 0x{command:08x} -> '{command_str}' (expected: '{expected_str}')")
            
            # Should return a string
            assert isinstance(command_str, str)
            assert len(command_str) > 0
        
        # Test unknown command
        unknown_command = 0x12345678
        unknown_str = command_to_string(unknown_command)
        packet_logger.debug(f"Unknown command 0x{unknown_command:08x} -> '{unknown_str}'")
        
        assert isinstance(unknown_str, str)
        
        packet_logger.debug("Command string conversion successful")

    def test_protocol_packet_timing(self, adb_connection, packet_logger):
        """Test protocol packet timing and performance."""
        packet_logger.debug("Testing protocol packet timing")
        
        # Measure packet round-trip times
        round_trip_times = []
        
        for i in range(10):
            start_time = time.time()
            
            # Send packet
            connect_packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, f"host::timing_test_{i}")
            send_result = adb_connection.send_packet(connect_packet)
            assert send_result is True
            
            # Receive response
            response = adb_connection.receive_packet()
            assert response is not None
            
            end_time = time.time()
            round_trip_time = end_time - start_time
            round_trip_times.append(round_trip_time)
            
            packet_logger.debug(f"Round trip {i+1}: {round_trip_time:.3f}s")
        
        # Calculate statistics
        avg_time = sum(round_trip_times) / len(round_trip_times)
        min_time = min(round_trip_times)
        max_time = max(round_trip_times)
        
        packet_logger.debug(f"Timing stats: avg={avg_time:.3f}s, min={min_time:.3f}s, max={max_time:.3f}s")
        
        # Reasonable performance expectations
        assert avg_time < 1.0  # Average should be under 1 second
        assert max_time < 5.0  # No single operation should take more than 5 seconds
        
        packet_logger.debug("Protocol packet timing test successful")

    def test_concurrent_protocol_operations(self, packet_logger):
        """Test concurrent protocol operations."""
        packet_logger.debug("Testing concurrent protocol operations")
        
        import threading
        
        results = []
        
        def protocol_task(task_id):
            try:
                connection = AdbTcpConnection('localhost', 5555, timeout=10.0)
                
                # Connect
                connect_result = connection.connect()
                results.append((task_id, 'connect', connect_result))
                
                if connect_result:
                    # Send packet
                    packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, f"host::concurrent_{task_id}")
                    send_result = connection.send_packet(packet)
                    results.append((task_id, 'send', send_result))
                    
                    if send_result:
                        # Receive response
                        response = connection.receive_packet()
                        results.append((task_id, 'receive', response is not None))
                    
                    # Disconnect
                    connection.disconnect()
                    results.append((task_id, 'disconnect', True))
                
                packet_logger.debug(f"Concurrent task {task_id}: completed")
                
            except Exception as e:
                results.append((task_id, 'error', str(e)))
                packet_logger.debug(f"Concurrent task {task_id}: error={e}")
        
        # Start multiple concurrent operations
        threads = []
        for i in range(3):
            thread = threading.Thread(target=protocol_task, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=30.0)
        
        packet_logger.debug(f"Concurrent protocol operations completed: {len(results)} results")
        
        # Verify results
        assert len(results) >= 3  # Should have at least some results
        
        # Count successful operations
        successful_ops = [r for r in results if r[1] != 'error' and r[2] is True]
        error_ops = [r for r in results if r[1] == 'error']
        
        packet_logger.debug(f"Successful operations: {len(successful_ops)}, Errors: {len(error_ops)}")
        
        # Most operations should succeed
        assert len(successful_ops) >= len(threads)  # At least one success per thread
        
        packet_logger.debug("Concurrent protocol operations successful")
