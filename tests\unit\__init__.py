"""
Unit Tests for PyADB Components

This package contains unit tests for individual PyADB components.
Each module tests a specific component in isolation with mocked dependencies.

Test Modules:
- test_core.py: Core protocol implementation tests
- test_const.py: Constants and enums tests  
- test_conn_wifi.py: TCP/IP connection tests
- test_conn_usb.py: USB connection tests
- test_shell.py: Shell service tests
- test_auth.py: Authentication tests
- test_pull.py: File pull service tests
- test_push.py: File push service tests
- test_root.py: Root service tests
- test_remount.py: Remount service tests
- test_port_forward.py: Port forwarding tests
"""

import logging

# Configure unit test logging
logging.getLogger('pyadb').setLevel(logging.DEBUG)
