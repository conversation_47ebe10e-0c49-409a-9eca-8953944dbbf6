"""
Unit tests for pyadb.adb_socket module

Tests the ADB socket implementation including socket creation,
connection handling, data transfer, and socket management.
"""

import pytest
import threading
import time
import queue
from unittest.mock import Mock, MagicMock, patch
import logging

from pyadb.adb_socket import AdbSocket, AdbSocketError, AdbSocketManager
from pyadb.core import AdbPacket, AdbMessage, create_packet
from pyadb.const import A_OPEN, A_OKAY, A_CLSE, A_WRTE

logger = logging.getLogger(__name__)


class MockConnection:
    """Mock ADB connection for testing."""
    
    def __init__(self):
        self.sent_packets = []
        self.received_packets = queue.Queue()
        self.connected = True
        
    def send_packet(self, packet: AdbPacket) -> bool:
        """Mock send packet."""
        if not self.connected:
            return False
        self.sent_packets.append(packet)
        return True
        
    def receive_packet(self) -> AdbPacket:
        """Mock receive packet."""
        try:
            return self.received_packets.get_nowait()
        except queue.Empty:
            return None
            
    def add_received_packet(self, packet: AdbPacket):
        """Add packet to received queue."""
        self.received_packets.put(packet)


class TestAdbSocket:
    """Test AdbSocket class functionality."""

    def test_socket_creation(self, packet_logger):
        """Test ADB socket creation."""
        packet_logger.debug("Testing ADB socket creation")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        
        assert socket.connection == connection
        assert socket.destination == "shell:"
        assert socket.local_id > 0
        assert socket.remote_id is None
        assert not socket.connected
        assert not socket.closed
        
        packet_logger.debug(f"Created socket with local_id: {socket.local_id}")

    def test_socket_connect_success(self, packet_logger):
        """Test successful socket connection."""
        packet_logger.debug("Testing successful socket connection")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        
        # Simulate connection response in a separate thread
        def simulate_response():
            time.sleep(0.1)  # Small delay to simulate network
            response_packet = create_packet(A_OKAY, 0x200, socket.local_id)
            socket.handle_packet(response_packet)
        
        response_thread = threading.Thread(target=simulate_response)
        response_thread.start()
        
        # Attempt connection
        result = socket.connect(timeout=1.0)
        response_thread.join()
        
        assert result is True
        assert socket.connected
        assert socket.remote_id == 0x200
        
        # Verify OPEN packet was sent
        assert len(connection.sent_packets) == 1
        sent_packet = connection.sent_packets[0]
        assert sent_packet.message.command == A_OPEN
        assert sent_packet.message.arg0 == socket.local_id
        assert sent_packet.payload == b"shell:\x00"
        
        packet_logger.debug(f"Socket connected successfully with remote_id: {socket.remote_id}")

    def test_socket_connect_timeout(self, packet_logger):
        """Test socket connection timeout."""
        packet_logger.debug("Testing socket connection timeout")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        
        # Attempt connection without response
        result = socket.connect(timeout=0.1)
        
        assert result is False
        assert not socket.connected
        assert socket.remote_id is None
        
        packet_logger.debug("Socket connection timed out as expected")

    def test_socket_send_data(self, packet_logger):
        """Test sending data through socket."""
        packet_logger.debug("Testing socket data sending")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        
        # Simulate connected state
        socket.connected = True
        socket.remote_id = 0x200
        
        test_data = b"echo hello world"
        bytes_sent = socket.send(test_data)
        
        assert bytes_sent == len(test_data)
        
        # Verify WRTE packet was sent
        assert len(connection.sent_packets) == 1
        sent_packet = connection.sent_packets[0]
        assert sent_packet.message.command == A_WRTE
        assert sent_packet.message.arg0 == socket.local_id
        assert sent_packet.message.arg1 == socket.remote_id
        assert sent_packet.payload == test_data
        
        packet_logger.debug(f"Sent {bytes_sent} bytes successfully")

    def test_socket_send_not_connected(self, packet_logger):
        """Test sending data when socket not connected."""
        packet_logger.debug("Testing send when not connected")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        
        with pytest.raises(AdbSocketError, match="Socket not connected"):
            socket.send(b"test data")
        
        packet_logger.debug("Send correctly failed when not connected")

    def test_socket_send_closed(self, packet_logger):
        """Test sending data when socket is closed."""
        packet_logger.debug("Testing send when socket closed")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        socket.connected = True
        socket.remote_id = 0x200
        socket.closed = True
        
        with pytest.raises(AdbSocketError, match="Socket is closed"):
            socket.send(b"test data")
        
        packet_logger.debug("Send correctly failed when socket closed")

    def test_socket_receive_data(self, packet_logger):
        """Test receiving data through socket."""
        packet_logger.debug("Testing socket data receiving")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        socket.connected = True
        socket.remote_id = 0x200
        
        # Simulate received data
        test_data = b"hello from device"
        data_packet = create_packet(A_WRTE, socket.remote_id, socket.local_id, test_data)
        socket.handle_packet(data_packet)
        
        # Receive the data
        received_data = socket.recv(1024)
        assert received_data == test_data
        
        # Verify ACK was sent
        assert len(connection.sent_packets) == 1
        ack_packet = connection.sent_packets[0]
        assert ack_packet.message.command == A_OKAY
        assert ack_packet.message.arg0 == socket.local_id
        assert ack_packet.message.arg1 == socket.remote_id
        
        packet_logger.debug(f"Received {len(received_data)} bytes successfully")

    def test_socket_receive_timeout(self, packet_logger):
        """Test receiving data with timeout."""
        packet_logger.debug("Testing socket receive timeout")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        socket.connected = True
        
        # Attempt to receive with no data available
        received_data = socket.recv(1024, timeout=0.1)
        assert received_data == b""
        
        packet_logger.debug("Receive timed out as expected")

    def test_socket_receive_partial_data(self, packet_logger):
        """Test receiving partial data."""
        packet_logger.debug("Testing partial data receive")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        socket.connected = True
        socket.remote_id = 0x200
        
        # Send data in chunks
        chunk1 = b"hello "
        chunk2 = b"world"
        
        data_packet1 = create_packet(A_WRTE, socket.remote_id, socket.local_id, chunk1)
        data_packet2 = create_packet(A_WRTE, socket.remote_id, socket.local_id, chunk2)
        
        socket.handle_packet(data_packet1)
        socket.handle_packet(data_packet2)
        
        # Receive partial data
        partial_data = socket.recv(7)  # Request 7 bytes
        assert partial_data == b"hello "  # First chunk is 6 bytes

        # Receive remaining data
        remaining_data = socket.recv(1024)
        assert remaining_data == b"world"  # Second chunk is 5 bytes
        
        packet_logger.debug("Partial data receive worked correctly")

    def test_socket_close(self, packet_logger):
        """Test socket close operation."""
        packet_logger.debug("Testing socket close")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        socket.connected = True
        socket.remote_id = 0x200
        
        socket.close()
        
        assert socket.closed
        assert not socket.connected
        
        # Verify CLSE packet was sent
        assert len(connection.sent_packets) == 1
        close_packet = connection.sent_packets[0]
        assert close_packet.message.command == A_CLSE
        assert close_packet.message.arg0 == socket.local_id
        assert close_packet.message.arg1 == socket.remote_id
        
        packet_logger.debug("Socket closed successfully")

    def test_socket_handle_close_packet(self, packet_logger):
        """Test handling remote close packet."""
        packet_logger.debug("Testing remote close packet handling")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "shell:")
        socket.connected = True
        socket.remote_id = 0x200
        
        # Simulate remote close
        close_packet = create_packet(A_CLSE, socket.remote_id, socket.local_id)
        socket.handle_packet(close_packet)
        
        assert socket.closed
        assert not socket.connected
        
        packet_logger.debug("Remote close handled correctly")

    def test_socket_compatibility_methods(self, packet_logger):
        """Test socket compatibility methods."""
        packet_logger.debug("Testing socket compatibility methods")
        
        connection = MockConnection()
        socket = AdbSocket(connection, "tcp:8080")
        socket.remote_id = 0x200
        
        # Test getsockname
        sockname = socket.getsockname()
        assert sockname == ("adb", socket.local_id)
        
        # Test getpeername
        peername = socket.getpeername()
        assert peername == ("tcp:8080", 0x200)
        
        # Test settimeout (should not raise)
        socket.settimeout(5.0)
        socket.settimeout(None)
        
        packet_logger.debug("Compatibility methods work correctly")

    def test_socket_context_manager(self, packet_logger):
        """Test socket as context manager."""
        packet_logger.debug("Testing socket context manager")
        
        connection = MockConnection()
        
        with AdbSocket(connection, "shell:") as socket:
            assert not socket.closed
            socket.connected = True
            socket.remote_id = 0x200
        
        # Should be closed after context exit
        assert socket.closed
        
        packet_logger.debug("Context manager worked correctly")


class TestAdbSocketManager:
    """Test AdbSocketManager class functionality."""

    def test_manager_creation(self, packet_logger):
        """Test socket manager creation."""
        packet_logger.debug("Testing socket manager creation")
        
        connection = MockConnection()
        manager = AdbSocketManager(connection)
        
        assert manager.connection == connection
        assert len(manager.sockets) == 0
        assert manager._running
        
        packet_logger.debug("Socket manager created successfully")

    def test_manager_create_socket(self, packet_logger):
        """Test creating socket through manager."""
        packet_logger.debug("Testing socket creation through manager")
        
        connection = MockConnection()
        manager = AdbSocketManager(connection)
        
        socket = manager.create_socket("shell:")
        
        assert socket.destination == "shell:"
        assert socket.local_id in manager.sockets
        assert manager.sockets[socket.local_id] == socket
        
        packet_logger.debug(f"Created socket {socket.local_id} through manager")

    def test_manager_remove_socket(self, packet_logger):
        """Test removing socket from manager."""
        packet_logger.debug("Testing socket removal from manager")
        
        connection = MockConnection()
        manager = AdbSocketManager(connection)
        
        socket = manager.create_socket("shell:")
        local_id = socket.local_id
        
        manager.remove_socket(socket)
        
        assert local_id not in manager.sockets
        
        packet_logger.debug(f"Removed socket {local_id} from manager")

    def test_manager_packet_routing(self, packet_logger):
        """Test packet routing to correct socket."""
        packet_logger.debug("Testing packet routing")
        
        connection = MockConnection()
        manager = AdbSocketManager(connection)
        
        # Create two sockets
        socket1 = manager.create_socket("shell:")
        socket2 = manager.create_socket("tcp:8080")
        
        # Mock handle_packet methods
        socket1.handle_packet = Mock()
        socket2.handle_packet = Mock()
        
        # Create test packet for socket1
        test_packet = create_packet(A_OKAY, 0x200, socket1.local_id)
        
        # Simulate packet routing
        manager._route_packet(test_packet)
        
        # Verify correct socket received the packet
        socket1.handle_packet.assert_called_once_with(test_packet)
        socket2.handle_packet.assert_not_called()
        
        packet_logger.debug("Packet routed correctly")

    def test_manager_unknown_socket_packet(self, packet_logger):
        """Test handling packet for unknown socket."""
        packet_logger.debug("Testing unknown socket packet handling")
        
        connection = MockConnection()
        manager = AdbSocketManager(connection)
        
        # Create packet for non-existent socket
        test_packet = create_packet(A_OKAY, 0x200, 0x999)  # Unknown local_id
        
        # Should not raise exception
        manager._route_packet(test_packet)
        
        packet_logger.debug("Unknown socket packet handled gracefully")

    def test_manager_shutdown(self, packet_logger):
        """Test socket manager shutdown."""
        packet_logger.debug("Testing socket manager shutdown")
        
        connection = MockConnection()
        manager = AdbSocketManager(connection)
        
        # Create some sockets
        socket1 = manager.create_socket("shell:")
        socket2 = manager.create_socket("tcp:8080")
        
        manager.shutdown()
        
        assert not manager._running
        assert len(manager.sockets) == 0
        
        packet_logger.debug("Socket manager shut down successfully")


class TestAdbSocketError:
    """Test AdbSocketError exception."""

    def test_socket_error_creation(self, packet_logger):
        """Test AdbSocketError creation."""
        packet_logger.debug("Testing AdbSocketError creation")
        
        error = AdbSocketError("Test error message")
        assert str(error) == "Test error message"
        assert isinstance(error, Exception)
        
        packet_logger.debug("AdbSocketError created correctly")

    def test_socket_error_inheritance(self, packet_logger):
        """Test AdbSocketError inheritance."""
        packet_logger.debug("Testing AdbSocketError inheritance")
        
        error = AdbSocketError("Test error")
        
        assert isinstance(error, Exception)
        assert isinstance(error, AdbSocketError)
        
        # Should be catchable as Exception
        try:
            raise error
        except Exception as e:
            assert isinstance(e, AdbSocketError)
        
        packet_logger.debug("AdbSocketError inheritance works correctly")


class TestSocketIntegration:
    """Integration tests for socket functionality."""

    def test_socket_full_lifecycle(self, packet_logger):
        """Test complete socket lifecycle."""
        packet_logger.debug("Testing complete socket lifecycle")
        
        connection = MockConnection()
        manager = AdbSocketManager(connection)
        
        # Create and connect socket
        socket = manager.create_socket("shell:")
        
        # Simulate connection
        def simulate_connection():
            time.sleep(0.05)
            response = create_packet(A_OKAY, 0x200, socket.local_id)
            socket.handle_packet(response)
        
        conn_thread = threading.Thread(target=simulate_connection)
        conn_thread.start()
        
        assert socket.connect(timeout=1.0)
        conn_thread.join()
        
        # Send data
        test_data = b"ls -la"
        bytes_sent = socket.send(test_data)
        assert bytes_sent == len(test_data)
        
        # Simulate response
        response_data = b"total 0\ndrwxr-xr-x 2 <USER> <GROUP> 60 Jan 1 00:00 .\n"
        data_packet = create_packet(A_WRTE, socket.remote_id, socket.local_id, response_data)
        socket.handle_packet(data_packet)
        
        # Receive data
        received = socket.recv(1024)
        assert received == response_data
        
        # Close socket
        socket.close()
        assert socket.closed
        
        # Clean up
        manager.shutdown()
        
        packet_logger.debug("Complete socket lifecycle test passed")

    def test_multiple_sockets_concurrent(self, packet_logger):
        """Test multiple sockets operating concurrently."""
        packet_logger.debug("Testing multiple concurrent sockets")
        
        connection = MockConnection()
        manager = AdbSocketManager(connection)
        
        # Create multiple sockets
        sockets = []
        for i in range(3):
            socket = manager.create_socket(f"tcp:{8080 + i}")
            sockets.append(socket)
        
        # Connect all sockets
        for i, socket in enumerate(sockets):
            def simulate_connection(sock, remote_id):
                time.sleep(0.05)
                response = create_packet(A_OKAY, remote_id, sock.local_id)
                sock.handle_packet(response)
            
            thread = threading.Thread(target=simulate_connection, args=(socket, 0x200 + i))
            thread.start()
            
            assert socket.connect(timeout=1.0)
            thread.join()
        
        # Send data through all sockets
        for i, socket in enumerate(sockets):
            test_data = f"data for socket {i}".encode()
            bytes_sent = socket.send(test_data)
            assert bytes_sent == len(test_data)
        
        # Close all sockets
        for socket in sockets:
            socket.close()
            assert socket.closed
        
        manager.shutdown()
        
        packet_logger.debug("Multiple concurrent sockets test passed")
