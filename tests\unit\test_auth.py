"""
Unit tests for pyadb.auth module

Tests the ADB authentication system including RSA key management,
token signing, key generation, and authentication flow.
"""

import pytest
import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, MagicMock, patch, mock_open
import logging

# Test with and without cryptography
try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.backends import default_backend
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

from pyadb.auth import (
    AdbAuthError, AdbKeyManager, get_key_manager, sign_auth_token,
    get_public_key
)
from pyadb.const import (
    TOKEN_SIZE, ADB_AUTH_TOKEN, ADB_AUTH_SIGNATURE, ADB_AUTH_RSAPUBLICKEY
)

logger = logging.getLogger(__name__)


class TestAdbAuthError:
    """Test AdbAuthError exception."""

    def test_auth_error_creation(self, packet_logger):
        """Test AdbAuthError creation."""
        packet_logger.debug("Testing AdbAuthError creation")
        
        error = AdbAuthError("Authentication failed")
        assert str(error) == "Authentication failed"
        assert isinstance(error, Exception)
        
        packet_logger.debug("AdbAuthError created correctly")

    def test_auth_error_inheritance(self, packet_logger):
        """Test AdbAuthError inheritance."""
        packet_logger.debug("Testing AdbAuthError inheritance")
        
        error = AdbAuthError("Test error")
        
        assert isinstance(error, Exception)
        assert isinstance(error, AdbAuthError)
        
        # Should be catchable as Exception
        try:
            raise error
        except Exception as e:
            assert isinstance(e, AdbAuthError)
        
        packet_logger.debug("AdbAuthError inheritance works correctly")


@pytest.mark.skipif(not CRYPTO_AVAILABLE, reason="cryptography library not available")
class TestAdbKeyManager:
    """Test AdbKeyManager class functionality."""

    def test_key_manager_initialization(self, packet_logger):
        """Test key manager initialization."""
        packet_logger.debug("Testing key manager initialization")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            assert key_manager.key_dir == Path(temp_dir)
            assert key_manager.private_key_path == Path(temp_dir) / "adbkey"
            assert key_manager.public_key_path == Path(temp_dir) / "adbkey.pub"
            assert key_manager._private_key is None
            assert key_manager._public_key_str is None
        
        packet_logger.debug("Key manager initialized correctly")

    def test_key_manager_default_directory(self, packet_logger):
        """Test key manager with default directory."""
        packet_logger.debug("Testing key manager default directory")
        
        with patch('os.path.expanduser') as mock_expanduser:
            mock_expanduser.return_value = "/home/<USER>/.android"

            with patch('pathlib.Path.mkdir'):
                key_manager = AdbKeyManager()

                # On Windows, paths use backslashes, so normalize for comparison
                expected_path = "/home/<USER>/.android"
                actual_path = str(key_manager.key_dir).replace('\\', '/')
                assert actual_path == expected_path
                mock_expanduser.assert_called_once_with("~/.android")
        
        packet_logger.debug("Default directory handling works correctly")

    def test_key_manager_without_crypto(self, packet_logger):
        """Test key manager without cryptography library."""
        packet_logger.debug("Testing key manager without cryptography")
        
        with patch('pyadb.auth.CRYPTO_AVAILABLE', False):
            with pytest.raises(AdbAuthError, match="cryptography library not available"):
                AdbKeyManager()
        
        packet_logger.debug("Crypto unavailable handled correctly")

    def test_generate_rsa_key(self, packet_logger):
        """Test RSA key generation."""
        packet_logger.debug("Testing RSA key generation")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            private_key = key_manager._generate_rsa_key()
            
            assert isinstance(private_key, rsa.RSAPrivateKey)
            assert private_key.key_size == 2048
            
            # Test public key extraction
            public_key = private_key.public_key()
            assert isinstance(public_key, rsa.RSAPublicKey)
        
        packet_logger.debug("RSA key generation successful")

    def test_android_pubkey_encode(self, packet_logger):
        """Test Android public key encoding."""
        packet_logger.debug("Testing Android public key encoding")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Generate test key
            private_key = key_manager._generate_rsa_key()
            
            # Encode in Android format
            android_key = key_manager._android_pubkey_encode(private_key)
            
            # Should be exactly 524 bytes (ANDROID_PUBKEY_ENCODED_SIZE)
            assert len(android_key) == 524
            assert isinstance(android_key, bytes)
        
        packet_logger.debug("Android public key encoding successful")

    def test_calculate_public_key_string(self, packet_logger):
        """Test public key string calculation."""
        packet_logger.debug("Testing public key string calculation")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Generate test key
            private_key = key_manager._generate_rsa_key()
            
            # Calculate public key string
            with patch.dict(os.environ, {'USERNAME': 'testuser', 'COMPUTERNAME': 'testhost'}):
                pub_key_str = key_manager._calculate_public_key_string(private_key)
            
            # Should be base64 encoded with identifier
            assert isinstance(pub_key_str, str)
            assert pub_key_str.endswith(" testuser@testhost")
            
            # Should be valid base64 (before the space)
            import base64
            key_part = pub_key_str.split(' ')[0]
            decoded = base64.b64decode(key_part)
            assert len(decoded) == 524  # Android key size
        
        packet_logger.debug("Public key string calculation successful")

    def test_save_and_load_keys(self, packet_logger):
        """Test saving and loading keys."""
        packet_logger.debug("Testing key save and load")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Generate and save keys
            private_key = key_manager._generate_rsa_key()
            public_key_str = key_manager._calculate_public_key_string(private_key)
            key_manager._save_keys(private_key, public_key_str)
            
            # Verify files exist
            assert key_manager.private_key_path.exists()
            assert key_manager.public_key_path.exists()
            
            # Load keys back
            loaded_private_key = key_manager._load_private_key()
            loaded_public_key_str = key_manager._load_public_key_string()
            
            assert loaded_private_key is not None
            assert loaded_public_key_str == public_key_str
            
            # Verify keys are equivalent
            original_public = private_key.public_key()
            loaded_public = loaded_private_key.public_key()
            
            # Compare public key numbers
            orig_numbers = original_public.public_numbers()
            loaded_numbers = loaded_public.public_numbers()
            assert orig_numbers.n == loaded_numbers.n
            assert orig_numbers.e == loaded_numbers.e
        
        packet_logger.debug("Key save and load successful")

    def test_get_private_key_existing(self, packet_logger):
        """Test getting existing private key."""
        packet_logger.debug("Testing get existing private key")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Generate and save keys first
            original_key = key_manager._generate_rsa_key()
            public_key_str = key_manager._calculate_public_key_string(original_key)
            key_manager._save_keys(original_key, public_key_str)
            
            # Get private key (should load from disk)
            loaded_key = key_manager.get_private_key()
            
            assert loaded_key is not None
            assert isinstance(loaded_key, rsa.RSAPrivateKey)
            
            # Should be cached
            same_key = key_manager.get_private_key()
            assert same_key is loaded_key
        
        packet_logger.debug("Get existing private key successful")

    def test_get_private_key_generate_new(self, packet_logger):
        """Test generating new private key when none exists."""
        packet_logger.debug("Testing generate new private key")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Get private key (should generate new)
            private_key = key_manager.get_private_key()
            
            assert private_key is not None
            assert isinstance(private_key, rsa.RSAPrivateKey)
            
            # Files should be created
            assert key_manager.private_key_path.exists()
            assert key_manager.public_key_path.exists()
        
        packet_logger.debug("Generate new private key successful")

    def test_get_public_key_string(self, packet_logger):
        """Test getting public key string."""
        packet_logger.debug("Testing get public key string")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Get public key string (should generate keys)
            public_key_str = key_manager.get_public_key_string()
            
            assert public_key_str is not None
            assert isinstance(public_key_str, str)
            assert "@" in public_key_str  # Should have identifier
            
            # Should be cached
            same_str = key_manager.get_public_key_string()
            assert same_str == public_key_str
        
        packet_logger.debug("Get public key string successful")

    def test_sign_token(self, packet_logger):
        """Test token signing."""
        packet_logger.debug("Testing token signing")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Generate test token
            test_token = b"test_token_12345678"  # 20 bytes
            
            # Sign token
            signature = key_manager.sign_token(test_token)
            
            assert signature is not None
            assert isinstance(signature, bytes)
            assert len(signature) == 256  # RSA-2048 signature size
            
            # Verify signature
            private_key = key_manager.get_private_key()
            public_key = private_key.public_key()
            
            # Should not raise exception if valid
            public_key.verify(
                signature,
                test_token,
                padding.PKCS1v15(),
                hashes.SHA1()
            )
        
        packet_logger.debug("Token signing successful")

    def test_generate_token(self, packet_logger):
        """Test token generation."""
        packet_logger.debug("Testing token generation")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Generate tokens
            token1 = key_manager.generate_token()
            token2 = key_manager.generate_token()
            
            assert len(token1) == TOKEN_SIZE
            assert len(token2) == TOKEN_SIZE
            assert token1 != token2  # Should be random
            assert isinstance(token1, bytes)
            assert isinstance(token2, bytes)
        
        packet_logger.debug("Token generation successful")

    def test_verify_signature(self, packet_logger):
        """Test signature verification."""
        packet_logger.debug("Testing signature verification")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Generate token and sign it
            token = key_manager.generate_token()
            signature = key_manager.sign_token(token)
            public_key_str = key_manager.get_public_key_string()
            
            # Verify signature
            result = key_manager.verify_signature(token, signature, public_key_str)
            assert result is True
            
            # Test with wrong token
            wrong_token = key_manager.generate_token()
            result = key_manager.verify_signature(wrong_token, signature, public_key_str)
            assert result is False
            
            # Test with wrong signature
            wrong_signature = key_manager.sign_token(wrong_token)
            result = key_manager.verify_signature(token, wrong_signature, public_key_str)
            assert result is False
        
        packet_logger.debug("Signature verification successful")

    def test_key_file_permissions(self, packet_logger):
        """Test that private key files have correct permissions."""
        packet_logger.debug("Testing key file permissions")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            key_manager = AdbKeyManager(temp_dir)
            
            # Generate keys (should create files)
            key_manager.get_private_key()
            
            # Check private key permissions (should be 600)
            private_key_stat = key_manager.private_key_path.stat()
            permissions = oct(private_key_stat.st_mode)[-3:]
            
            # On Unix systems, should be 600 (owner read/write only)
            if os.name != 'nt':  # Skip on Windows
                assert permissions == '600'
        
        packet_logger.debug("Key file permissions correct")


@pytest.mark.skipif(not CRYPTO_AVAILABLE, reason="cryptography library not available")
class TestGlobalFunctions:
    """Test global authentication functions."""

    def test_get_key_manager_singleton(self, packet_logger):
        """Test global key manager singleton."""
        packet_logger.debug("Testing global key manager singleton")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # First call should create instance
            manager1 = get_key_manager(temp_dir)
            assert isinstance(manager1, AdbKeyManager)
            
            # Second call should return same instance
            manager2 = get_key_manager()
            assert manager1 is manager2
            
            # Reset global state for other tests
            import pyadb.auth
            pyadb.auth._key_manager = None
        
        packet_logger.debug("Global key manager singleton works correctly")

    def test_sign_auth_token_function(self, packet_logger):
        """Test sign_auth_token convenience function."""
        packet_logger.debug("Testing sign_auth_token function")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            test_token = b"test_token_12345678"
            
            signature = sign_auth_token(test_token, temp_dir)
            
            assert isinstance(signature, bytes)
            assert len(signature) == 256  # RSA-2048 signature
            
            # Reset global state
            import pyadb.auth
            pyadb.auth._key_manager = None
        
        packet_logger.debug("sign_auth_token function works correctly")

    def test_get_public_key_function(self, packet_logger):
        """Test get_public_key convenience function."""
        packet_logger.debug("Testing get_public_key function")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            public_key_str = get_public_key(temp_dir)
            
            assert isinstance(public_key_str, str)
            assert "@" in public_key_str  # Should have identifier
            
            # Reset global state
            import pyadb.auth
            pyadb.auth._key_manager = None
        
        packet_logger.debug("get_public_key function works correctly")


class TestAuthConstants:
    """Test authentication constants."""

    def test_auth_constants_values(self, packet_logger):
        """Test authentication constant values."""
        packet_logger.debug("Testing authentication constants")
        
        # These must match ADB source code values
        assert ADB_AUTH_TOKEN == 1
        assert ADB_AUTH_SIGNATURE == 2
        assert ADB_AUTH_RSAPUBLICKEY == 3
        
        # Token size should be 20 bytes (SHA-1 hash size)
        assert TOKEN_SIZE == 20
        
        packet_logger.debug("Authentication constants are correct")


@pytest.mark.skipif(not CRYPTO_AVAILABLE, reason="cryptography library not available")
class TestAuthIntegration:
    """Integration tests for authentication flow."""

    def test_full_auth_flow(self, packet_logger):
        """Test complete authentication flow."""
        packet_logger.debug("Testing complete authentication flow")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create key manager
            key_manager = AdbKeyManager(temp_dir)
            
            # Generate token (simulating device)
            auth_token = key_manager.generate_token()
            
            # Sign token (simulating client)
            signature = key_manager.sign_token(auth_token)
            
            # Get public key for verification
            public_key_str = key_manager.get_public_key_string()
            
            # Verify signature (simulating device)
            is_valid = key_manager.verify_signature(auth_token, signature, public_key_str)
            
            assert is_valid is True
        
        packet_logger.debug("Complete authentication flow successful")

    def test_cross_key_manager_verification(self, packet_logger):
        """Test verification between different key manager instances."""
        packet_logger.debug("Testing cross key manager verification")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create first key manager and generate keys
            key_manager1 = AdbKeyManager(temp_dir)
            token = key_manager1.generate_token()
            signature = key_manager1.sign_token(token)
            public_key_str = key_manager1.get_public_key_string()
            
            # Create second key manager (should load same keys)
            key_manager2 = AdbKeyManager(temp_dir)
            
            # Verify with second key manager
            is_valid = key_manager2.verify_signature(token, signature, public_key_str)
            assert is_valid is True
        
        packet_logger.debug("Cross key manager verification successful")

    def test_invalid_signature_rejection(self, packet_logger):
        """Test that invalid signatures are rejected."""
        packet_logger.debug("Testing invalid signature rejection")
        
        with tempfile.TemporaryDirectory() as temp_dir1, \
             tempfile.TemporaryDirectory() as temp_dir2:
            
            # Create two different key managers
            key_manager1 = AdbKeyManager(temp_dir1)
            key_manager2 = AdbKeyManager(temp_dir2)
            
            # Generate token and sign with first key
            token = key_manager1.generate_token()
            signature = key_manager1.sign_token(token)
            
            # Try to verify with second key's public key
            public_key_str2 = key_manager2.get_public_key_string()
            is_valid = key_manager1.verify_signature(token, signature, public_key_str2)
            
            assert is_valid is False
        
        packet_logger.debug("Invalid signature rejection successful")
