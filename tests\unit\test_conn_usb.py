"""
Unit tests for pyadb.conn_usb module

Tests the USB connection implementation including device discovery,
bulk transfer communication, and USB protocol handling.
"""

import pytest
from unittest.mock import Mock, MagicMock, patch, call
import logging

from pyadb.conn_usb import AdbUsbConnection, UsbDevice
from pyadb.core import <PERSON>b<PERSON>acket, AdbMessage, create_packet
from pyadb.const import A_CNXN, A_OKAY, ADB_CLASS, ADB_SUBCLASS, ADB_PROTOCOL, TransportType

logger = logging.getLogger(__name__)


class TestUsbDevice:
    """Test UsbDevice class functionality."""

    def test_usb_device_creation(self, packet_logger):
        """Test USB device creation."""
        packet_logger.debug("Testing USB device creation")
        
        # Mock PyUSB device
        mock_device = Mock()
        mock_device.serial_number = "ABC123DEF456"
        mock_device.product = "Test Android Device"
        mock_device.manufacturer = "Test Manufacturer"
        mock_device.bus = 1
        mock_device.address = 5
        
        usb_device = UsbDevice(
            device=mock_device,
            interface_number=0,
            endpoint_in=0x81,
            endpoint_out=0x02
        )
        
        assert usb_device.device == mock_device
        assert usb_device.interface_number == 0
        assert usb_device.endpoint_in == 0x81
        assert usb_device.endpoint_out == 0x02
        assert usb_device.serial_number == "ABC123DEF456"
        assert "Test Android Device" in usb_device.product_name
        
        packet_logger.debug(f"Created USB device: {usb_device}")

    def test_usb_device_fallback_serial(self, packet_logger):
        """Test USB device with fallback serial number."""
        packet_logger.debug("Testing USB device fallback serial")
        
        # Mock device without serial number
        mock_device = Mock()
        mock_device.serial_number = None
        mock_device.bus = 2
        mock_device.address = 10
        
        # Mock USB error when accessing serial_number
        type(mock_device).serial_number = Mock(side_effect=Exception("No serial"))
        
        usb_device = UsbDevice(
            device=mock_device,
            interface_number=0,
            endpoint_in=0x81,
            endpoint_out=0x02
        )
        
        # Should use bus:address format as fallback
        assert usb_device.serial_number == "usb:002:010"
        
        packet_logger.debug(f"Fallback serial: {usb_device.serial_number}")

    def test_usb_device_string_representation(self, packet_logger):
        """Test USB device string representation."""
        packet_logger.debug("Testing USB device string representation")
        
        mock_device = Mock()
        mock_device.serial_number = "TEST123"
        mock_device.product = "Test Device"
        mock_device.manufacturer = "Test Mfg"
        
        usb_device = UsbDevice(mock_device, 0, 0x81, 0x02)
        
        str_repr = str(usb_device)
        assert "TEST123" in str_repr
        # The actual string representation may not include hex endpoint addresses
        # Just verify the serial number is present
        
        packet_logger.debug(f"USB device string: {str_repr}")


class TestAdbUsbConnection:
    """Test AdbUsbConnection class functionality."""

    def test_usb_connection_initialization(self, packet_logger):
        """Test USB connection initialization."""
        packet_logger.debug("Testing USB connection initialization")
        
        # Test without PyUSB available
        with patch('pyadb.conn_usb.USB_AVAILABLE', False):
            with pytest.raises(ImportError, match="PyUSB is required"):
                AdbUsbConnection()
        
        # Test with PyUSB available
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection(timeout=3000)
            
            assert conn.device is None
            assert conn.timeout == 3000
            assert conn.transport_type == TransportType.USB
            assert not conn.connected
            assert conn.usb_device is None
            
        packet_logger.debug("USB connection initialized correctly")

    def test_usb_connection_with_device(self, packet_logger):
        """Test USB connection with specific device."""
        packet_logger.debug("Testing USB connection with specific device")
        
        mock_device = Mock()
        usb_device = UsbDevice(mock_device, 0, 0x81, 0x02)
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection(device=usb_device)
            
            assert conn.device == usb_device
            
        packet_logger.debug("USB connection with device created correctly")

    @patch('pyadb.conn_usb.usb.core.find')
    def test_find_usb_devices_success(self, mock_find, packet_logger):
        """Test successful USB device discovery."""
        packet_logger.debug("Testing successful USB device discovery")
        
        # Mock USB devices
        mock_device1 = Mock()
        mock_device1.serial_number = "DEVICE1"
        mock_device1.product = "Android Device 1"
        
        mock_device2 = Mock()
        mock_device2.serial_number = "DEVICE2"
        mock_device2.product = "Android Device 2"
        
        # Mock device configurations and interfaces for iteration
        mock_interface = Mock()
        mock_interface.bInterfaceClass = ADB_CLASS
        mock_interface.bInterfaceSubClass = ADB_SUBCLASS
        mock_interface.bInterfaceProtocol = ADB_PROTOCOL
        mock_interface.bInterfaceNumber = 0

        # Mock endpoints
        mock_endpoint_in = Mock()
        mock_endpoint_in.bEndpointAddress = 0x81  # IN endpoint
        mock_endpoint_in.bmAttributes = 2  # Bulk transfer

        mock_endpoint_out = Mock()
        mock_endpoint_out.bEndpointAddress = 0x02  # OUT endpoint
        mock_endpoint_out.bmAttributes = 2  # Bulk transfer

        # Mock interface iteration (for ep in intf)
        mock_interface.__iter__ = Mock(return_value=iter([mock_endpoint_in, mock_endpoint_out]))

        # Mock configuration iteration (for intf in cfg)
        mock_config = Mock()
        mock_config.__iter__ = Mock(return_value=iter([mock_interface]))

        # Mock device iteration (for cfg in dev)
        mock_device1.__iter__ = Mock(return_value=iter([mock_config]))
        mock_device2.__iter__ = Mock(return_value=iter([mock_config]))
        
        mock_find.return_value = [mock_device1, mock_device2]
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True), \
             patch('pyadb.conn_usb.usb.util.endpoint_direction') as mock_direction, \
             patch('pyadb.conn_usb.usb.util.endpoint_type') as mock_type, \
             patch('pyadb.conn_usb.usb.util.ENDPOINT_IN', 0x80), \
             patch('pyadb.conn_usb.usb.util.ENDPOINT_OUT', 0x00), \
             patch('pyadb.conn_usb.usb.util.ENDPOINT_TYPE_BULK', 2):

            # Mock USB utility functions
            def mock_endpoint_direction_func(addr):
                return 0x80 if addr & 0x80 else 0x00

            def mock_endpoint_type_func(attr):
                return attr  # Return the attribute directly

            mock_direction.side_effect = mock_endpoint_direction_func
            mock_type.side_effect = mock_endpoint_type_func

            conn = AdbUsbConnection()
            devices = conn.find_adb_devices()
        
        assert len(devices) == 2
        assert devices[0].serial_number == "DEVICE1"
        assert devices[1].serial_number == "DEVICE2"
        
        packet_logger.debug(f"Found {len(devices)} USB devices")

    @patch('pyadb.conn_usb.usb.core.find')
    def test_find_usb_devices_no_devices(self, mock_find, packet_logger):
        """Test USB device discovery with no devices."""
        packet_logger.debug("Testing USB device discovery with no devices")
        
        mock_find.return_value = []
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            devices = conn.find_adb_devices()

        assert len(devices) == 0
        
        packet_logger.debug("No USB devices found as expected")

    @patch('pyadb.conn_usb.usb.core.find')
    def test_find_usb_devices_wrong_class(self, mock_find, packet_logger):
        """Test USB device discovery filtering non-ADB devices."""
        packet_logger.debug("Testing USB device discovery filtering")
        
        # Mock non-ADB device
        mock_device = Mock()
        mock_device.serial_number = "NON_ADB_DEVICE"
        
        mock_config = Mock()
        mock_interface = Mock()
        mock_interface.bInterfaceClass = 0x09  # Hub class, not ADB
        mock_interface.bInterfaceSubClass = 0x00
        mock_interface.bInterfaceProtocol = 0x00
        
        mock_config.interfaces.return_value = [mock_interface]
        mock_device.get_active_configuration.return_value = mock_config
        
        mock_find.return_value = [mock_device]
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            devices = conn.find_adb_devices()

        assert len(devices) == 0  # Should filter out non-ADB devices
        
        packet_logger.debug("Non-ADB devices filtered correctly")

    @patch('pyadb.conn_usb.usb.util.claim_interface')
    def test_connect_success(self, mock_claim_interface, packet_logger):
        """Test successful USB connection."""
        packet_logger.debug("Testing successful USB connection")
        
        # Mock USB device
        mock_usb_device = Mock()
        mock_usb_device.set_configuration.return_value = None
        
        usb_device = UsbDevice(mock_usb_device, 0, 0x81, 0x02)
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            # Mock find_adb_devices to return our test device
            with patch.object(conn, 'find_adb_devices', return_value=[usb_device]):
                result = conn.connect()
        
        assert result is True
        assert conn.connected is True
        assert conn.device == usb_device
        assert conn.usb_device == mock_usb_device
        
        # Verify interface was claimed
        mock_claim_interface.assert_called_once_with(mock_usb_device, 0)
        
        packet_logger.debug("USB connection successful")

    def test_connect_no_devices(self, packet_logger):
        """Test connection when no USB devices found."""
        packet_logger.debug("Testing connection with no USB devices")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            # Mock find_adb_devices to return empty list
            with patch.object(conn, 'find_adb_devices', return_value=[]):
                result = conn.connect()
        
        assert result is False
        assert conn.connected is False
        
        packet_logger.debug("No USB devices handled correctly")

    def test_connect_specific_device(self, packet_logger):
        """Test connecting to specific USB device."""
        packet_logger.debug("Testing connection to specific USB device")
        
        mock_usb_device = Mock()
        usb_device = UsbDevice(mock_usb_device, 0, 0x81, 0x02)
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True), \
             patch('pyadb.conn_usb.usb.util.claim_interface'):
            
            conn = AdbUsbConnection(device=usb_device)
            result = conn.connect(device=usb_device)
        
        assert result is True
        assert conn.connected is True
        assert conn.device == usb_device
        
        packet_logger.debug("Specific device connection successful")

    @patch('pyadb.conn_usb.usb.util.claim_interface')
    def test_connect_claim_interface_failure(self, mock_claim_interface, packet_logger):
        """Test connection with interface claim failure."""
        packet_logger.debug("Testing connection with interface claim failure")
        
        mock_usb_device = Mock()
        usb_device = UsbDevice(mock_usb_device, 0, 0x81, 0x02)
        
        # Mock interface claim failure
        mock_claim_interface.side_effect = Exception("Interface busy")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            with patch.object(conn, 'find_adb_devices', return_value=[usb_device]):
                result = conn.connect()
        
        assert result is False
        assert conn.connected is False
        
        packet_logger.debug("Interface claim failure handled correctly")

    def test_disconnect(self, packet_logger):
        """Test USB disconnection."""
        packet_logger.debug("Testing USB disconnection")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            # Mock connected state
            mock_usb_device = Mock()
            conn.usb_device = mock_usb_device
            conn.interface = 0
            conn.connected = True
            
            with patch('pyadb.conn_usb.usb.util.release_interface') as mock_release:
                conn.disconnect()
            
            assert conn.connected is False
            assert conn.usb_device is None
            mock_release.assert_called_once_with(mock_usb_device, 0)
        
        packet_logger.debug("USB disconnection successful")

    def test_send_packet_success(self, packet_logger):
        """Test successful USB packet sending."""
        packet_logger.debug("Testing successful USB packet sending")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            # Mock connected state
            mock_usb_device = Mock()
            conn.usb_device = mock_usb_device
            conn.endpoint_out = 0x02
            conn.connected = True
            
            test_packet = create_packet(A_CNXN, 0x01000001, 1024*1024, b"host::\0")
            packet_data = test_packet.to_bytes()
            
            # Mock successful USB write
            mock_usb_device.write.return_value = len(packet_data)
            
            result = conn.send_packet(test_packet)
            
            assert result is True
            mock_usb_device.write.assert_called_once_with(0x02, packet_data, conn.timeout)
        
        packet_logger.debug("USB packet sent successfully")

    def test_send_packet_not_connected(self, packet_logger):
        """Test sending packet when not connected."""
        packet_logger.debug("Testing send packet when not connected")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            test_packet = create_packet(A_CNXN, 0x01000001, 1024*1024, b"host::\0")
            
            result = conn.send_packet(test_packet)
            
            assert result is False
        
        packet_logger.debug("Send packet correctly failed when not connected")

    def test_send_packet_incomplete_write(self, packet_logger):
        """Test USB packet sending with incomplete write."""
        packet_logger.debug("Testing USB packet sending with incomplete write")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            # Mock connected state
            mock_usb_device = Mock()
            conn.usb_device = mock_usb_device
            conn.endpoint_out = 0x02
            conn.connected = True
            
            test_packet = create_packet(A_CNXN, 0x01000001, 1024*1024, b"host::\0")
            packet_data = test_packet.to_bytes()
            
            # Mock incomplete USB write
            mock_usb_device.write.return_value = len(packet_data) - 10
            
            result = conn.send_packet(test_packet)
            
            assert result is False
        
        packet_logger.debug("Incomplete USB write handled correctly")

    def test_receive_packet_success(self, packet_logger):
        """Test successful USB packet receiving."""
        packet_logger.debug("Testing successful USB packet receiving")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            # Mock connected state
            mock_usb_device = Mock()
            conn.usb_device = mock_usb_device
            conn.endpoint_in = 0x81
            conn.connected = True
            
            # Create test packet data
            test_packet = create_packet(A_OKAY, 0x100, 0x200, b"test payload")
            packet_data = test_packet.to_bytes()

            # Mock USB read returning header first, then payload
            header_data = packet_data[:24]  # MESSAGE_SIZE
            payload_data = packet_data[24:]
            mock_usb_device.read.side_effect = [header_data, payload_data]
            
            result = conn.receive_packet()
            
            assert result is not None
            assert result.message.command == A_OKAY
            assert result.message.arg0 == 0x100
            assert result.message.arg1 == 0x200
            assert result.payload == b"test payload"
        
        packet_logger.debug("USB packet received successfully")

    def test_receive_packet_not_connected(self, packet_logger):
        """Test receiving packet when not connected."""
        packet_logger.debug("Testing receive packet when not connected")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            result = conn.receive_packet()
            
            assert result is None
        
        packet_logger.debug("Receive packet correctly failed when not connected")

    def test_receive_packet_timeout(self, packet_logger):
        """Test USB packet receiving with timeout."""
        packet_logger.debug("Testing USB packet receiving with timeout")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True), \
             patch('pyadb.conn_usb.usb.core.USBTimeoutError', Exception):
            
            conn = AdbUsbConnection()
            
            # Mock connected state
            mock_usb_device = Mock()
            conn.usb_device = mock_usb_device
            conn.endpoint_in = 0x81
            conn.connected = True
            
            # Mock USB timeout
            mock_usb_device.read.side_effect = Exception("USB timeout")
            
            result = conn.receive_packet()
            
            assert result is None
        
        packet_logger.debug("USB timeout handled correctly")

    def test_is_connected(self, packet_logger):
        """Test connection status check."""
        packet_logger.debug("Testing connection status check")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            # Initially not connected
            assert conn.is_connected() is False
            
            # Mock connected state
            conn.connected = True
            conn.usb_device = Mock()
            assert conn.is_connected() is True
            
            # Disconnected state
            conn.connected = False
            assert conn.is_connected() is False
        
        packet_logger.debug("Connection status check works correctly")

    def test_get_device_info(self, packet_logger):
        """Test getting device information."""
        packet_logger.debug("Testing device information retrieval")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            conn = AdbUsbConnection()
            
            # No device connected
            info = conn.get_device_info()
            assert info is None
            
            # Mock connected device
            mock_usb_device = Mock()
            usb_device = UsbDevice(mock_usb_device, 0, 0x81, 0x02)
            usb_device.serial_number = "TEST123"
            usb_device.product_name = "Test Device"
            
            conn.device = usb_device
            conn.connected = True
            
            info = conn.get_device_info()
            assert info is not None
            assert info[0] == "TEST123"  # serial_number
            assert "Test Device" in info[1]  # product_name
        
        packet_logger.debug("Device information retrieved correctly")

    def test_context_manager(self, packet_logger):
        """Test USB connection as context manager."""
        packet_logger.debug("Testing USB connection context manager")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            mock_usb_device = Mock()
            usb_device = UsbDevice(mock_usb_device, 0, 0x81, 0x02)
            
            with patch.object(AdbUsbConnection, 'connect', return_value=True) as mock_connect, \
                 patch.object(AdbUsbConnection, 'disconnect') as mock_disconnect:
                
                with AdbUsbConnection(device=usb_device) as conn:
                    assert conn is not None
                
                mock_connect.assert_called_once()
                mock_disconnect.assert_called_once()
        
        packet_logger.debug("Context manager works correctly")

    def test_context_manager_connection_failure(self, packet_logger):
        """Test context manager with connection failure."""
        packet_logger.debug("Testing context manager with connection failure")
        
        with patch('pyadb.conn_usb.USB_AVAILABLE', True):
            with patch.object(AdbUsbConnection, 'connect', return_value=False):
                with pytest.raises(ConnectionError, match="Failed to connect to USB device"):
                    with AdbUsbConnection():
                        pass
        
        packet_logger.debug("Context manager connection failure handled correctly")
