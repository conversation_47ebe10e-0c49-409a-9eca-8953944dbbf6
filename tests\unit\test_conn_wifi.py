"""
Unit tests for pyadb.conn_wifi module

Tests the TCP/IP (WiFi) connection implementation including socket handling,
TLS support, authentication, and network communication.
"""

import pytest
import socket
import ssl
import threading
import time
from unittest.mock import Mock, MagicMock, patch, call
import logging

from pyadb.conn_wifi import AdbTcpConnection
from pyadb.core import AdbPacket, AdbMessage, create_packet
from pyadb.const import A_CNXN, A_OKAY, A_AUTH, DEFAULT_ADB_LOCAL_TRANSPORT_PORT, TransportType

logger = logging.getLogger(__name__)


class TestAdbTcpConnection:
    """Test AdbTcpConnection class functionality."""

    def test_connection_initialization(self, packet_logger):
        """Test TCP connection initialization."""
        packet_logger.debug("Testing TCP connection initialization")
        
        conn = AdbTcpConnection(
            host='*************',
            port=5555,
            timeout=15.0,
            use_tls=True,
            device_guid='test-device-guid'
        )
        
        assert conn.host == '*************'
        assert conn.port == 5555
        assert conn.timeout == 15.0
        assert conn.use_tls is True
        assert conn.device_guid == 'test-device-guid'
        assert conn.transport_type == TransportType.LOCAL
        assert not conn.connected
        assert not conn.authenticated
        assert conn.socket is None
        assert conn.ssl_socket is None
        
        packet_logger.debug("TCP connection initialized correctly")

    def test_connection_default_values(self, packet_logger):
        """Test TCP connection with default values."""
        packet_logger.debug("Testing TCP connection default values")
        
        conn = AdbTcpConnection()
        
        assert conn.host == 'localhost'
        assert conn.port == DEFAULT_ADB_LOCAL_TRANSPORT_PORT
        assert conn.timeout == 10.0
        assert conn.use_tls is False
        assert conn.device_guid is None
        
        packet_logger.debug("Default values set correctly")

    @patch('socket.socket')
    def test_connection_success_ipv4(self, mock_socket_class, packet_logger):
        """Test successful IPv4 connection."""
        packet_logger.debug("Testing successful IPv4 connection")
        
        # Mock socket instance
        mock_socket = Mock()
        mock_socket_class.return_value = mock_socket
        
        conn = AdbTcpConnection('*************', 5555)
        
        # Mock successful connection
        mock_socket.connect.return_value = None
        
        with patch.object(conn, '_perform_authentication', return_value=True):
            result = conn.connect()
        
        assert result is True
        assert conn.connected is True
        
        # Verify socket creation and connection
        mock_socket_class.assert_called_with(socket.AF_INET, socket.SOCK_STREAM)
        mock_socket.settimeout.assert_called_with(10.0)
        mock_socket.connect.assert_called_with(('*************', 5555))
        
        packet_logger.debug("IPv4 connection successful")

    @patch('socket.socket')
    def test_connection_ipv4_fallback_to_ipv6(self, mock_socket_class, packet_logger):
        """Test IPv4 failure with IPv6 fallback."""
        packet_logger.debug("Testing IPv4 failure with IPv6 fallback")
        
        # Mock socket instances
        mock_socket_ipv4 = Mock()
        mock_socket_ipv6 = Mock()
        mock_socket_class.side_effect = [mock_socket_ipv4, mock_socket_ipv6]
        
        conn = AdbTcpConnection('::1', 5555)  # IPv6 localhost
        
        # IPv4 fails, IPv6 succeeds
        mock_socket_ipv4.connect.side_effect = socket.error("IPv4 failed")
        mock_socket_ipv6.connect.return_value = None
        
        with patch.object(conn, '_perform_authentication', return_value=True):
            result = conn.connect()
        
        assert result is True
        assert conn.connected is True
        
        # Verify both socket types were tried
        calls = mock_socket_class.call_args_list
        assert len(calls) == 2
        assert calls[0] == call(socket.AF_INET, socket.SOCK_STREAM)
        assert calls[1] == call(socket.AF_INET6, socket.SOCK_STREAM)
        
        packet_logger.debug("IPv6 fallback successful")

    @patch('socket.socket')
    def test_connection_failure_both_protocols(self, mock_socket_class, packet_logger):
        """Test connection failure for both IPv4 and IPv6."""
        packet_logger.debug("Testing connection failure for both protocols")
        
        # Mock socket instances that both fail
        mock_socket_ipv4 = Mock()
        mock_socket_ipv6 = Mock()
        mock_socket_class.side_effect = [mock_socket_ipv4, mock_socket_ipv6]
        
        conn = AdbTcpConnection('unreachable.host', 5555)
        
        # Both connections fail
        mock_socket_ipv4.connect.side_effect = socket.error("IPv4 failed")
        mock_socket_ipv6.connect.side_effect = socket.error("IPv6 failed")
        
        result = conn.connect()
        
        assert result is False
        assert conn.connected is False
        
        packet_logger.debug("Connection failure handled correctly")

    @patch('socket.socket')
    @patch('ssl.create_default_context')
    def test_tls_connection_success(self, mock_ssl_context, mock_socket_class, packet_logger):
        """Test successful TLS connection."""
        packet_logger.debug("Testing successful TLS connection")
        
        # Mock socket and SSL context
        mock_socket = Mock()
        mock_socket_class.return_value = mock_socket
        mock_context = Mock()
        mock_ssl_context.return_value = mock_context
        mock_ssl_socket = Mock()
        mock_context.wrap_socket.return_value = mock_ssl_socket
        
        conn = AdbTcpConnection('*************', 5555, use_tls=True)
        
        # Mock successful connection and TLS handshake
        mock_socket.connect.return_value = None
        
        with patch.object(conn, '_perform_authentication', return_value=True):
            result = conn.connect()
        
        assert result is True
        assert conn.connected is True
        assert conn.ssl_socket == mock_ssl_socket
        
        # Verify TLS setup
        mock_ssl_context.assert_called_once()
        mock_context.wrap_socket.assert_called_once_with(
            mock_socket,
            server_hostname=None,
            do_handshake_on_connect=True
        )
        
        packet_logger.debug("TLS connection successful")

    @patch('socket.socket')
    @patch('ssl.create_default_context')
    def test_tls_handshake_failure(self, mock_ssl_context, mock_socket_class, packet_logger):
        """Test TLS handshake failure."""
        packet_logger.debug("Testing TLS handshake failure")
        
        # Mock socket and SSL context
        mock_socket = Mock()
        mock_socket_class.return_value = mock_socket
        mock_context = Mock()
        mock_ssl_context.return_value = mock_context
        
        conn = AdbTcpConnection('*************', 5555, use_tls=True)
        
        # Mock successful socket connection but TLS handshake failure
        mock_socket.connect.return_value = None
        mock_context.wrap_socket.side_effect = ssl.SSLError("TLS handshake failed")
        
        result = conn.connect()
        
        assert result is False
        assert conn.connected is False
        
        packet_logger.debug("TLS handshake failure handled correctly")

    @patch('socket.socket')
    def test_authentication_failure(self, mock_socket_class, packet_logger):
        """Test authentication failure."""
        packet_logger.debug("Testing authentication failure")
        
        mock_socket = Mock()
        mock_socket_class.return_value = mock_socket
        
        conn = AdbTcpConnection('*************', 5555)
        
        # Mock successful connection but authentication failure
        mock_socket.connect.return_value = None
        
        with patch.object(conn, '_perform_authentication', return_value=False):
            result = conn.connect()
        
        assert result is False
        assert conn.connected is False
        
        packet_logger.debug("Authentication failure handled correctly")

    def test_already_connected(self, packet_logger):
        """Test connecting when already connected."""
        packet_logger.debug("Testing already connected scenario")
        
        conn = AdbTcpConnection()
        conn.connected = True
        
        result = conn.connect()
        
        assert result is True
        
        packet_logger.debug("Already connected handled correctly")

    def test_disconnect(self, packet_logger):
        """Test disconnection."""
        packet_logger.debug("Testing disconnection")
        
        conn = AdbTcpConnection()
        
        # Mock connected state
        mock_socket = Mock()
        mock_ssl_socket = Mock()
        conn.socket = mock_socket
        conn.ssl_socket = mock_ssl_socket
        conn.connected = True
        conn.authenticated = True
        
        conn.disconnect()
        
        assert conn.connected is False
        assert conn.authenticated is False
        assert conn.socket is None
        assert conn.ssl_socket is None
        
        # Verify sockets were closed
        mock_socket.close.assert_called_once()
        mock_ssl_socket.close.assert_called_once()
        
        packet_logger.debug("Disconnection successful")

    def test_send_packet_not_connected(self, packet_logger):
        """Test sending packet when not connected."""
        packet_logger.debug("Testing send packet when not connected")
        
        conn = AdbTcpConnection()
        test_packet = create_packet(A_CNXN, 0x01000001, 1024*1024, b"host::\0")
        
        result = conn.send_packet(test_packet)
        
        assert result is False
        
        packet_logger.debug("Send packet correctly failed when not connected")

    def test_send_packet_success(self, packet_logger):
        """Test successful packet sending."""
        packet_logger.debug("Testing successful packet sending")
        
        conn = AdbTcpConnection()
        
        # Mock connected state
        mock_socket = Mock()
        conn.socket = mock_socket
        conn.connected = True
        
        test_packet = create_packet(A_CNXN, 0x01000001, 1024*1024, b"host::\0")
        packet_data = test_packet.to_bytes()
        
        # Mock successful send
        mock_socket.send.return_value = len(packet_data)
        
        result = conn.send_packet(test_packet)
        
        assert result is True
        mock_socket.send.assert_called_once_with(packet_data)
        
        packet_logger.debug("Packet sent successfully")

    def test_send_packet_partial_send(self, packet_logger):
        """Test packet sending with partial sends."""
        packet_logger.debug("Testing packet sending with partial sends")
        
        conn = AdbTcpConnection()
        
        # Mock connected state
        mock_socket = Mock()
        conn.socket = mock_socket
        conn.connected = True
        
        test_packet = create_packet(A_CNXN, 0x01000001, 1024*1024, b"host::\0")
        packet_data = test_packet.to_bytes()
        
        # Mock partial sends
        mock_socket.send.side_effect = [10, len(packet_data) - 10]
        
        result = conn.send_packet(test_packet)
        
        assert result is True
        assert mock_socket.send.call_count == 2
        
        packet_logger.debug("Partial send handled correctly")

    def test_send_packet_socket_error(self, packet_logger):
        """Test packet sending with socket error."""
        packet_logger.debug("Testing packet sending with socket error")
        
        conn = AdbTcpConnection()
        
        # Mock connected state
        mock_socket = Mock()
        conn.socket = mock_socket
        conn.connected = True
        
        test_packet = create_packet(A_CNXN, 0x01000001, 1024*1024, b"host::\0")
        
        # Mock socket error
        mock_socket.send.side_effect = socket.error("Connection broken")
        
        result = conn.send_packet(test_packet)
        
        assert result is False
        assert conn.connected is False  # Should disconnect on error
        
        packet_logger.debug("Socket error handled correctly")

    def test_receive_packet_not_connected(self, packet_logger):
        """Test receiving packet when not connected."""
        packet_logger.debug("Testing receive packet when not connected")
        
        conn = AdbTcpConnection()
        
        result = conn.receive_packet()
        
        assert result is None
        
        packet_logger.debug("Receive packet correctly failed when not connected")

    def test_receive_packet_success(self, packet_logger):
        """Test successful packet receiving."""
        packet_logger.debug("Testing successful packet receiving")
        
        conn = AdbTcpConnection()
        
        # Mock connected state
        mock_socket = Mock()
        conn.socket = mock_socket
        conn.connected = True
        
        # Create test packet data
        test_packet = create_packet(A_OKAY, 0x100, 0x200, b"test payload")
        packet_data = test_packet.to_bytes()
        
        # Mock receiving header and payload
        header_data = packet_data[:24]  # MESSAGE_SIZE
        payload_data = packet_data[24:]
        
        mock_socket.recv.side_effect = [header_data, payload_data]
        
        result = conn.receive_packet()
        
        assert result is not None
        assert result.message.command == A_OKAY
        assert result.message.arg0 == 0x100
        assert result.message.arg1 == 0x200
        assert result.payload == b"test payload"
        
        packet_logger.debug("Packet received successfully")

    def test_receive_packet_no_payload(self, packet_logger):
        """Test receiving packet with no payload."""
        packet_logger.debug("Testing packet receiving with no payload")
        
        conn = AdbTcpConnection()
        
        # Mock connected state
        mock_socket = Mock()
        conn.socket = mock_socket
        conn.connected = True
        
        # Create test packet with no payload
        test_packet = create_packet(A_OKAY, 0x100, 0x200)
        header_data = test_packet.to_bytes()
        
        mock_socket.recv.return_value = header_data
        
        result = conn.receive_packet()
        
        assert result is not None
        assert result.message.command == A_OKAY
        assert result.payload == b""
        
        packet_logger.debug("Packet with no payload received successfully")

    def test_receive_packet_socket_error(self, packet_logger):
        """Test receiving packet with socket error."""
        packet_logger.debug("Testing packet receiving with socket error")
        
        conn = AdbTcpConnection()
        
        # Mock connected state
        mock_socket = Mock()
        conn.socket = mock_socket
        conn.connected = True
        
        # Mock socket error
        mock_socket.recv.side_effect = socket.error("Connection broken")
        
        result = conn.receive_packet()
        
        assert result is None
        assert conn.connected is False  # Should disconnect on error
        
        packet_logger.debug("Socket error during receive handled correctly")

    def test_context_manager_success(self, packet_logger):
        """Test connection as context manager with success."""
        packet_logger.debug("Testing context manager with success")
        
        with patch.object(AdbTcpConnection, 'connect', return_value=True) as mock_connect, \
             patch.object(AdbTcpConnection, 'disconnect') as mock_disconnect:
            
            with AdbTcpConnection('*************', 5555) as conn:
                assert conn is not None
            
            mock_connect.assert_called_once()
            mock_disconnect.assert_called_once()
        
        packet_logger.debug("Context manager success handled correctly")

    def test_context_manager_connection_failure(self, packet_logger):
        """Test context manager with connection failure."""
        packet_logger.debug("Testing context manager with connection failure")
        
        with patch.object(AdbTcpConnection, 'connect', return_value=False):
            with pytest.raises(ConnectionError, match="Failed to connect"):
                with AdbTcpConnection('unreachable.host', 5555):
                    pass
        
        packet_logger.debug("Context manager connection failure handled correctly")

    def test_utility_methods(self, packet_logger):
        """Test utility methods."""
        packet_logger.debug("Testing utility methods")
        
        conn = AdbTcpConnection('*************', 5555)
        
        # Test is_connected
        assert conn.is_connected() is False
        conn.connected = True
        conn.socket = Mock()
        assert conn.is_connected() is True
        
        # Test get_peer_address
        mock_socket = Mock()
        mock_socket.getpeername.return_value = ('*************', 5555, 0, 0)
        conn.socket = mock_socket
        
        peer_addr = conn.get_peer_address()
        assert peer_addr == ('*************', 5555)
        
        # Test set_timeout
        conn.set_timeout(30.0)
        mock_socket.settimeout.assert_called_with(30.0)
        
        packet_logger.debug("Utility methods work correctly")

    def test_ssl_socket_priority(self, packet_logger):
        """Test that SSL socket takes priority over regular socket."""
        packet_logger.debug("Testing SSL socket priority")
        
        conn = AdbTcpConnection()
        
        mock_socket = Mock()
        mock_ssl_socket = Mock()
        conn.socket = mock_socket
        conn.ssl_socket = mock_ssl_socket
        
        active_socket = conn._get_active_socket()
        assert active_socket == mock_ssl_socket
        
        # Test with only regular socket
        conn.ssl_socket = None
        active_socket = conn._get_active_socket()
        assert active_socket == mock_socket
        
        packet_logger.debug("SSL socket priority works correctly")
