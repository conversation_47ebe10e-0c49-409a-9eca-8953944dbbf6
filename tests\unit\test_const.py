"""
Unit tests for pyadb.const module

Tests the ADB protocol constants, enums, and helper functions
to ensure they match the Android source code reference.
"""

import pytest
import struct
import logging

from pyadb.const import (
    # Protocol commands
    A_SYNC, A_CNXN, A_OPEN, A_OKAY, A_<PERSON><PERSON><PERSON>, A_WRTE, A_AUTH, A_STLS,
    
    # Protocol versions
    A_VERSION, A_VERSION_MIN, A_VERSION_SKIP_CHECKSUM,
    A_STLS_VERSION_MIN, A_STLS_VERSION,
    
    # Version information
    ADB_VERSION_MAJOR, ADB_VERSION_MINOR, ADB_SERVER_VERSION,
    
    # Payload limits
    MAX_PAYLOAD, MAX_PAYLOAD_V1,
    
    # Other constants
    INITIAL_DELAYED_ACK_BYTES, LINUX_MAX_SOCKET_SIZE, TOKEN_SIZE,
    DEFAULT_ADB_LOCAL_TRANSPORT_PORT,
    
    # USB identifiers
    ADB_CLASS, ADB_SUBCLASS, <PERSON><PERSON>_PROTOCOL,
    ADB_DBC_CLASS, ADB_DB<PERSON>_SUBCLASS,
    
    # Message format
    MESSAGE_FORMAT, MESSAGE_SIZE,
    
    # Enums
    TransportType, ConnectionState,
    
    # Helper functions
    command_to_string, connection_state_is_online,
    
    # Feature constants
    FEATURE_SHELL2, FEATURE_CMD, FEATURE_STAT2, FEATURE_LS2,
    DEFAULT_FEATURES, COMMAND_NAMES
)

logger = logging.getLogger(__name__)


class TestProtocolCommands:
    """Test ADB protocol command constants."""

    def test_command_values_match_reference(self, packet_logger):
        """Test that command values match ADB source code."""
        packet_logger.debug("Testing protocol command values against reference")
        
        # These values must match adb.h from Android source
        assert A_SYNC == 0x434e5953  # 'SYNC'
        assert A_CNXN == 0x4e584e43  # 'CNXN'
        assert A_OPEN == 0x4e45504f  # 'OPEN'
        assert A_OKAY == 0x59414b4f  # 'OKAY'
        assert A_CLSE == 0x45534c43  # 'CLSE'
        assert A_WRTE == 0x45545257  # 'WRTE'
        assert A_AUTH == 0x48545541  # 'AUTH'
        assert A_STLS == 0x534C5453  # 'STLS'
        
        packet_logger.debug("All command values match reference implementation")

    def test_command_ascii_representation(self, packet_logger):
        """Test that commands represent correct ASCII strings."""
        packet_logger.debug("Testing command ASCII representations")
        
        # Convert to bytes and check ASCII representation
        assert A_SYNC.to_bytes(4, 'little') == b'SYNC'
        assert A_CNXN.to_bytes(4, 'little') == b'CNXN'
        assert A_OPEN.to_bytes(4, 'little') == b'OPEN'
        assert A_OKAY.to_bytes(4, 'little') == b'OKAY'
        assert A_CLSE.to_bytes(4, 'little') == b'CLSE'
        assert A_WRTE.to_bytes(4, 'little') == b'WRTE'
        assert A_AUTH.to_bytes(4, 'little') == b'AUTH'
        assert A_STLS.to_bytes(4, 'little') == b'STLS'
        
        packet_logger.debug("All command ASCII representations are correct")

    def test_command_names_dictionary(self, packet_logger):
        """Test COMMAND_NAMES dictionary completeness."""
        packet_logger.debug("Testing COMMAND_NAMES dictionary")
        
        expected_commands = {
            A_SYNC: 'SYNC',
            A_CNXN: 'CNXN',
            A_OPEN: 'OPEN',
            A_OKAY: 'OKAY',
            A_CLSE: 'CLSE',
            A_WRTE: 'WRTE',
            A_AUTH: 'AUTH',
            A_STLS: 'STLS',
        }
        
        for cmd, name in expected_commands.items():
            assert cmd in COMMAND_NAMES
            assert COMMAND_NAMES[cmd] == name
            
        packet_logger.debug(f"COMMAND_NAMES contains {len(COMMAND_NAMES)} entries")


class TestProtocolVersions:
    """Test ADB protocol version constants."""

    def test_version_values_match_reference(self, packet_logger):
        """Test that version values match ADB source code."""
        packet_logger.debug("Testing protocol version values against reference")
        
        # These values must match adb.h from Android source
        assert A_VERSION_MIN == 0x01000000
        assert A_VERSION_SKIP_CHECKSUM == 0x01000001
        assert A_VERSION == 0x01000001
        
        # TLS versions
        assert A_STLS_VERSION_MIN == 0x01000000
        assert A_STLS_VERSION == 0x01000000
        
        packet_logger.debug("All version values match reference implementation")

    def test_version_hierarchy(self, packet_logger):
        """Test version hierarchy is logical."""
        packet_logger.debug("Testing version hierarchy")
        
        assert A_VERSION >= A_VERSION_MIN
        assert A_VERSION_SKIP_CHECKSUM >= A_VERSION_MIN
        assert A_STLS_VERSION >= A_STLS_VERSION_MIN
        
        packet_logger.debug("Version hierarchy is correct")

    def test_adb_version_info(self, packet_logger):
        """Test ADB version information constants."""
        packet_logger.debug("Testing ADB version information")
        
        assert isinstance(ADB_VERSION_MAJOR, int)
        assert isinstance(ADB_VERSION_MINOR, int)
        assert isinstance(ADB_SERVER_VERSION, int)
        
        assert ADB_VERSION_MAJOR >= 1
        assert ADB_VERSION_MINOR >= 0
        assert ADB_SERVER_VERSION > 0
        
        packet_logger.debug(f"ADB Version: {ADB_VERSION_MAJOR}.{ADB_VERSION_MINOR}, Server: {ADB_SERVER_VERSION}")


class TestPayloadLimits:
    """Test payload size limit constants."""

    def test_payload_limits_match_reference(self, packet_logger):
        """Test that payload limits match ADB source code."""
        packet_logger.debug("Testing payload limits against reference")
        
        # These values must match the Android source
        assert MAX_PAYLOAD_V1 == 4 * 1024  # 4KB for v1
        assert MAX_PAYLOAD == 1024 * 1024  # 1MB for current version
        
        packet_logger.debug(f"MAX_PAYLOAD_V1: {MAX_PAYLOAD_V1}, MAX_PAYLOAD: {MAX_PAYLOAD}")

    def test_payload_limit_hierarchy(self, packet_logger):
        """Test payload limit hierarchy is logical."""
        packet_logger.debug("Testing payload limit hierarchy")
        
        assert MAX_PAYLOAD > MAX_PAYLOAD_V1
        
        packet_logger.debug("Payload limit hierarchy is correct")


class TestUSBIdentifiers:
    """Test USB class/subclass/protocol identifiers."""

    def test_usb_identifiers_match_reference(self, packet_logger):
        """Test that USB identifiers match ADB source code."""
        packet_logger.debug("Testing USB identifiers against reference")
        
        # These values must match the Android source
        assert ADB_CLASS == 0xff
        assert ADB_SUBCLASS == 0x42
        assert ADB_PROTOCOL == 0x1
        
        # DBC identifiers
        assert ADB_DBC_CLASS == 0xDC
        assert ADB_DBC_SUBCLASS == 0x2
        
        packet_logger.debug("All USB identifiers match reference implementation")


class TestMessageFormat:
    """Test message format constants."""

    def test_message_format_specification(self, packet_logger):
        """Test message format specification."""
        packet_logger.debug("Testing message format specification")
        
        assert MESSAGE_FORMAT == '<6I'  # Little-endian, 6 unsigned 32-bit integers
        assert MESSAGE_SIZE == struct.calcsize(MESSAGE_FORMAT)
        assert MESSAGE_SIZE == 24  # 6 * 4 bytes
        
        packet_logger.debug(f"Message format: {MESSAGE_FORMAT}, size: {MESSAGE_SIZE}")

    def test_message_format_packing(self, packet_logger):
        """Test message format packing/unpacking."""
        packet_logger.debug("Testing message format packing/unpacking")
        
        # Test data
        test_values = (0x12345678, 0x87654321, 0xabcdef00, 100, 0x11223344, 0x55667788)
        
        # Pack and unpack
        packed = struct.pack(MESSAGE_FORMAT, *test_values)
        unpacked = struct.unpack(MESSAGE_FORMAT, packed)
        
        assert len(packed) == MESSAGE_SIZE
        assert unpacked == test_values
        
        packet_logger.debug(f"Packed/unpacked test values successfully")


class TestTransportType:
    """Test TransportType enum."""

    def test_transport_type_values(self, packet_logger):
        """Test TransportType enum values."""
        packet_logger.debug("Testing TransportType enum values")
        
        assert TransportType.USB == 0
        assert TransportType.LOCAL == 1  # TCP/IP
        assert TransportType.ANY == 2
        assert TransportType.HOST == 3
        
        packet_logger.debug("TransportType enum values are correct")

    def test_transport_type_enum_behavior(self, packet_logger):
        """Test TransportType enum behavior."""
        packet_logger.debug("Testing TransportType enum behavior")
        
        # Test enum membership
        assert TransportType.USB in TransportType
        assert TransportType.LOCAL in TransportType
        
        # Test comparison
        assert TransportType.USB < TransportType.LOCAL
        assert TransportType.LOCAL < TransportType.ANY
        
        packet_logger.debug("TransportType enum behavior is correct")


class TestConnectionState:
    """Test ConnectionState enum."""

    def test_connection_state_values(self, packet_logger):
        """Test ConnectionState enum values."""
        packet_logger.debug("Testing ConnectionState enum values")
        
        # Pre-connection states
        assert ConnectionState.ANY == -1
        assert ConnectionState.CONNECTING == 0
        assert ConnectionState.AUTHORIZING == 1
        assert ConnectionState.UNAUTHORIZED == 2
        assert ConnectionState.NO_PERM == 3
        assert ConnectionState.DETACHED == 4
        assert ConnectionState.OFFLINE == 5
        
        # Post-CNXN states
        assert ConnectionState.BOOTLOADER == 6
        assert ConnectionState.DEVICE == 7
        assert ConnectionState.HOST == 8
        assert ConnectionState.RECOVERY == 9
        assert ConnectionState.SIDELOAD == 10
        assert ConnectionState.RESCUE == 11
        
        packet_logger.debug("ConnectionState enum values are correct")

    def test_connection_state_online_check(self, packet_logger):
        """Test connection_state_is_online function."""
        packet_logger.debug("Testing connection_state_is_online function")

        # Online states (according to ADB source code)
        online_states = [
            ConnectionState.BOOTLOADER,
            ConnectionState.DEVICE,
            ConnectionState.HOST,
            ConnectionState.RECOVERY,
            ConnectionState.SIDELOAD,
            ConnectionState.RESCUE
        ]

        # Offline states
        offline_states = [
            ConnectionState.CONNECTING,
            ConnectionState.AUTHORIZING,
            ConnectionState.UNAUTHORIZED,
            ConnectionState.NO_PERM,
            ConnectionState.DETACHED,
            ConnectionState.OFFLINE
        ]
        
        for state in online_states:
            assert connection_state_is_online(state), f"State {state} should be online"
            
        for state in offline_states:
            assert not connection_state_is_online(state), f"State {state} should be offline"
            
        packet_logger.debug("connection_state_is_online function works correctly")


class TestHelperFunctions:
    """Test helper functions."""

    def test_command_to_string_known_commands(self, packet_logger):
        """Test command_to_string for known commands."""
        packet_logger.debug("Testing command_to_string for known commands")
        
        test_cases = [
            (A_SYNC, 'SYNC'),
            (A_CNXN, 'CNXN'),
            (A_OPEN, 'OPEN'),
            (A_OKAY, 'OKAY'),
            (A_CLSE, 'CLSE'),
            (A_WRTE, 'WRTE'),
            (A_AUTH, 'AUTH'),
            (A_STLS, 'STLS'),
        ]
        
        for cmd, expected_name in test_cases:
            result = command_to_string(cmd)
            assert result == expected_name
            packet_logger.debug(f"Command 0x{cmd:08x} -> {result}")

    def test_command_to_string_unknown_command(self, packet_logger):
        """Test command_to_string for unknown commands."""
        packet_logger.debug("Testing command_to_string for unknown commands")
        
        unknown_cmd = 0x12345678
        result = command_to_string(unknown_cmd)
        
        assert "UNKNOWN" in result
        assert "0x12345678" in result
        
        packet_logger.debug(f"Unknown command 0x{unknown_cmd:08x} -> {result}")


class TestFeatureConstants:
    """Test feature constants."""

    def test_feature_constants_exist(self, packet_logger):
        """Test that feature constants are defined."""
        packet_logger.debug("Testing feature constants existence")
        
        # Core features that should be defined
        assert isinstance(FEATURE_SHELL2, str)
        assert isinstance(FEATURE_CMD, str)
        assert isinstance(FEATURE_STAT2, str)
        assert isinstance(FEATURE_LS2, str)
        
        packet_logger.debug("Core feature constants are defined")

    def test_default_features_list(self, packet_logger):
        """Test DEFAULT_FEATURES list."""
        packet_logger.debug("Testing DEFAULT_FEATURES list")
        
        assert isinstance(DEFAULT_FEATURES, list)
        assert len(DEFAULT_FEATURES) > 0
        
        # Should contain core features
        assert FEATURE_SHELL2 in DEFAULT_FEATURES
        assert FEATURE_CMD in DEFAULT_FEATURES
        
        packet_logger.debug(f"DEFAULT_FEATURES contains {len(DEFAULT_FEATURES)} features")


class TestOtherConstants:
    """Test other miscellaneous constants."""

    def test_network_constants(self, packet_logger):
        """Test network-related constants."""
        packet_logger.debug("Testing network constants")
        
        assert INITIAL_DELAYED_ACK_BYTES == 32 * 1024 * 1024  # 32MB
        assert LINUX_MAX_SOCKET_SIZE == 4194304  # 4MB
        assert DEFAULT_ADB_LOCAL_TRANSPORT_PORT == 5555
        
        packet_logger.debug("Network constants are correct")

    def test_security_constants(self, packet_logger):
        """Test security-related constants."""
        packet_logger.debug("Testing security constants")
        
        assert TOKEN_SIZE == 20  # SHA-1 hash size
        
        packet_logger.debug("Security constants are correct")

    def test_constant_types(self, packet_logger):
        """Test that constants have correct types."""
        packet_logger.debug("Testing constant types")
        
        # All command constants should be integers
        commands = [A_SYNC, A_CNXN, A_OPEN, A_OKAY, A_CLSE, A_WRTE, A_AUTH, A_STLS]
        for cmd in commands:
            assert isinstance(cmd, int)
            
        # Version constants should be integers
        versions = [A_VERSION, A_VERSION_MIN, A_VERSION_SKIP_CHECKSUM]
        for version in versions:
            assert isinstance(version, int)
            
        # Size constants should be integers
        sizes = [MAX_PAYLOAD, MAX_PAYLOAD_V1, MESSAGE_SIZE, TOKEN_SIZE]
        for size in sizes:
            assert isinstance(size, int)
            assert size > 0
            
        packet_logger.debug("All constant types are correct")
