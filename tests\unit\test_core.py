"""
Unit tests for pyadb.core module

Tests the core ADB protocol implementation including message handling,
packet creation, checksum calculation, and protocol validation.
"""

import pytest
import struct
import logging
from unittest.mock import patch, MagicMock

from pyadb.core import (
    AdbMessage, AdbPacket, calculate_checksum,
    create_message, create_packet, create_connect_packet,
    create_auth_packet, create_open_packet, create_ready_packet,
    create_write_packet, create_close_packet
)
from pyadb.const import (
    A_SYNC, A_CNXN, A_OPEN, A_OKAY, A_CLSE, A_WRTE, A_AUTH, A_STLS,
    A_VERSION, MESSAGE_FORMAT, MESSAGE_SIZE, MAX_PAYLOAD,
    command_to_string
)

logger = logging.getLogger(__name__)


class TestAdbMessage:
    """Test AdbMessage class functionality."""

    def test_message_creation_valid(self, packet_logger):
        """Test creating valid ADB messages."""
        packet_logger.debug("Testing valid ADB message creation")
        
        # Test basic message creation
        msg = AdbMessage(
            command=A_CNXN,
            arg0=A_VERSION,
            arg1=MAX_PAYLOAD,
            data_length=0,
            data_check=0,
            magic=A_CNXN ^ 0xffffffff
        )
        
        assert msg.command == A_CNXN
        assert msg.arg0 == A_VERSION
        assert msg.arg1 == MAX_PAYLOAD
        assert msg.data_length == 0
        assert msg.data_check == 0
        assert msg.magic == (A_CNXN ^ 0xffffffff)
        
        packet_logger.debug(f"Created message: {msg}")

    def test_message_creation_invalid_magic(self, packet_logger):
        """Test message creation with invalid magic value."""
        packet_logger.debug("Testing invalid magic value handling")
        
        with pytest.raises(ValueError, match="Invalid magic value"):
            AdbMessage(
                command=A_CNXN,
                arg0=A_VERSION,
                arg1=MAX_PAYLOAD,
                data_length=0,
                data_check=0,
                magic=0x12345678  # Invalid magic
            )

    def test_message_creation_oversized_payload(self, packet_logger):
        """Test message creation with oversized payload."""
        packet_logger.debug("Testing oversized payload handling")
        
        with pytest.raises(ValueError, match="Data length .* exceeds maximum"):
            AdbMessage(
                command=A_WRTE,
                arg0=1,
                arg1=2,
                data_length=MAX_PAYLOAD + 1,  # Too large
                data_check=0,
                magic=A_WRTE ^ 0xffffffff
            )

    def test_message_to_bytes(self, packet_logger):
        """Test converting message to bytes."""
        packet_logger.debug("Testing message to bytes conversion")
        
        msg = AdbMessage(
            command=A_OKAY,
            arg0=0x12345678,
            arg1=0x87654321,
            data_length=100,
            data_check=0xabcdef00,
            magic=A_OKAY ^ 0xffffffff
        )
        
        data = msg.to_bytes()
        assert len(data) == MESSAGE_SIZE
        
        # Verify the packed data matches expected format
        unpacked = struct.unpack(MESSAGE_FORMAT, data)
        assert unpacked[0] == A_OKAY
        assert unpacked[1] == 0x12345678
        assert unpacked[2] == 0x87654321
        assert unpacked[3] == 100
        assert unpacked[4] == 0xabcdef00
        assert unpacked[5] == (A_OKAY ^ 0xffffffff)
        
        packet_logger.debug(f"Message bytes: {data.hex()}")

    def test_message_from_bytes_valid(self, packet_logger):
        """Test creating message from valid bytes."""
        packet_logger.debug("Testing message from bytes creation")
        
        # Create test data
        original_data = struct.pack(MESSAGE_FORMAT,
                                  A_CNXN, A_VERSION, MAX_PAYLOAD,
                                  0, 0, A_CNXN ^ 0xffffffff)
        
        msg = AdbMessage.from_bytes(original_data)
        
        assert msg.command == A_CNXN
        assert msg.arg0 == A_VERSION
        assert msg.arg1 == MAX_PAYLOAD
        assert msg.data_length == 0
        assert msg.data_check == 0
        assert msg.magic == (A_CNXN ^ 0xffffffff)
        
        packet_logger.debug(f"Parsed message: {msg}")

    def test_message_from_bytes_insufficient_data(self, packet_logger):
        """Test creating message from insufficient bytes."""
        packet_logger.debug("Testing insufficient data handling")
        
        with pytest.raises(ValueError, match="Insufficient data for message header"):
            AdbMessage.from_bytes(b"short")

    def test_message_string_representation(self, packet_logger):
        """Test message string representation."""
        packet_logger.debug("Testing message string representation")
        
        msg = AdbMessage(
            command=A_OPEN,
            arg0=0x100,
            arg1=0x200,
            data_length=50,
            data_check=0x12345678,
            magic=A_OPEN ^ 0xffffffff
        )
        
        str_repr = str(msg)
        assert "OPEN" in str_repr
        assert "0x00000100" in str_repr
        assert "0x00000200" in str_repr
        assert "data_length=50" in str_repr
        assert "0x12345678" in str_repr
        
        packet_logger.debug(f"Message string: {str_repr}")


class TestAdbPacket:
    """Test AdbPacket class functionality."""

    def test_packet_creation_valid(self, packet_logger):
        """Test creating valid ADB packets."""
        packet_logger.debug("Testing valid ADB packet creation")
        
        payload = b"test payload data"
        checksum = calculate_checksum(payload)
        
        msg = AdbMessage(
            command=A_WRTE,
            arg0=1,
            arg1=2,
            data_length=len(payload),
            data_check=checksum,
            magic=A_WRTE ^ 0xffffffff
        )
        
        packet = AdbPacket(msg, payload)
        
        assert packet.message == msg
        assert packet.payload == payload
        
        packet_logger.debug(f"Created packet: {packet}")

    def test_packet_creation_payload_length_mismatch(self, packet_logger):
        """Test packet creation with payload length mismatch."""
        packet_logger.debug("Testing payload length mismatch handling")
        
        payload = b"test payload"
        
        msg = AdbMessage(
            command=A_WRTE,
            arg0=1,
            arg1=2,
            data_length=100,  # Wrong length
            data_check=0,
            magic=A_WRTE ^ 0xffffffff
        )
        
        with pytest.raises(ValueError, match="Payload length mismatch"):
            AdbPacket(msg, payload)

    def test_packet_creation_checksum_mismatch(self, packet_logger):
        """Test packet creation with checksum mismatch."""
        packet_logger.debug("Testing checksum mismatch handling")
        
        payload = b"test payload"
        
        msg = AdbMessage(
            command=A_WRTE,
            arg0=1,
            arg1=2,
            data_length=len(payload),
            data_check=0x12345678,  # Wrong checksum
            magic=A_WRTE ^ 0xffffffff
        )
        
        with pytest.raises(ValueError, match="Checksum mismatch"):
            AdbPacket(msg, payload)

    def test_packet_to_bytes(self, packet_logger):
        """Test converting packet to bytes."""
        packet_logger.debug("Testing packet to bytes conversion")
        
        payload = b"hello world"
        checksum = calculate_checksum(payload)
        
        msg = AdbMessage(
            command=A_WRTE,
            arg0=0x100,
            arg1=0x200,
            data_length=len(payload),
            data_check=checksum,
            magic=A_WRTE ^ 0xffffffff
        )
        
        packet = AdbPacket(msg, payload)
        data = packet.to_bytes()
        
        # Should be message header + payload
        assert len(data) == MESSAGE_SIZE + len(payload)
        assert data[:MESSAGE_SIZE] == msg.to_bytes()
        assert data[MESSAGE_SIZE:] == payload
        
        packet_logger.debug(f"Packet bytes: {data.hex()}")

    def test_packet_from_bytes_valid(self, packet_logger):
        """Test creating packet from valid bytes."""
        packet_logger.debug("Testing packet from bytes creation")
        
        payload = b"test data for packet"
        checksum = calculate_checksum(payload)
        
        # Create raw packet data
        header_data = struct.pack(MESSAGE_FORMAT,
                                A_WRTE, 0x100, 0x200,
                                len(payload), checksum,
                                A_WRTE ^ 0xffffffff)
        
        packet_data = header_data + payload
        
        packet = AdbPacket.from_bytes(packet_data)
        
        assert packet.message.command == A_WRTE
        assert packet.message.arg0 == 0x100
        assert packet.message.arg1 == 0x200
        assert packet.message.data_length == len(payload)
        assert packet.message.data_check == checksum
        assert packet.payload == payload
        
        packet_logger.debug(f"Parsed packet: {packet}")

    def test_packet_from_bytes_incomplete_payload(self, packet_logger):
        """Test creating packet from bytes with incomplete payload."""
        packet_logger.debug("Testing incomplete payload handling")
        
        # Create header expecting 100 bytes but provide only 50
        header_data = struct.pack(MESSAGE_FORMAT,
                                A_WRTE, 1, 2, 100, 0,
                                A_WRTE ^ 0xffffffff)
        
        incomplete_data = header_data + b"x" * 50  # Only 50 bytes
        
        with pytest.raises(ValueError, match="Incomplete payload"):
            AdbPacket.from_bytes(incomplete_data)


class TestChecksumCalculation:
    """Test checksum calculation functionality."""

    def test_checksum_empty_data(self, packet_logger):
        """Test checksum calculation for empty data."""
        packet_logger.debug("Testing checksum for empty data")
        
        checksum = calculate_checksum(b"")
        assert checksum == 0
        
        packet_logger.debug(f"Empty data checksum: {checksum}")

    def test_checksum_simple_data(self, packet_logger):
        """Test checksum calculation for simple data."""
        packet_logger.debug("Testing checksum for simple data")
        
        data = b"hello"
        checksum = calculate_checksum(data)
        
        # Manual calculation: h=104, e=101, l=108, l=108, o=111
        expected = (104 + 101 + 108 + 108 + 111) & 0xffffffff
        assert checksum == expected
        
        packet_logger.debug(f"Data: {data}, Checksum: 0x{checksum:08x}")

    def test_checksum_large_data(self, packet_logger):
        """Test checksum calculation for large data."""
        packet_logger.debug("Testing checksum for large data")
        
        # Create data that will cause overflow
        data = b"\xff" * 1000
        checksum = calculate_checksum(data)
        
        # Should be properly masked to 32 bits
        expected = (255 * 1000) & 0xffffffff
        assert checksum == expected
        
        packet_logger.debug(f"Large data checksum: 0x{checksum:08x}")

    def test_checksum_binary_data(self, packet_logger):
        """Test checksum calculation for binary data."""
        packet_logger.debug("Testing checksum for binary data")
        
        data = bytes(range(256))  # All possible byte values
        checksum = calculate_checksum(data)
        
        # Sum of 0-255 = 255*256/2 = 32640
        expected = 32640 & 0xffffffff
        assert checksum == expected
        
        packet_logger.debug(f"Binary data checksum: 0x{checksum:08x}")


class TestPacketCreators:
    """Test packet creation helper functions."""

    def test_create_message(self, packet_logger):
        """Test create_message function."""
        packet_logger.debug("Testing create_message function")
        
        data = b"test payload"
        msg = create_message(A_WRTE, 0x100, 0x200, data)
        
        assert msg.command == A_WRTE
        assert msg.arg0 == 0x100
        assert msg.arg1 == 0x200
        assert msg.data_length == len(data)
        assert msg.data_check == calculate_checksum(data)
        assert msg.magic == (A_WRTE ^ 0xffffffff)
        
        packet_logger.debug(f"Created message: {msg}")

    def test_create_packet(self, packet_logger):
        """Test create_packet function."""
        packet_logger.debug("Testing create_packet function")
        
        payload = b"packet payload"
        packet = create_packet(A_OKAY, 0x123, 0x456, payload)
        
        assert packet.message.command == A_OKAY
        assert packet.message.arg0 == 0x123
        assert packet.message.arg1 == 0x456
        assert packet.payload == payload
        
        packet_logger.debug(f"Created packet: {packet}")

    def test_create_connect_packet(self, packet_logger):
        """Test create_connect_packet function."""
        packet_logger.debug("Testing create_connect_packet function")
        
        packet = create_connect_packet(A_VERSION, MAX_PAYLOAD, "host::")
        
        assert packet.message.command == A_CNXN
        assert packet.message.arg0 == A_VERSION
        assert packet.message.arg1 == MAX_PAYLOAD
        assert packet.payload == b"host::\0"
        
        packet_logger.debug(f"Connect packet: {packet}")

    def test_create_auth_packet(self, packet_logger):
        """Test create_auth_packet function."""
        packet_logger.debug("Testing create_auth_packet function")
        
        auth_data = b"authentication token"
        packet = create_auth_packet(1, auth_data)
        
        assert packet.message.command == A_AUTH
        assert packet.message.arg0 == 1
        assert packet.message.arg1 == 0
        assert packet.payload == auth_data
        
        packet_logger.debug(f"Auth packet: {packet}")

    def test_create_open_packet(self, packet_logger):
        """Test create_open_packet function."""
        packet_logger.debug("Testing create_open_packet function")
        
        packet = create_open_packet(0x100, "shell:")
        
        assert packet.message.command == A_OPEN
        assert packet.message.arg0 == 0x100
        assert packet.message.arg1 == 0
        assert packet.payload == b"shell:\0"
        
        packet_logger.debug(f"Open packet: {packet}")

    def test_create_ready_packet(self, packet_logger):
        """Test create_ready_packet function."""
        packet_logger.debug("Testing create_ready_packet function")
        
        packet = create_ready_packet(0x100, 0x200)
        
        assert packet.message.command == A_OKAY
        assert packet.message.arg0 == 0x100
        assert packet.message.arg1 == 0x200
        assert packet.payload == b""
        
        packet_logger.debug(f"Ready packet: {packet}")

    def test_create_write_packet(self, packet_logger):
        """Test create_write_packet function."""
        packet_logger.debug("Testing create_write_packet function")
        
        data = b"write data"
        packet = create_write_packet(0x100, 0x200, data)
        
        assert packet.message.command == A_WRTE
        assert packet.message.arg0 == 0x100
        assert packet.message.arg1 == 0x200
        assert packet.payload == data
        
        packet_logger.debug(f"Write packet: {packet}")

    def test_create_close_packet(self, packet_logger):
        """Test create_close_packet function."""
        packet_logger.debug("Testing create_close_packet function")
        
        packet = create_close_packet(0x100, 0x200)
        
        assert packet.message.command == A_CLSE
        assert packet.message.arg0 == 0x100
        assert packet.message.arg1 == 0x200
        assert packet.payload == b""
        
        packet_logger.debug(f"Close packet: {packet}")
