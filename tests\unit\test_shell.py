"""
Unit tests for pyadb.shell module

Tests the ADB shell service implementation including command execution,
shell protocol handling, and shell_v2 support.
"""

import pytest
import time
import struct
from unittest.mock import Mock, MagicMock, patch, call
import logging

from pyadb.shell import (
    AdbShellService, ShellResult, ShellProtocolPacket, ShellProtocolId
)
from pyadb.core import AdbPacket, AdbMessage, create_packet
from pyadb.const import A_OPEN, A_OKAY, A_WRTE, A_CLSE, FEATURE_SHELL2, FEATURE_CMD

logger = logging.getLogger(__name__)


class TestShellProtocolId:
    """Test ShellProtocolId enum."""

    def test_shell_protocol_id_values(self, packet_logger):
        """Test shell protocol ID values."""
        packet_logger.debug("Testing shell protocol ID values")
        
        # These values must match ADB source code
        assert ShellProtocolId.STDIN == 0
        assert ShellProtocolId.STDOUT == 1
        assert ShellProtocolId.STDERR == 2
        assert ShellProtocolId.EXIT == 3
        
        packet_logger.debug("Shell protocol ID values are correct")

    def test_shell_protocol_id_enum_behavior(self, packet_logger):
        """Test shell protocol ID enum behavior."""
        packet_logger.debug("Testing shell protocol ID enum behavior")
        
        # Test enum membership
        assert ShellProtocolId.STDOUT in ShellProtocolId
        assert ShellProtocolId.STDERR in ShellProtocolId
        
        # Test comparison
        assert ShellProtocolId.STDIN < ShellProtocolId.STDOUT
        assert ShellProtocolId.STDOUT < ShellProtocolId.STDERR
        
        packet_logger.debug("Shell protocol ID enum behavior is correct")


class TestShellResult:
    """Test ShellResult dataclass."""

    def test_shell_result_creation(self, packet_logger):
        """Test shell result creation."""
        packet_logger.debug("Testing shell result creation")
        
        result = ShellResult(
            stdout=b"hello world\n",
            stderr=b"warning: test\n",
            exit_code=0,
            execution_time=1.5
        )
        
        assert result.stdout == b"hello world\n"
        assert result.stderr == b"warning: test\n"
        assert result.exit_code == 0
        assert result.execution_time == 1.5
        
        packet_logger.debug("Shell result created correctly")

    def test_shell_result_equality(self, packet_logger):
        """Test shell result equality."""
        packet_logger.debug("Testing shell result equality")
        
        result1 = ShellResult(b"output", b"error", 0, 1.0)
        result2 = ShellResult(b"output", b"error", 0, 1.0)
        result3 = ShellResult(b"different", b"error", 0, 1.0)
        
        assert result1 == result2
        assert result1 != result3
        
        packet_logger.debug("Shell result equality works correctly")


class TestShellProtocolPacket:
    """Test ShellProtocolPacket class."""

    def test_shell_protocol_packet_creation(self, packet_logger):
        """Test shell protocol packet creation."""
        packet_logger.debug("Testing shell protocol packet creation")
        
        packet = ShellProtocolPacket(ShellProtocolId.STDOUT, b"test data")
        
        assert packet.stream_id == ShellProtocolId.STDOUT
        assert packet.data == b"test data"
        
        packet_logger.debug("Shell protocol packet created correctly")

    def test_shell_protocol_packet_to_bytes(self, packet_logger):
        """Test shell protocol packet serialization."""
        packet_logger.debug("Testing shell protocol packet serialization")
        
        test_data = b"hello world"
        packet = ShellProtocolPacket(ShellProtocolId.STDERR, test_data)
        
        wire_data = packet.to_bytes()
        
        # Should be: [id:1][length:4][data:length]
        expected_header = struct.pack('<BI', ShellProtocolId.STDERR, len(test_data))
        expected_data = expected_header + test_data
        
        assert wire_data == expected_data
        assert len(wire_data) == 1 + 4 + len(test_data)
        
        packet_logger.debug(f"Serialized packet: {wire_data.hex()}")

    def test_shell_protocol_packet_from_bytes(self, packet_logger):
        """Test shell protocol packet deserialization."""
        packet_logger.debug("Testing shell protocol packet deserialization")
        
        # Create test wire data
        test_data = b"test output"
        wire_data = struct.pack('<BI', ShellProtocolId.STDOUT, len(test_data)) + test_data
        
        packet = ShellProtocolPacket.from_bytes(wire_data)
        
        assert packet.stream_id == ShellProtocolId.STDOUT
        assert packet.data == test_data
        
        packet_logger.debug("Shell protocol packet deserialized correctly")

    def test_shell_protocol_packet_empty_data(self, packet_logger):
        """Test shell protocol packet with empty data."""
        packet_logger.debug("Testing shell protocol packet with empty data")
        
        packet = ShellProtocolPacket(ShellProtocolId.EXIT, b"")
        wire_data = packet.to_bytes()
        
        # Should be just header with zero length
        expected = struct.pack('<BI', ShellProtocolId.EXIT, 0)
        assert wire_data == expected
        
        packet_logger.debug("Empty data packet handled correctly")

    def test_shell_protocol_packet_invalid_data(self, packet_logger):
        """Test shell protocol packet with invalid data."""
        packet_logger.debug("Testing shell protocol packet with invalid data")
        
        # Test with insufficient data for header
        with pytest.raises((struct.error, ValueError)):
            ShellProtocolPacket.from_bytes(b"short")
        
        # Test with mismatched length
        invalid_data = struct.pack('<BI', ShellProtocolId.STDOUT, 10) + b"short"
        with pytest.raises(ValueError):
            ShellProtocolPacket.from_bytes(invalid_data)
        
        packet_logger.debug("Invalid data handled correctly")


class TestAdbShellService:
    """Test AdbShellService class functionality."""

    def test_shell_service_initialization(self, packet_logger):
        """Test shell service initialization."""
        packet_logger.debug("Testing shell service initialization")
        
        mock_connection = Mock()
        features = [FEATURE_SHELL2, FEATURE_CMD]
        
        shell_service = AdbShellService(mock_connection, features)
        
        assert shell_service.connection == mock_connection
        assert shell_service.features == features
        assert shell_service.supports_shell2 is True
        assert shell_service.supports_cmd is True
        assert shell_service._next_local_id == 1
        assert len(shell_service._active_shells) == 0
        
        packet_logger.debug("Shell service initialized correctly")

    def test_shell_service_no_features(self, packet_logger):
        """Test shell service without features."""
        packet_logger.debug("Testing shell service without features")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection)
        
        assert shell_service.features == []
        assert shell_service.supports_shell2 is False
        assert shell_service.supports_cmd is False
        
        packet_logger.debug("Shell service without features initialized correctly")

    def test_get_next_local_id(self, packet_logger):
        """Test local ID generation."""
        packet_logger.debug("Testing local ID generation")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection)
        
        # Test ID increment
        id1 = shell_service._get_next_local_id()
        id2 = shell_service._get_next_local_id()
        id3 = shell_service._get_next_local_id()
        
        assert id1 == 1
        assert id2 == 2
        assert id3 == 3
        
        packet_logger.debug("Local ID generation works correctly")

    def test_execute_command_shell2(self, packet_logger):
        """Test command execution with shell_v2."""
        packet_logger.debug("Testing command execution with shell_v2")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection, [FEATURE_SHELL2])
        
        # Mock connection responses
        okay_response = create_packet(A_OKAY, 0x200, 1)  # remote_id=0x200, local_id=1
        
        # Mock shell protocol packets
        stdout_data = b"hello world\n"
        stderr_data = b"warning: test\n"
        exit_data = struct.pack('<I', 0)  # exit code 0
        
        stdout_packet_data = ShellProtocolPacket(ShellProtocolId.STDOUT, stdout_data).to_bytes()
        stderr_packet_data = ShellProtocolPacket(ShellProtocolId.STDERR, stderr_data).to_bytes()
        exit_packet_data = ShellProtocolPacket(ShellProtocolId.EXIT, exit_data).to_bytes()
        
        data_packet1 = create_packet(A_WRTE, 0x200, 1, stdout_packet_data)
        data_packet2 = create_packet(A_WRTE, 0x200, 1, stderr_packet_data)
        exit_packet = create_packet(A_WRTE, 0x200, 1, exit_packet_data)
        close_packet = create_packet(A_CLSE, 0x200, 1)
        
        mock_connection.receive_packet.side_effect = [
            okay_response, data_packet1, data_packet2, exit_packet, close_packet
        ]
        mock_connection.send_packet.return_value = True
        
        # Execute command
        result = shell_service.execute_command("echo hello world")
        
        assert isinstance(result, ShellResult)
        assert result.stdout == stdout_data
        assert result.stderr == stderr_data
        assert result.exit_code == 0
        assert result.execution_time >= 0  # Execution time can be 0 for mocked tests
        
        # Verify packets sent
        assert mock_connection.send_packet.call_count >= 1
        open_call = mock_connection.send_packet.call_args_list[0]
        open_packet = open_call[0][0]
        assert open_packet.message.command == A_OPEN
        assert b"shell,v2,raw:echo hello world" in open_packet.payload
        
        packet_logger.debug("Shell_v2 command execution successful")

    def test_execute_command_legacy(self, packet_logger):
        """Test command execution with legacy shell."""
        packet_logger.debug("Testing command execution with legacy shell")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection)  # No shell_v2 feature
        
        # Mock connection responses
        okay_response = create_packet(A_OKAY, 0x200, 1)
        output_data = b"hello world\n"
        data_packet = create_packet(A_WRTE, 0x200, 1, output_data)
        close_packet = create_packet(A_CLSE, 0x200, 1)
        
        mock_connection.receive_packet.side_effect = [
            okay_response, data_packet, close_packet
        ]
        mock_connection.send_packet.return_value = True
        
        # Execute command
        result = shell_service.execute_command("echo hello world")
        
        assert isinstance(result, ShellResult)
        assert result.stdout == output_data
        assert result.stderr == b""  # Legacy shell doesn't separate stderr
        assert result.exit_code == 0  # Legacy shell doesn't provide exit code
        assert result.execution_time >= 0  # Execution time can be 0 for mocked tests
        
        # Verify open packet
        open_call = mock_connection.send_packet.call_args_list[0]
        open_packet = open_call[0][0]
        assert open_packet.message.command == A_OPEN
        assert b"shell:echo hello world" in open_packet.payload
        
        packet_logger.debug("Legacy shell command execution successful")

    def test_execute_command_timeout(self, packet_logger):
        """Test command execution timeout."""
        packet_logger.debug("Testing command execution timeout")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection, [FEATURE_SHELL2])
        
        # Mock OKAY response but no further packets (timeout scenario)
        okay_response = create_packet(A_OKAY, 0x200, 1)
        mock_connection.receive_packet.side_effect = [okay_response, None, None]
        mock_connection.send_packet.return_value = True
        
        # Execute command with short timeout
        start_time = time.time()
        result = shell_service.execute_command("sleep 10", timeout=0.1)
        execution_time = time.time() - start_time
        
        # Should timeout quickly
        assert execution_time < 1.0
        assert isinstance(result, ShellResult)
        
        packet_logger.debug("Command timeout handled correctly")

    def test_execute_command_connection_failure(self, packet_logger):
        """Test command execution with connection failure."""
        packet_logger.debug("Testing command execution with connection failure")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection)
        
        # Mock connection failure
        mock_connection.send_packet.return_value = False
        
        # Should raise RuntimeError
        with pytest.raises(RuntimeError, match="Failed to send shell open packet"):
            shell_service.execute_command("echo test")
        
        packet_logger.debug("Connection failure handled correctly")

    def test_execute_command_unexpected_response(self, packet_logger):
        """Test command execution with unexpected response."""
        packet_logger.debug("Testing command execution with unexpected response")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection)
        
        # Mock unexpected response (not OKAY)
        error_response = create_packet(A_CLSE, 0x200, 1)
        mock_connection.receive_packet.return_value = error_response
        mock_connection.send_packet.return_value = True
        
        # Should raise RuntimeError
        with pytest.raises(RuntimeError, match="Unexpected response to shell open"):
            shell_service.execute_command("echo test")
        
        packet_logger.debug("Unexpected response handled correctly")

    def test_execute_command_streaming(self, packet_logger):
        """Test streaming command execution."""
        packet_logger.debug("Testing streaming command execution")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection, [FEATURE_SHELL2])
        
        # Mock connection responses
        okay_response = create_packet(A_OKAY, 0x200, 1)
        
        # Mock streaming data
        stdout_chunks = [b"chunk1\n", b"chunk2\n", b"chunk3\n"]
        stderr_chunks = [b"error1\n", b"error2\n"]
        
        packets = [okay_response]
        for chunk in stdout_chunks:
            packet_data = ShellProtocolPacket(ShellProtocolId.STDOUT, chunk).to_bytes()
            packets.append(create_packet(A_WRTE, 0x200, 1, packet_data))
        
        for chunk in stderr_chunks:
            packet_data = ShellProtocolPacket(ShellProtocolId.STDERR, chunk).to_bytes()
            packets.append(create_packet(A_WRTE, 0x200, 1, packet_data))
        
        # Exit packet
        exit_data = struct.pack('<I', 0)
        exit_packet_data = ShellProtocolPacket(ShellProtocolId.EXIT, exit_data).to_bytes()
        packets.append(create_packet(A_WRTE, 0x200, 1, exit_packet_data))
        packets.append(create_packet(A_CLSE, 0x200, 1))
        
        mock_connection.receive_packet.side_effect = packets
        mock_connection.send_packet.return_value = True
        
        # Capture streaming output
        stdout_chunks_received = []
        stderr_chunks_received = []
        
        def stdout_callback(data):
            stdout_chunks_received.append(data)
        
        def stderr_callback(data):
            stderr_chunks_received.append(data)
        
        # Execute streaming command
        exit_code = shell_service.execute_command_streaming(
            "echo test",
            stdout_callback=stdout_callback,
            stderr_callback=stderr_callback
        )
        
        assert exit_code == 0
        assert stdout_chunks_received == stdout_chunks
        assert stderr_chunks_received == stderr_chunks
        
        packet_logger.debug("Streaming command execution successful")

    def test_execute_command_streaming_legacy(self, packet_logger):
        """Test streaming command execution with legacy shell."""
        packet_logger.debug("Testing streaming command execution with legacy shell")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection)  # No shell_v2
        
        # Mock legacy shell response
        okay_response = create_packet(A_OKAY, 0x200, 1)
        output_data = b"combined output\n"
        data_packet = create_packet(A_WRTE, 0x200, 1, output_data)
        close_packet = create_packet(A_CLSE, 0x200, 1)
        
        mock_connection.receive_packet.side_effect = [
            okay_response, data_packet, close_packet
        ]
        mock_connection.send_packet.return_value = True
        
        # Capture output
        stdout_received = []
        
        def stdout_callback(data):
            stdout_received.append(data)
        
        # Execute streaming command
        exit_code = shell_service.execute_command_streaming(
            "echo test",
            stdout_callback=stdout_callback
        )
        
        assert exit_code == 0  # Legacy shell always returns 0
        assert stdout_received == [output_data]
        
        packet_logger.debug("Legacy streaming command execution successful")

    def test_shell_service_concurrent_commands(self, packet_logger):
        """Test concurrent command execution."""
        packet_logger.debug("Testing concurrent command execution")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection, [FEATURE_SHELL2])
        
        # Mock responses for two concurrent commands
        okay1 = create_packet(A_OKAY, 0x200, 1)
        okay2 = create_packet(A_OKAY, 0x300, 2)
        
        # Mock data for both commands
        data1 = ShellProtocolPacket(ShellProtocolId.STDOUT, b"output1\n").to_bytes()
        data2 = ShellProtocolPacket(ShellProtocolId.STDOUT, b"output2\n").to_bytes()
        
        exit_data = struct.pack('<I', 0)
        exit1 = ShellProtocolPacket(ShellProtocolId.EXIT, exit_data).to_bytes()
        exit2 = ShellProtocolPacket(ShellProtocolId.EXIT, exit_data).to_bytes()
        
        # Interleaved responses
        mock_connection.receive_packet.side_effect = [
            okay1, okay2,
            create_packet(A_WRTE, 0x200, 1, data1),
            create_packet(A_WRTE, 0x300, 2, data2),
            create_packet(A_WRTE, 0x200, 1, exit1),
            create_packet(A_WRTE, 0x300, 2, exit2),
            create_packet(A_CLSE, 0x200, 1),
            create_packet(A_CLSE, 0x300, 2)
        ]
        mock_connection.send_packet.return_value = True
        
        # This test would require threading to properly test concurrent execution
        # For now, just verify that local IDs are properly managed
        id1 = shell_service._get_next_local_id()
        id2 = shell_service._get_next_local_id()
        
        assert id1 != id2
        assert id1 == 1
        assert id2 == 2
        
        packet_logger.debug("Concurrent command setup verified")

    def test_shell_service_error_handling(self, packet_logger):
        """Test shell service error handling."""
        packet_logger.debug("Testing shell service error handling")
        
        mock_connection = Mock()
        shell_service = AdbShellService(mock_connection, [FEATURE_SHELL2])
        
        # Test various error conditions
        test_cases = [
            # Connection send failure
            (False, None, "Failed to send shell open packet"),
            # No response
            (True, None, "Unexpected response to shell open"),
            # Wrong response type
            (True, create_packet(A_CLSE, 0, 0), "Unexpected response to shell open"),
        ]
        
        for send_result, response, expected_error in test_cases:
            mock_connection.send_packet.return_value = send_result
            mock_connection.receive_packet.return_value = response
            
            with pytest.raises(RuntimeError, match=expected_error):
                shell_service.execute_command("test command")
        
        packet_logger.debug("Error handling works correctly")
